function formatNumber(value) {
    // 确保value是数字
    let num = parseFloat(value);
    if (isNaN(num)) return value;
    
    // 保留一位小数并进行四舍五入
    return Math.round(num * 10) / 10;
}

// 在表格数据渲染时应用格式化
function updateTable(data) {
    data.forEach(row => {
        if (row.cr) row.cr = formatNumber(row.cr);
        if (row.maj) row.maj = formatNumber(row.maj);
        if (row.min) row.min = formatNumber(row.min);
    });
    // ... 其余表格更新代码 ...
}

function addSearchField() {
    const container = document.getElementById('search-container');
    const newField = document.createElement('div');
    newField.className = 'search-field';
    newField.innerHTML = `
        <select name="search_field" style="width: 120px;">
            <option value="supplier">供应商</option>
            <option value="material_number">物料料号</option>
        </select>
        <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容...">
        <button type="button" onclick="this.parentNode.remove()">×</button>
    `;
    container.appendChild(newField);
}

function performSearch() {
    const form = document.getElementById('searchForm');
    const formData = new FormData(form);
    const searchParams = new URLSearchParams();
    
    for (const [key, value] of formData.entries()) {
        if (value) searchParams.append(key, value);
    }
    
    fetch(`/material_sample_list/search?${searchParams.toString()}`)
        .then(response => response.json())
        .then(data => updateTable(data))
        .catch(error => console.error('搜索错误:', error));
}