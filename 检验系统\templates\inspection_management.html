<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>检验记录管理</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .search-container {
            margin: 20px 0;
            padding: 10px;
            background: #f8f8f8;
        }
        .button-container {
            margin: 10px 0;
            display: flex;
            gap: 10px;
        }
        .button-container button {
            padding: 5px 15px;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .checkbox-column {
            width: 30px;
        }
        
        .number-column {
            width: 50px;
        }
        
        .checkbox-column input[type="checkbox"] {
            cursor: pointer;
        }
        
        /* 调整表格单元格样式 */
        td {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 问题点汇总和尺寸列的特殊样式 */
        td:nth-child(12), td:nth-child(13) {
            min-width: 150px;
        }
    </style>
    <script>
        function toggleDeleteButtons() {
            var th = document.querySelector('#inspection-table th:first-child');
            var tds = document.querySelectorAll('#inspection-table td:first-child');
            if (th.style.display === 'none') {
                th.style.display = 'table-cell';
                tds.forEach(function(td) {
                    td.style.display = 'table-cell';
                });
            } else {
                th.style.display = 'none';
                tds.forEach(function(td) {
                    td.style.display = 'none';
                });
            }
        }

        function confirmDelete(recordId) {
            if (confirm('是否确认删除该检验记录？')) {
                window.location.href = '/delete_inspection_record/' + recordId;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            loadRecords();  // 直接加载记录

            // 监听复选框变化
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('record-checkbox') || e.target.id === 'selectAll') {
                    updateButtonStatus();
                }
            });
        });

        // 搜索功能
        function searchRecords() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.getElementById('recordsBody').getElementsByTagName('tr');

            for (let row of rows) {
                let text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            }
        }

        // 加载记录
        function loadRecords() {
            fetch('/api/inspection_records')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    const tbody = document.getElementById('recordsBody');
                    tbody.innerHTML = '';

                    if (data.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="14" style="text-align: center;">暂无检验记录</td></tr>';
                        return;
                    }

                    data.forEach((record, index) => {
                        // 处理问题点汇总
                        let issues = [];
                        for (let i = 1; i <= 5; i++) {
                            const problem = record[`problem_${i}`];
                            const quantity = record[`problem_${i}_quantity`];
                            const rate = record[`problem_${i}_rate`];
                            if (problem && problem.trim() !== '' && quantity > 0) {
                                issues.push(`${problem} ${quantity} pcs 占${rate}%不良`);  // 移除 toFixed
                            }
                        }
                            const issuesText = issues.length > 0 ? issues.join('; ') : '无';

                        // 处理尺寸结果
                        let defectPositions = [];
                        for (let i = 1; i <= 10; i++) {
                            const result = record[`ctq_${i}_result`];
                            if (result && result.toLowerCase() === 'fail') {
                                defectPositions.push(i);
                            }
                        }
                        const ctqDisplay = defectPositions.length > 0 
                            ? `不合格（位置${defectPositions.join(',')}）` 
                            : '合格';

                            const row = `
                            <tr>
                                <td class="checkbox-column">
                                    <input type="checkbox" class="record-checkbox" value="${record.id}" onchange="updateButtonStatus()">
                                </td>
                                <td class="number-column">${index + 1}</td>
                                <td>${record.material_code || ''}</td>
                                <td>${record.material_name || ''}</td>
                                <td>${record.supplier || ''}</td>
                                <td>${record.inspection_date || ''}</td>
                                <td>${record.OrderID || ''}</td>
                                <td>${record.batch || ''}</td>
                                <td>${record.inspection_result || ''}</td>
                                <td>${record.incoming_quantity || ''}</td>
                                <td>${record.sampling_quantity || ''}</td>
                                <td>${issuesText}</td>
                                <td>${ctqDisplay}</td>
                                <td>${record.overall_defect_rate ? record.overall_defect_rate + '%' : 'N/A'}</td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                    
                    updateButtonStatus();
                })
                .catch(error => {
                    console.error('Error loading records:', error);
                    const tbody = document.getElementById('recordsBody');
                    tbody.innerHTML = '<tr><td colspan="14" style="text-align: center; color: red;">加载数据失败</td></tr>';
                });
        }

        function viewRecord(id) {
            window.location.href = `/inspection_detail/${id}`;
        }

        function editRecord(id) {
            window.location.href = `/edit_inspection/${id}`;
        }

        function deleteRecord(id) {
            if (confirm('确定要删除这条记录吗？')) {
                fetch(`/api/inspection_records/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        loadRecords(); // 重新加载数据
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }

        // 添加新的批量操作相关函数
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const checkboxes = document.getElementsByClassName('record-checkbox');
            
            Array.from(checkboxes).forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            
            updateButtonStatus();
        }

        function updateButtonStatus() {
            const checkboxes = document.getElementsByClassName('record-checkbox');
            const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            
            const viewButton = document.getElementById('viewButton');
            const editButton = document.getElementById('editButton');
            const deleteButton = document.getElementById('deleteButton');
            
            // 只有选中一个时才能查看和编辑
            viewButton.disabled = selectedCount !== 1;
            editButton.disabled = selectedCount !== 1;
            // 选中至少一个时才能删除
            deleteButton.disabled = selectedCount === 0;

            // 更新全选框状态
            const selectAllCheckbox = document.getElementById('selectAll');
            if (checkboxes.length > 0) {
                selectAllCheckbox.checked = selectedCount === checkboxes.length;
                // 添加部分选中状态
                selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < checkboxes.length;
            }
        }

        function viewSelected() {
            const selectedId = getSelectedId();
            if (selectedId) {
                viewRecord(selectedId);
            }
        }

        function editSelected() {
            const selectedId = getSelectedId();
            if (selectedId) {
                editRecord(selectedId);
            }
        }

        function getSelectedId() {
            const checkboxes = document.getElementsByClassName('record-checkbox');
            const selectedCheckbox = Array.from(checkboxes).find(cb => cb.checked);
            return selectedCheckbox ? selectedCheckbox.value : null;
        }

        // 添加 deleteSelected 函数
        function deleteSelected() {
            const checkboxes = document.getElementsByClassName('record-checkbox');
            const selectedIds = Array.from(checkboxes)
                .filter(checkbox => checkbox.checked)
                .map(checkbox => checkbox.value);

            if (selectedIds.length === 0) {
                alert('请选择要删除的记录');
                return;
            }

            if (confirm(`确定要删除选中的 ${selectedIds.length} 条记录吗？`)) {
                Promise.all(selectedIds.map(id => 
                    fetch(`/api/inspection_records/${id}`, { method: 'DELETE' })
                        .then(response => response.json())
                ))
                .then(() => {
                    loadRecords();
                    document.getElementById('selectAll').checked = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败');
                });
            }
        }
    </script>
</head>
<body>
    <h1>检验记录管理</h1>
    <a href="{{ url_for('index') }}">返回首页</a>

    <div class="button-container">
        <button onclick="location.href='/new_inspection'">新增检验记录</button>
        <button id="viewButton" onclick="viewSelected()" disabled>查看</button>
        <button id="editButton" onclick="editSelected()" disabled>编辑</button>
        <button id="deleteButton" onclick="deleteSelected()" disabled>删除</button>
    </div>

    <div class="search-container">
        <input type="text" id="searchInput" placeholder="输入关键字搜索...">
        <button onclick="searchRecords()">搜索</button>
    </div>

    <table id="recordsTable">
        <thead>
            <tr>
                <th class="checkbox-column">
                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                </th>
                <th class="number-column">序号</th>
                <th>物料编码</th>
                <th>物料名称</th>
                <th>供应商</th>
                <th>检验日期</th>
                <th>订单号</th>
                <th>批次号</th>
                <th>检验结果</th>
                <th>来料数量</th>
                <th>抽样数量</th>
                <th>问题点汇总</th>
                <th>尺寸</th>
                <th>总不良率</th>
            </tr>
        </thead>
        <tbody id="recordsBody">
            <!-- 数据将通过 JavaScript 动态加载 -->
        </tbody>
    </table>
</body>
</html>