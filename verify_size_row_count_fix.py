#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证size_row_count修复
"""

def verify_fix():
    """验证修复是否正确"""
    print("=" * 60)
    print("验证size_row_count修复")
    print("=" * 60)
    
    try:
        # 检查load_data_handler.py
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        checks = [
            ('size_row_count变量计算', 'size_row_count = len([row for row in size_dict.values()'),
            ('默认值设置', 'size_row_count = 30'),
            ('模板变量传递', 'size_row_count=size_row_count'),
        ]
        
        passed = 0
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"✅ {check_name}: 正确")
                passed += 1
            else:
                print(f"❌ {check_name}: 缺失")
        
        print(f"\n检查结果: {passed}/{len(checks)} 项通过")
        
        if passed == len(checks):
            print("\n🎉 size_row_count错误已成功修复！")
            print("\n📋 修复内容:")
            print("1. ✅ 添加了size_row_count变量的计算逻辑")
            print("2. ✅ 设置了默认值（30行）")
            print("3. ✅ 将变量正确传递给模板")
            print("\n🚀 现在可以正常访问load-data页面了！")
            return True
        else:
            print("\n⚠️  修复不完整，请检查代码")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_fix()
