from flask import Blueprint, render_template, jsonify
from db_config import get_db_connection
from flask import request
import re
from datetime import datetime

material_sample_list_bp = Blueprint('material_sample_list', __name__, url_prefix='/material_sample_list')

@material_sample_list_bp.route('/')
def material_sample_list():
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # 获取搜索参数
    supplier = request.args.get('supplier')
    material_number = request.args.get('material_number')
    material_name = request.args.get('material_name')
    inspection_date = request.args.get('inspection_date')
    created_at = request.args.get('created_at')
    inspector = request.args.get('inspector')
    final_judgment = request.args.get('final_judgment')
    
    # 构建基础查询
    query = """
        SELECT 
            m.id, m.report_code, m.supplier, m.material_number, m.material_name, 
            DATE_FORMAT(m.inspection_date, '%Y-%m-%d') as inspection_date, 
            DATE_FORMAT(m.created_at, '%Y-%m-%d') as created_at, 
            m.inspector, m.final_judgment, m.opinion,
            (SELECT JSON_ARRAYAGG(JSON_OBJECT('question_number', q.question_number, 'question_text', q.question_text))
             FROM material_sample_questions q 
             WHERE q.form_id = m.id) AS questions
        FROM material_sample_confirmation_form m
    """
    
    # 添加搜索条件
    conditions = []
    if supplier:
        conditions.append(f"supplier LIKE '%{supplier}%'")
    if material_number:
        conditions.append(f"material_number LIKE '%{material_number}%'")
    if material_name:
        conditions.append(f"material_name LIKE '%{material_name}%'")
    if inspection_date:
        conditions.append(f"inspection_date LIKE '%{inspection_date}%'")
    if created_at:
        conditions.append(f"created_at LIKE '%{created_at}%'")
    if inspector:
        conditions.append(f"inspector LIKE '%{inspector}%'")
    if final_judgment:
        conditions.append(f"final_judgment LIKE '%{final_judgment}%'")
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY inspection_date DESC"
    
    cursor.execute(query)
    material_samples = cursor.fetchall()
    
    # 为每个物料样板获取问题点并按序号排序
    for sample in material_samples:
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s 
            ORDER BY question_number
        """, (sample['id'],))
        questions = cursor.fetchall()
        sample['questions'] = questions
    
    cursor.close()
    conn.close()
    return render_template('material_sample_list.html', material_samples=material_samples)

@material_sample_list_bp.route('/search')
def search_material_samples():
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    # 获取所有搜索参数
    search_fields = request.args.getlist('search_field')
    search_values = request.args.getlist('search_value')
    
    if not search_fields or not search_values or len(search_fields) != len(search_values):
        return jsonify({'error': 'Missing search parameters'}), 400
    
    # 定义允许搜索的字段白名单
    allowed_fields = ['supplier', 'material_number', 'material_name', 'inspection_date', 
                     'created_at', 'inspector', 'final_judgment']
        
    # 构建查询条件和参数
    conditions = []
    params = []
    
    for field, value in zip(search_fields, search_values):
        if field not in allowed_fields:
            continue
            
        # 处理日期字段的特殊格式
        if field in ['inspection_date', 'created_at']:
            # 处理'4.7'这样的简写日期格式
            if re.search(r'^\d{1,2}\.\d{1,2}$', value):
                current_year = datetime.now().year
                month, day = value.split('.')
                value = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'4-7'这样的简单日期格式
            elif re.search(r'^\d{1,2}-\d{1,2}$', value):
                current_year = datetime.now().year
                month, day = value.split('-')
                value = f"{current_year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'2025.4.7'这样的完整日期格式
            elif re.search(r'^\d{4}\.\d{1,2}\.\d{1,2}$', value):
                year, month, day = value.split('.')
                value = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            # 处理'2025-4-7'这样的格式
            elif re.search(r'^\d{4}-\d{1,2}-\d{1,2}$', value):
                year, month, day = value.split('-')
                value = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            conditions.append(f"DATE_FORMAT({field}, '%Y-%m-%d') = %s")
            params.append(value)
        else:
            conditions.append(f"{field} LIKE %s")
            params.append(f"%{value}%")
    
    query = """
        SELECT 
            m.id, m.report_code, m.supplier, m.material_number, m.material_name, 
            DATE_FORMAT(m.inspection_date, '%Y-%m-%d') as inspection_date, 
            DATE_FORMAT(m.created_at, '%Y-%m-%d') as created_at, 
            m.inspector, m.final_judgment, m.opinion,
            (SELECT JSON_ARRAYAGG(JSON_OBJECT('question_number', q.question_number, 'question_text', q.question_text))
             FROM material_sample_questions q 
             WHERE q.form_id = m.id) AS questions
        FROM material_sample_confirmation_form m
    """
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    cursor.execute(query, tuple(params))
    results = cursor.fetchall()
    print(f"数据库查询结果: {results}")  # 调试打印
    
    # 为每个结果获取问题点并按序号排序
    for result in results:
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s 
            ORDER BY question_number
        """, (result['id'],))
        questions = cursor.fetchall()
        result['questions'] = questions
        print(f"问题点查询结果: {questions}")  # 调试打印
    
    # 确保返回的是数组格式
    if not isinstance(results, list):
        results = [results]
    
    cursor.close()
    conn.close()
    return jsonify({'data': results})