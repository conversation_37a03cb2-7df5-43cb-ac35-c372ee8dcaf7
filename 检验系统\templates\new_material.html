<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>新物料录入</title>
    <style>
        /* 横向显示供应商列表 */
        #suppliers-container {
            display: flex;
            align-items: center;
        }
        #suppliers-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-right: 10px;
        }
        .supplier-item {
            display: flex;
            align-items: center;
            color: #0804ff;
        }
        .supplier-item button {
            margin-left: 5px;
        }
        #new-suppliers {
            margin-top: 0;
        }
        .new-supplier-input {
            display: none;
            margin-right: 5px;
            min-width: 60px; /* 设置最小宽度 */
        }
    </style>
    <script>
        function validateCTQFields() {
            // 获取所有 CTQ 尺寸测量数据的输入框
            var positions = document.getElementsByName('position[]');
            var base_values = document.getElementsByName('base_value[]');
            var lower_tolerances = document.getElementsByName('lower_tolerance[]');
            var upper_tolerances = document.getElementsByName('upper_tolerance[]');

            for (var i = 0; i < positions.length; i++) {
                var position = positions[i].value.trim();
                var base_value = base_values[i].value.trim();
                var lower_tolerance = lower_tolerances[i].value.trim();
                var upper_tolerance = upper_tolerances[i].value.trim();

                // 检查基准值、下限公差、上限公差是否为有效的数字
                if (base_value && isNaN(parseFloat(base_value))) {
                    alert('基准值必须是有效的数字');
                    return false;
                }
                if (lower_tolerance && isNaN(parseFloat(lower_tolerance))) {
                    alert('下限公差必须是有效的数字');
                    return false;
                }
                if (upper_tolerance && isNaN(parseFloat(upper_tolerance))) {
                    alert('上限公差必须是有效的数字');
                    return false;
                }
            }
            return true;
        }

        function addRow() {
            var table = document.getElementById('ctq-table');
            var rowCount = table.rows.length;
            if (rowCount < 11) { // 最多支持 10 组数据（加上表头共 11 行）
                var newRow = table.insertRow(rowCount);
                var cell1 = newRow.insertCell(0);
                var cell2 = newRow.insertCell(1);
                var cell3 = newRow.insertCell(2);
                var cell4 = newRow.insertCell(3);
                var cell5 = newRow.insertCell(4);

                cell1.innerHTML = rowCount;
                cell2.innerHTML = '<input type="text" name="position[]">';
                cell3.innerHTML = '<input type="text" name="base_value[]">';
                cell4.innerHTML = '<input type="text" name="lower_tolerance[]">';
                cell5.innerHTML = '<input type="text" name="upper_tolerance[]">';
            } else {
                alert("最多支持 10 组数据");
            }
        }

        // 新添加的函数，用于检查物料料号是否存在
        function checkMaterialCode() {
            var materialCode = document.getElementById('material_code').value;
            if (materialCode) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', '/check_material_code?material_code=' + materialCode, true);
                xhr.onreadystatechange = function () {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        var response = JSON.parse(xhr.responseText);
                        if (response.exists) {
                            alert('物料料号已存在，请重新输入！');
                            location.reload()
                        }
                    }
                };
                xhr.send();
            }
        }

        //增加供应商的输入
        function addSupplierField() {
            const container = document.getElementById('supplier-container');
            const newRow = document.createElement('div');
            newRow.className = 'supplier-row';
            newRow.innerHTML = `
                <input type="text" name="supplier[]">
                <button type="button" class="remove-supplier" onclick="this.parentElement.remove()">×</button>
            `;
            container.appendChild(newRow);
        }
        // 动态调整输入框宽度
        function adjustWidth(event) {
            const input = event.target;
            const text = input.value;
            const tempSpan = document.createElement('span');
            tempSpan.style.fontFamily = window.getComputedStyle(input).fontFamily;
            tempSpan.style.fontSize = window.getComputedStyle(input).fontSize;
            tempSpan.style.visibility = 'hidden';
            tempSpan.style.whiteSpace = 'pre';
            document.body.appendChild(tempSpan);
            tempSpan.textContent = text;
            input.style.width = Math.max(60, tempSpan.offsetWidth + 10) + 'px'; // 设置最小宽度为 60px，并增加一些额外的空间
            document.body.removeChild(tempSpan);
        }
    </script>
</head>

<body>
    <h1>新物料录入</h1>
    <form method="post" onsubmit="return validateCTQFields()">
        <label for="material_code">物料料号:</label>
        <!-- 添加 onblur 事件 -->
        <input type="text" id="material_code" name="material_code" required onblur="checkMaterialCode()">
        <br>
        <label for="material_name">物料名称:</label>
        <input type="text" id="material_name" name="material_name" required><br>
        <label for="norm">规格:</label>
        <input type="text" id="norm" name="norm" ><br>
        <label for="material">材质:</label>
        <input type="text" id="material" name="material" required><br>
        <label for="colour">颜色:</label>
        <input type="text" id="colour" name="colour" required><br>
        <label for="version">版本:</label>
        <input type="text" id="version" name="version" value="V1"><br>
        <label for="remark">备注:</label>
        <input type="text" id="remark" name="remark" ><br>
        <!-- 供应商部分修改 -->
        <label>供应商:</label>
        <div id="supplier-container">
            <div class="supplier-row">
                <input type="text" name="supplier[]" style="width: 60px;" oninput="adjustWidth(event)">
                <button type="button" class="add-supplier" onclick="addSupplierField()">+</button>
            </div>
        </div>
        <h2>CTQ 尺寸测量</h2>
        <table id="ctq-table" border="1">
            <tr>
                <th>序号</th>
                <th>位置</th>
                <th>基准值</th>
                <th>下限公差</th>
                <th>上限公差</th>
            </tr>
            <tr>
                <td>1</td>
                <td><input type="text" name="position[]"></td>
                <td><input type="text" name="base_value[]" pattern="^[0-9]+(\.[0-9]+)?$" title="必须是有效的数字"></td>
                <td><input type="text" name="lower_tolerance[]" pattern="^[0-9]+(\.[0-9]+)?$" title="必须是有效的数字"></td>
                <td><input type="text" name="upper_tolerance[]" pattern="^[0-9]+(\.[0-9]+)?$" title="必须是有效的数字"></td>
            </tr>
        </table>
        <button type="button" onclick="addRow()">+</button>
        <input type="submit" value="提交">
    </form>
    <a href="{{ url_for('index') }}">返回首页</a>
</body>


</html>