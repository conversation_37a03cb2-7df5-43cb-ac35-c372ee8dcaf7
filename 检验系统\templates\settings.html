<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>系统设置</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        /* 表头列宽设置 */
        th.col-checkbox { width: 8%; }
        th.col-id { width: 5%; }
        th.col-unique-id { width: 15%; }
        th.col-material-type { width: 12%; }
        th.col-inspection-item { width: 15%; }
        th.col-sampling-level { width: 10%; }
        th.col-cr { width: 8%; }
        th.col-maj { width: 8%; }
        th.col-min { width: 8%; }

        /* 合并后的CSS文件内容 */
        body {
            font-size: 14px; /* 默认字体大小调整为14px */
        }

        h1 {
            font-size: 20px; /* 标题字体大小 */
            margin-top: 2px; /* 标题上方的间距 */
            margin-bottom: 2px; /* 标题下方的间距 */

        }

        h2 {
            font-size: 18px; /* 子标题字体大小 */
        }

        th, td {
            font-size: 16px; /* 表格字体大小 */
            padding: 4px; /* 减小单元格内边距 */
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center; /* 默认居中对齐 */
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
        }
        /* 表头单元格样式 */
        th {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            z-index: 2;
            padding: 4px; /* 减少内边距 */
            text-align: center;
            border: 1px solid #ddd;
            box-shadow: 0 1px 0 0 #ddd; /* 添加底部阴影 */
        }
        /* 排序图标样式 */
        th[data-sort]::after {
            content: '';
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
        }

        th[data-sort].sort-asc::after {
            border-bottom: 5px solid #333;
            border-top: none;
        }

        th[data-sort].sort-desc::after {
            border-top: 5px solid #333;
            border-bottom: none;
        }
        .sticky-col {
            position: sticky;
            right: 0;
            background: white;
            z-index: 1;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }
        input[type="checkbox"] {
            transform: scale(1.5);
        }

        .breadcrumb {
            padding: 5px 0;
            margin-bottom: 5px;
            font-size: 16px;
            z-index: 1; /* 确保其层级低于表格容器 */
        }

        /* 表格容器样式 */
        .table-container {
            height: calc(99.5vh - 200px); /* 减去其他元素的高度 */
            overflow-y: auto;
            border: 1px solid #ddd;
            margin-top: 2px;  /* 减小表格容器的上边距 */
            position: relative;
            padding: 0; /* 移除内边距 */
        }

        /* 表格基础样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        /* 表头样式 */
        thead {
            position: sticky;
            top: 0;
            z-index: 2;
            background-color: #f2f2f2;
        }

        thead tr {
            height: 40px;  /* 减小表格行的高度 */
        }

        /* 表头单元格样式 */
        th {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            z-index: 2;
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            box-shadow: 0 1px 0 0 #ddd; /* 添加底部阴影 */
        }

        /* 表格内容样式 */
        td {
            padding: 4px; /* 减少内边距 */
            text-align: center;
            border: 1px solid #ddd;
            background-color: #fff;
            position: relative;
        }

        /* 确保复选框列宽度固定 */
        th:first-child,
        td:first-child {
            width: 40px;
            min-width: 40px;
        }

        /* 序号列宽度固定 */
        th:nth-child(2),
        td:nth-child(2) {
            width: 60px;
            min-width: 60px;
        }

        /* AQL 相关列的样式 */
        .aql-select,
        .custom-aql {
            width: 100%;
            padding: 3px;
            box-sizing: border-box;
            font-size: 12px; /* 减小字体大小 */
        }

        /* AQL 单元格样式 */
        td:nth-child(10),  /* CR 列 */
        td:nth-child(11),  /* MAJ 列 */
        td:nth-child(12) { /* MIN 列 */
            padding: 4px;
            min-width: 80px;
            white-space: normal;
        }

        /* AQL 下拉框选项样式 */
        .aql-select option {
            font-size: 12px;
            padding: 2px;
        }

        /* 表格内的按钮样式 */
        td button {
            margin: 2px;
            padding: 4px 8px;
        }

        /* 按钮样式 */
        button {
            padding: 4px 8px; /* 减小按钮内边距 */
            margin: 2px; /* 减小按钮之间的外边距 */
            border: none;
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 12px; /* 按钮字体大小 */
        }

        /* 表格内的复选框样式 */
        td input[type="checkbox"] {
            transform: scale(1.5);
            margin: 0 auto;
            display: block;
        }

        /* 确保表格单元格可以容纳输入框 */
        td {
            padding: 4px;
            position: relative;
        }

        /* 自定义 AQL 输入框样式 */
        .custom-aql {
            margin-top: 2px;
        }

        /* 确保输入框宽度为100%，并使用 border-box */
        td input[type="text"],
        td input[type="number"],
        td select {
            width: 100%;
            box-sizing: border-box;
            padding: 4px; /* 根据需要调整 */
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0; /* 移除外边距 */
        }

        /* 添加悬停效果 */
        td input[type="text"]:hover,
        td input[type="number"]:hover,
        td select:hover {
            border-color: #007bff;
        }

        /* 重复提醒增强弹窗样式 */
        .duplicate-details {
            border: 1px solid #eee;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #7ea1c5; /* 更改底色 */
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            width: 300px; /* 设置宽度 */
            max-width: 500px; /* 设置最大宽度 */
        }

        .duplicate-details button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 4px;
            margin-top: 10px;
        }

        .duplicate-details p {
            margin: 5px 0;
            font-size: 0.9em;
            text-align: left; /* 左对齐 */
        }

        /* 面包屑导航样式 */
        .breadcrumb {
            padding: 5px 0;
            margin-bottom: 5px;/* 减小面包屑导航的下边距 */
            font-size: 16px;
        }
        
        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb span {
            color: #6c757d;
            margin: 0 8px;
        }
        
        .breadcrumb .current {
            color: #333;
            font-weight: 500;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: #495057;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 5px;
            transition: all 0.2s;
        }
        select {
            padding: 3px;
            width: 100px;
        }
        .save-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
        }
        .edit-btn, .delete-btn, .cancel-btn {
            background: #008CBA;
            color: white;
            border: none;
            padding: 3px 6px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .editing {
            background-color: #f0f8ff;
        }
        .editing .material-type, .editing .inspection-item {
            border: 1px solid #ccc;
            padding: 2px;
            min-width: 80px;
        }
        .search-container {
            margin: 5px 0;
            padding: 2px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        #searchFields {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .search-field {
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 2px;/* 减小搜索字段的内边距 */
            background: #fff;
            border-radius: 4px;
        }
        
        .search-field select,
        .search-field input {
            padding: 2px 4px; /* 减小搜索框内输入框的内边距 */
            height: 24px; /* 减小输入框的高度 */
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .add-search-btn,
        .remove-btn {
            width: 24px;
            height: 24px;
            margin: 0 2px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        
        .search-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 2px 8px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
            margin-left: 2px;
        }
        th:after {
            content: '↕';
            position: absolute;
            right: 8px;
            opacity: 0.3;
        }
        th.sort-asc:after {
            content: '↑';
            opacity: 1;
        }
        th.sort-desc:after {
            content: '↓';
            opacity: 1;
        }
        td:nth-child(5) {
            text-align: left;
        }
        th:nth-child(4),
        th:nth-child(5) {
            width: 200px;
        }
        td:nth-child(4),
        td:nth-child(5) {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f0f4f8;
        }
        .action-buttons {
            margin: 20px 0;
            padding: 10px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .action-buttons button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            margin-right: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        #addBtn {
            background-color: #28a745;
            color: white;
        }
        #manageBtn {
            background-color: #17a2b8;
            color: white;
        }
        #deleteBtn {
            background-color: #dc3545;
            color: white;
        }
        #saveBtn {
            background-color: #28a745;
            color: white;
        }
        #cancelBtn {
            background-color: #6c757d;
            color: white;
        }
        button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        .tooltip {
            position: absolute;
            background: #333;
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            display: none;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .operation-buttons {
            margin-top: 0px;
            margin-bottom: 2px;
           /* margin: 1px 1px; 减小操作按钮的上下边距 */
            padding: 0;
            background: #fff;
            border-radius: 8px;
            /*box-shadow: 0 2px 4px rgba(0,0,0,0.05);*/
        }
        .operation-buttons button {
            padding: 4px 12px;
            margin-right: 5px;
            border: none;
            border-radius: 4px;
            font-weight: 500;
        }
        #addBtn {
            background-color: #28a745;
            color: white;
        }
        #manageBtn {
            background-color: #17a2b8;
            color: white;
        }
        #deleteBtn {
            background-color: #dc3545;
            color: white;
        }
        #saveBtn, #saveNewBtn {
            background-color: #28a745;
            color: white;
        }
        #cancelBtn {
            background-color: #6c757d;
            color: white;
        }
        button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        /* 搜索框样式 */
        .search-container {
            margin: 5px 0;
            padding: 2px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .search-field {
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 2px;
            background: #fff;
            border-radius: 4px;
        }
        .search-field select,
        .search-field input {
            padding: 2px 4px;
            height: 24px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .add-search-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        .search-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 2px 8px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
        }
        /* 添加移除按钮样式 */
        .remove-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        .remove-btn:hover {
            background: #c82333;
        }
        /* 修改搜索字段容器样式 */
        #search-container {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        /* 修改搜索字段样式 */
        .search-field {
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 2px;
            background: #fff;
            border-radius: 4px;
        }
        /* 基础布局样式 */
        .settings-container {
            display: flex;
            width: 100%;
            height: calc(100vh - 60px); /* 减去标题的高度 */
            padding: 0; /* 移除内边距 */
            margin: 0; /* 移除外边距 */
        }

        /* 左侧导航栏样式 */
        .sidebar {
            width: 150px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            padding: 10px; /* 减少内边距 */
            height: 100%;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px; /* 减小侧边栏链接的内边距 */
            color: #495057;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 5px; /* 减小侧边栏链接之间的下边距 */
            transition: all 0.2s;
        }

        .sidebar a:hover {
            background: #e9ecef;
            color: #212529;
        }

        .sidebar a.active {
            background: #007bff;
            color: white;
        }

        /* 右侧内容区域样式 */
        .content {
            flex: 1;
            padding: 5px 10px; /* 减少上边距 */
            overflow-y: auto;
            background: #fff;
        }

        /* 标题样式 */
        h2 {
            margin: 0 0 10px 0;
            padding: 0;
            font-size: 18px;
        }

        /* 按钮组样式 */
        .button-group {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        /* 第一个搜索字段的特殊样式 */
        .search-field.base-search .button-group {
            display: flex;
            gap: 5px;
        }

        /* 搜索按钮只在第一行显示 */
        .search-btn {
            display: none;
        }
        .base-search .search-btn {
            display: inline-flex;
        }
        th[data-sort] {
            position: relative;
            padding-right: 25px !important;
            cursor: pointer;
        }
        th[data-sort]:after {
            content: '';
            position: absolute;
            right: 8px;
            opacity: 0.6;
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
        }
        
 

        /* 添加激活状态的样式 */
        .sidebar a.active {
            background-color: #007bff !important; /* 蓝色背景 */
            color: white; /* 白色文字 */
        }

        /* 分页控件样式 */
        #pagination {
            margin-bottom: 1px; /* 减小分页控件的下边距 */
            padding-bottom: 1px; /* 如果需要，也可以调整内边距 */
            text-align: left; /* 居右对齐 */
        }

        .folder-browser .drives-list {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .folder-browser .drives-list button {
            min-width: 80px;
        }

        .folder-browser .folders-list {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }

        .folder-browser .folder-item {
            padding: 8px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }

        .folder-browser .folder-item:hover {
            background-color: #f8f9fa;
        }

        .folder-browser .folder-item i {
            margin-right: 10px;
            color: #ffd700;
        }

        .folder-browser .location-content {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }

        .folder-browser .drives-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .folder-browser .drive-item,
        .folder-browser .folder-item {
            padding: 8px 12px;
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            transition: all 0.2s;
        }

        .folder-browser .drive-item:hover,
        .folder-browser .folder-item:hover {
            background-color: #f8f9fa;
            border-color: #0056b3;
        }

        .folder-browser .drive-item i,
        .folder-browser .folder-item i {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .folder-browser .drive-item i {
            color: #28a745;
        }

        .folder-browser .folder-item i.fa-folder {
            color: #ffd700;
        }

        .folder-browser .folder-item i.fa-desktop {
            color: #17a2b8;
        }

        .folder-browser .folder-item i.fa-download {
            color: #28a745;
        }

        .folder-browser .folder-item i.fa-file-image {
            color: #dc3545;
        }

        .folder-browser .folder-item i.fa-folder-open {
            color: #fd7e14;
        }
    </style>
</head>
<body>
    <h1>系统设置</h1>
    <div class="settings-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <a href="#general" id="generalLink">常规</a>
            <a href="#interface" id="interfaceLink">界面</a>
            <a href="#" id="materialConfirmLink">物料样板确认书</a>
            <a href="#" id="aqlSettingsLink">AQL设置</a>
            <a href="{{ url_for('index') }}">退出设置</a>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="content" id="generalLink_contentArea" style="display: none;">
            <h2>常规</h2>
        </div>
        <div class="content" id="materialConfirmArea" style="display: none;">
            <h3>物料样板确认书设置</h3>
            <div class="setting-group">
                <label for="image_base_path">图片存储路径：</label>
                <div class="input-group" style="width: 500px;">
                    <input type="text" id="image_base_path" class="form-control" placeholder="请输入图片存储路径">
                    <div class="input-group-append">
                        <button class="btn btn-secondary" type="button" onclick="browseFolders()">浏览...</button>
                        <button class="btn btn-primary" type="button" onclick="saveImagePath()">保存</button>
                    </div>
                </div>
                <small class="form-text text-muted">设置物料样板确认书的图片存储基础路径，例如：D:\检验系统图片\</small>
            </div>
            
            <div class="setting-group mt-3">
                <label for="enable_image_reordering">问题点图片重排序：</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="enable_image_reordering" id="enable_image_reordering_yes" value="yes">
                    <label class="form-check-label" for="enable_image_reordering_yes">是</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="enable_image_reordering" id="enable_image_reordering_no" value="no" checked>
                    <label class="form-check-label" for="enable_image_reordering_no">否</label>
                </div>
                <button class="btn btn-primary btn-sm ml-2" type="button" onclick="saveImageReordering()">保存</button>
                <small class="form-text text-muted">设置是否启用物料样板确认书修改页问题点图片重排序功能</small>
            </div>
        </div>
        <div class="content" id="contentArea" style="display: none;">
            <h2>AQL设置</h2>
            <form id="searchForm" onsubmit="event.preventDefault(); performSearch();">
                <div id="search-container" class="search-container" style="gap: 0.1px;">
                    <div class="search-field base-search">
                        <select name="search_field" style="width: 100px;">
                            <option value="material_type">物料类型</option>
                            <option value="inspection_item">检验项目</option>
                            <option value="sampling_level">抽样水准</option>
                            <option value="cr">CR</option>
                            <option value="maj">MAJ</option>
                            <option value="min">MIN</option>
                        </select>
                        <input type="text" name="search_value" style="width: 180px;" placeholder="请输入搜索内容...">
                        <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                        <button type="submit" class="search-btn">搜索</button>
                    </div>
                </div>
            </form>
            <div class="operation-buttons">
                <button onclick="showAddRow()" id="addBtn">添加新规范</button>
                <button onclick="startEdit()" id="manageBtn" disabled style="display: none;">管理</button>
                <button onclick="deleteSelected()" id="deleteBtn" disabled style="display: none;">删除</button>
                <button onclick="saveEdit()" id="saveBtn" style="display: none;">保存</button>
                <button onclick="saveNewRow()" id="saveNewBtn" style="display: none;">保存新规范</button>
                <button onclick="cancelEdit()" id="cancelBtn" style="display: none;">取消</button>
            </div>
            <div class="table-container">
            <table id="aqlTable">
                <thead>
                    <tr>
                        <th><input type="checkbox" onchange="toggleAllRows(this)"></th>
                        <th>序号</th>
                        <th data-sort="unique_id">唯一码</th>
                        <th data-sort="material_type">物料类型</th>
                        <th data-sort="inspection_item">检验项目</th>
                        <th data-sort="sampling_level">抽样水准</th>
                        <th data-sort="cr">CR</th>
                        <th data-sort="maj">MAJ</th>
                        <th data-sort="min">MIN</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            </div>
            <div id="pagination"></div>
            <template id="rowTemplate">
                <tr>
                    <td class="actions">
                        <button class="edit-btn">编辑</button>
                        <button class="save-btn" style="display:none;">保存</button>
                        <button class="cancel-btn" style="display:none;">取消</button>
                        <button class="delete-btn">删除</button>
                    </td>
                    <td class="index"></td>
                    <td class="unique-id"></td>
                    <td class="material-type"></td>
                    <td class="inspection-item"></td>
                    <td>
                        <select class="sampling-level">
                            ${samplingLevels.map(level => 
                                `<option value="${level}">${level}</option>`
                            ).join('')}
                        </select>
                    </td>
                    <td>
                        <select class="cr-select">
                            ${generateOptions('cr', '')}
                        </select>
                            <input type="text" class="custom-cr" 
                                   style="display:none; width:80px;" 
                                   placeholder="输入值">
                    </td>
                    <td>
                        <select class="maj-select">
                            ${generateOptions('maj', '')}
                        </select>
                        <input type="text" class="custom-maj" 
                               style="display:none; width:80px;" 
                               placeholder="输入值">
                    </td>
                    <td>
                        <select class="min-select">
                            ${generateOptions('min', '')}
                        </select>
                        <input type="text" class="custom-min" 
                               style="display:none; width:80px;" 
                               placeholder="输入值">
                    </td>
                </tr>
            </template>
        </div>
    </div>

    <!-- 文件夹浏览对话框 -->
    <div class="modal fade" id="folderBrowserModal" tabindex="-1" role="dialog" aria-labelledby="folderBrowserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="folderBrowserModalLabel">选择文件夹</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="folder-browser">
                        <div class="current-path mb-3">
                            <div class="input-group">
                                <input type="text" id="currentPath" class="form-control" readonly>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="navigateToParent()" id="upButton" disabled>
                                        <i class="fas fa-level-up-alt"></i> 上级目录
                                    </button>
                                    <button class="btn btn-outline-secondary" type="button" onclick="navigateToRoot()">
                                        <i class="fas fa-home"></i> 根目录
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="location-content">
                            <div class="drives-list mb-3" id="drivesList">
                                <!-- 驱动器列表将在这里动态生成 -->
                            </div>
                            <div class="folders-list" id="foldersList">
                                <!-- 文件夹列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="selectCurrentFolder()">选择此文件夹</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 抽样水准选项
        const samplingLevels = ['S-1', 'S-2', 'S-3', 'S-4', 'G-I', 'G-II', 'G-III'];
        
        // 修改初始化变量声明
        let currentPage = 1;
        let pageSize = 20;
        let sortField = '';  // 改为 sortField
        let sortOrder = 'ASC';  // 改为大写的 ASC，并设置初始值

        // 加载AQL数据
        let isFetching = false;

        // 修改 loadAQLData 函数
        function loadAQLData() {
            if (isFetching) return;
            isFetching = true;
            
            const params = new URLSearchParams();
            params.append('page', currentPage);
            params.append('page_size', pageSize);
            
            // 添加排序参数
            if (sortField) {
                params.append('sort_field', sortField);
                params.append('sort_order', sortOrder);
            }

            fetch('/api/aql_settings?' + params.toString())
                .then(response => response.json())
                .then(data => {
                    const tbody = document.querySelector('#aqlTable tbody');
                    tbody.innerHTML = '';

                    if (data.settings && Array.isArray(data.settings)) {
                        data.settings.forEach((row, index) => {
                            const tr = document.createElement('tr');
                            tr.dataset.id = row.id;
                            tr.innerHTML = `
                                <td><input type="checkbox" onchange="toggleRowActions(this)"></td>
                                <td>${(currentPage - 1) * pageSize + index + 1}</td>
                                <td>${row.unique_id || ''}</td>
                                <td>${row.material_type || ''}</td>
                                <td>${row.inspection_item || ''}</td>
                                <td>${row.sampling_level || ''}</td>
                                <td>${row.cr || ''}</td>
                                <td>${row.maj || ''}</td>
                                <td>${row.min || ''}</td>
                            `;
                            tbody.appendChild(tr);
                        });

                        if (data.total_pages) {
                            updatePagination(data.total_pages);
                        }
                    }
                    
                    // 重新初始化排序功能和工具提示
                    initTableSort();
                    initTooltips();
                })
                .catch(error => {
                    console.error('加载失败:', error);
                    alert('加载数据失败，请稍后再试');
                })
                .finally(() => {
                    isFetching = false;
                });
        }

        // 添加切换行操作按钮的函数
        function toggleRowActions(checkbox) {
            const manageBtn = document.getElementById('manageBtn');
            const deleteBtn = document.getElementById('deleteBtn');
            const checkedBoxes = document.querySelectorAll('#aqlTable tbody input[type="checkbox"]:checked');
            
            manageBtn.style.display = checkedBoxes.length > 0 ? 'inline' : 'none';
            deleteBtn.style.display = checkedBoxes.length > 0 ? 'inline' : 'none';
            manageBtn.disabled = checkedBoxes.length !== 1;
            deleteBtn.disabled = checkedBoxes.length === 0;
        }

        // 修改分页控件的显示
        function updatePagination(totalPages) {
            const paginationContainer = document.getElementById('pagination');
            if (!paginationContainer) return;

            const paginationHtml = `
                <button onclick="changePage(${currentPage - 1})" 
                        ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
                <span style="margin: 0 10px;">第 ${currentPage} 页，共 ${totalPages} 页</span>
                <button onclick="changePage(${currentPage + 1})"
                        ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>
                <select name="page_size" onchange="updatePageSize(this.value)" style="margin-left: 10px;">
                    <option value="20" ${pageSize === 20 ? 'selected' : ''}>每页20行</option>
                    <option value="50" ${pageSize === 50 ? 'selected' : ''}>每页50行</option>
                    <option value="100" ${pageSize === 100 ? 'selected' : ''}>每页100行</option>
                    <option value="200" ${pageSize === 200 ? 'selected' : ''}>每页200行</option>
                </select>
            `;
            paginationContainer.innerHTML = paginationHtml;
        }

        // 添加页面切换函数
        function changePage(newPage) {
            if (newPage < 1) return;
            currentPage = newPage;
            loadAQLData();
        }

        // 添加更新每页显示数量的函数
        function updatePageSize(size) {
            pageSize = parseInt(size);
            currentPage = 1;
            loadAQLData();
        }

        // 动态显示自定义输入框
        function setupCustomInputs(clone) {
            clone.querySelectorAll('.cr-select, .maj-select, .min-select').forEach(select => {
                select.addEventListener('change', function() {
                    const customInput = this.nextElementSibling;
                    if (this.value === 'custom') {
                        customInput.style.display = 'inline';
                    } else {
                        customInput.style.display = 'none';
                    }
                });

                // 初始化自定义输入框的显示状态
                const customInput = select.nextElementSibling;
                if (select.value === 'custom') {
                    customInput.style.display = 'inline';
                    customInput.value = select.parentElement.querySelector('input[type="text"]').value;
                }
            });
        }

        // 初始化行事件
        function initRowEvents() {
            const table = document.querySelector('#aqlTable');
            // 移除旧的监听器（使用相同的函数引用）
            table.removeEventListener('click', handleRowClick);
            // 添加新的监听器
            table.addEventListener('click', handleRowClick);
        }

        function handleRowClick(e) {
            if (e.target.classList.contains('edit-btn')) enableEdit(e);
            if (e.target.classList.contains('save-btn')) saveRow(e);
            if (e.target.classList.contains('cancel-btn')) cancelEdit(e);
            if (e.target.classList.contains('delete-btn')) deleteRow(e); // 直接调用删除，内部处理确认
        }

        // 启用编辑模式
        function enableEdit(e) {
            isTableEditing = true; // 新增
            const row = e.target.closest('tr');
            if (!row) return; // 如果找不到行，直接返回

            row.classList.add('editing');

            // 获取按钮元素
            const editBtn = row.querySelector('.edit-btn');
            const saveBtn = row.querySelector('.save-btn');
            const cancelBtn = row.querySelector('.cancel-btn');

            if (editBtn && saveBtn && cancelBtn) {
                editBtn.style.display = 'none';
                saveBtn.style.display = 'inline';
                cancelBtn.style.display = 'inline';
            }

            // 使单元格可编辑
            row.querySelectorAll('.material-type, .inspection-item').forEach(cell => {
                cell.contentEditable = true;
            });

            // 将文本内容替换为下拉选择框
            ['cr', 'maj', 'min'].forEach(field => {
                const select = row.querySelector(`.${field}-select`);
                const customInput = select.nextElementSibling;
                const textSpan = select.previousElementSibling;

                if (select && customInput && textSpan) {
                    // 显示选择框，隐藏文本内容
                    select.style.display = 'inline';
                    customInput.style.display = 'none';
                    textSpan.style.display = 'none';

                    // 设置选择框的值
                    if (textSpan.textContent === 'custom') {
                        select.value = 'custom';
                        customInput.style.display = 'inline';
                        customInput.value = textSpan.textContent;
                    } else {
                        select.value = textSpan.textContent;
                    }
                }
            });

            // 将抽样水准的文本内容替换为下拉选择框
            const samplingLevelSelect = row.querySelector('.sampling-level');
            const samplingLevelSpan = samplingLevelSelect.previousElementSibling;
            if (samplingLevelSelect && samplingLevelSpan) {
                samplingLevelSpan.style.display = 'none';
                samplingLevelSelect.style.display = 'inline';
            }
        }

        // 取消编辑
        function cancelEdit(e) {
            isTableEditing = false; // 新增
            const row = e.target.closest('tr');
            if (!row.dataset.id) { // 如果是新增的行
                row.remove(); // 直接删除该行
                return;
            }

            row.classList.remove('editing');
            row.querySelector('.edit-btn').style.display = 'inline';
            row.querySelector('.save-btn').style.display = 'none';
            row.querySelector('.cancel-btn').style.display = 'none';
            
            // 恢复原始数据
            row.querySelectorAll('[data-original]').forEach(cell => {
                cell.textContent = cell.dataset.original;
            });

            // 将下拉选择框替换为文本内容
            ['cr', 'maj', 'min'].forEach(field => {
                const select = row.querySelector(`.${field}-select`);
                const customInput = select.nextElementSibling;
                const textSpan = select.previousElementSibling;

                if (select && customInput && textSpan) {
                    const textContent = textSpan.textContent;

                    // 隐藏选择框，显示文本内容
                    select.style.display = 'none';
                    customInput.style.display = 'none';
                    textSpan.style.display = 'inline';
                    textSpan.textContent = textContent;
                }
            });

            // 将抽样水准的下拉选择框替换为文本内容
            const samplingLevelSelect = row.querySelector('.sampling-level');
            const samplingLevelSpan = samplingLevelSelect.previousElementSibling;
            if (samplingLevelSelect && samplingLevelSpan) {
                samplingLevelSpan.textContent = samplingLevelSpan.textContent; // 保持原始文本内容
                samplingLevelSelect.style.display = 'none';
                samplingLevelSpan.style.display = 'inline';
            }
        }

        // 保存单行
        function saveRow(e) {
            isTableEditing = false; // 新增
            const row = e.target.closest('tr');
            const id = row.dataset.id;
            const crSelect = row.querySelector('.cr-select');
            const crCustom = row.querySelector('.custom-cr');
            const crValue = crSelect.value === 'custom' ? crCustom.value : (crSelect.value || '');

            const majSelect = row.querySelector('.maj-select');
            const majCustom = row.querySelector('.custom-maj');
            const majValue = majSelect.value === 'custom' ? majCustom.value : (majSelect.value || '');

            const minSelect = row.querySelector('.min-select');
            const minCustom = row.querySelector('.custom-min');
            const minValue = minSelect.value === 'custom' ? minCustom.value : (minSelect.value || '');

            const data = {
                material_type: row.querySelector('.material-type').textContent,
                inspection_item: row.querySelector('.inspection-item').textContent,
                sampling_level: row.querySelector('.sampling-level').value,
                cr: crValue || '',  // 确保空值处理
                maj: majValue || '',
                min: minValue || ''
            };

            if (!data.material_type || !data.inspection_item) {
                alert('物料类型和检验项目为必填项！');
                return;
            }

            const method = id ? 'PUT' : 'POST';
            const url = id ? `/api/aql_settings/${id}` : '/api/aql_settings';

            fetch(url, {
                method: method,
                headers: { 'Content-Type': 'application/json' }, // 定义 headers
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    // 更新当前行数据
                    row.dataset.id = result.id || id;
                    row.querySelector('.unique-id').textContent = result.unique_id || '';
                    cancelEdit(e);
                    if (!id) row.remove(); // 清理临时行
                    loadAQLData(); // 重新加载数据
                } else {
                    alert('保存失败: ' + result.message);
                }
            })
            .catch(error => alert('保存失败: ' + error));
        }

        // 删除单行
        function deleteRow(e) {
            e.stopPropagation(); // 阻止事件冒泡
            const row = e.target.closest('tr');
            const id = row.dataset.id;

            if (!confirm('确定要删除该记录吗？')) return; // 仅一次确认

            if (!id) {
                row.remove();
                return;
            }

            const deleteBtn = e.target;
            deleteBtn.disabled = true;

            fetch(`/api/aql_settings/${id}`, { method: 'DELETE' })
                .then(response => response.json())
                .then(result => {
                    if (result.status === 'success') row.remove();
                    else alert('删除失败: ' + result.message);
                })
                .catch(error => alert('删除失败: ' + error.message))
                .finally(() => deleteBtn.disabled = false);
        }

        // 新增项目
        async function showAddRow() {
            try {
                // 先获取唯一ID
                const uniqueId = await generateUniqueId();
                if (!uniqueId) {
                    alert('生成唯一码失败，请重试');
                    return;
                }

                const tbody = document.querySelector('#aqlTable tbody');
                const newRow = document.createElement('tr');
                newRow.dataset.id = 'new';
                
                newRow.innerHTML = `
                    <td><input type="checkbox" disabled></td>
                    <td>${tbody.children.length + 1}</td>
                    <td>${uniqueId}</td>
                    <td><input type="text" class="edit-input" name="material_type"></td>
                    <td><input type="text" class="edit-input" name="inspection_item"></td>
                    <td>
                        <select name="sampling_level">
                            <option value="S-1">S-1</option>
                            <option value="S-2">S-2</option>
                            <option value="S-3">S-3</option>
                            <option value="S-4">S-4</option>
                            <option value="G-I" selected>G-I</option>
                            <option value="G-II">G-II</option>
                            <option value="G-III">G-III</option>
                        </select>
                    </td>
                    <td>
                        <select name="cr" onchange="toggleCustomAQL(this)">
                            <option value="">请选择</option>
                            <option value="0.010">0.010</option>
                            <option value="0.015">0.015</option>
                            <option value="0.025">0.025</option>
                            <option value="0.040">0.040</option>
                            <option value="0.065">0.065</option>
                            <option value="0.10">0.10</option>
                            <option value="0.15">0.15</option>
                            <option value="0.25">0.25</option>
                            <option value="0.40">0.40</option>
                            <option value="0.65">0.65</option>
                            <option value="1.0">1.0</option>
                            <option value="1.5">1.5</option>
                            <option value="2.5">2.5</option>
                            <option value="4.0">4.0</option>
                            <option value="6.5">6.5</option>
                            <option value="10.0">10.0</option>
                            <option value="custom">自定义</option>
                        </select>
                        <input type="number" class="custom-aql" style="display: none" step="0.001" min="0" max="100">
                    </td>
                    <td>
                        <select name="maj" onchange="toggleCustomAQL(this)">
                            <option value="">请选择</option>
                            <option value="0.010">0.010</option>
                            <option value="0.015">0.015</option>
                            <option value="0.025">0.025</option>
                            <option value="0.040">0.040</option>
                            <option value="0.065">0.065</option>
                            <option value="0.10">0.10</option>
                            <option value="0.15">0.15</option>
                            <option value="0.25">0.25</option>
                            <option value="0.40">0.40</option>
                            <option value="0.65">0.65</option>
                            <option value="1.0">1.0</option>
                            <option value="1.5">1.5</option>
                            <option value="2.5">2.5</option>
                            <option value="4.0">4.0</option>
                            <option value="6.5">6.5</option>
                            <option value="10.0">10.0</option>                        
                            <option value="custom">自定义</option>
                        </select>
                        <input type="number" class="custom-aql" style="display: none" step="0.001" min="0" max="100">
                    </td>
                    <td>
                        <select name="min" onchange="toggleCustomAQL(this)">
                            <option value="">请选择</option>
                            <option value="0.010">0.010</option>
                            <option value="0.015">0.015</option>
                            <option value="0.025">0.025</option>
                            <option value="0.040">0.040</option>
                            <option value="0.065">0.065</option>
                            <option value="0.10">0.10</option>
                            <option value="0.15">0.15</option>
                            <option value="0.25">0.25</option>
                            <option value="0.40">0.40</option>
                            <option value="0.65">0.65</option>
                            <option value="1.0">1.0</option>
                            <option value="1.5">1.5</option>
                            <option value="2.5">2.5</option>
                            <option value="4.0">4.0</option>
                            <option value="6.5">6.5</option>
                            <option value="10.0">10.0</option> 
                            <option value="custom">自定义</option>
                        </select>
                        <input type="number" class="custom-aql" style="display: none" step="0.001" min="0" max="100">
                    </td>
                `;
            
            // 将新行插入到表格的第一行
            if (tbody.firstChild) {
                tbody.insertBefore(newRow, tbody.firstChild);
            } else {
                tbody.appendChild(newRow);
            }

            // 滚动到表格容器顶部
            const tableContainer = document.querySelector('.table-container');
            if (tableContainer) {
                tableContainer.scrollTop = 0;
            }

            // 修改按钮显示状态
            document.getElementById('addBtn').style.display = 'none'; // 添加这一行
            document.getElementById('saveNewBtn').style.display = 'inline';
            document.getElementById('saveNewBtn').onclick = saveNewRow;
            document.getElementById('cancelBtn').style.display = 'inline';
            document.getElementById('cancelBtn').onclick = cancelAdd;
            document.getElementById('manageBtn').style.display = 'none';
            document.getElementById('deleteBtn').style.display = 'none';

            // 聚焦到第一个输入框
            const firstInput = newRow.querySelector('input[name="material_type"]');
            if (firstInput) {
                firstInput.focus();
            }

        } catch (error) {
            console.error('显示新行失败:', error);
            alert('显示新行失败: ' + error.message);
        }
    }

    // 添加 toggleCustomAQL 函数
    function toggleCustomAQL(select) {
        const customInput = select.nextElementSibling;
        customInput.style.display = select.value === 'custom' ? 'inline' : 'none';
        if (select.value === 'custom') {
            customInput.value = '';
            customInput.focus();
        }
    }

    // 修改保存新规范的函数
    async function saveNewRow() {
        const newRow = document.querySelector('#aqlTable tbody tr[data-id="new"]');
        if (!newRow) return;

        try {
            const formData = {
                unique_id: newRow.querySelector('td:nth-child(3)').textContent,
                material_type: newRow.querySelector('td:nth-child(4) input').value,
                inspection_item: newRow.querySelector('td:nth-child(5) input').value,
                sampling_level: newRow.querySelector('td:nth-child(6) select').value,
                cr: getAQLValue(newRow, 7),
                maj: getAQLValue(newRow, 8),
                min: getAQLValue(newRow, 9)
            };

            // 验证必填字段
            if (!formData.material_type || !formData.inspection_item) {
                alert('物料类型和检验项目为必填项！');
                return;
            }

            // 验证数值格式
            const numericFields = ['cr', 'maj', 'min'];
            for (const field of numericFields) {
                if (formData[field] && isNaN(parseFloat(formData[field]))) {
                    alert(`${field.toUpperCase()} 必须是有效的数字`);
                    return;
                }
            }

            // 检查重复记录
            const isDuplicate = await checkDuplicate(formData.material_type, formData.inspection_item);
            if (isDuplicate) return;

            const response = await fetch('/api/aql_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            if (data.status === 'success') {
                alert('保存成功');
                await loadAQLData();
                resetButtons();
            } else {
                throw new Error(data.message || '保存失败');
            }
        } catch (error) {
            console.error('Error saving new row:', error);
            alert('保存失败: ' + error.message);
        }
    }

    // 修改生成选项的函数
    function generateOptions(type, currentValue) {
        const options = {
            cr: ['', '0.010', '0.015', '0.025', '0.040', '0.065', '0.10', '0.15', '0.25', '0.40', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0', 'custom'],
            maj: ['', '0.010', '0.015', '0.025', '0.040', '0.065', '0.10', '0.15', '0.25', '0.40', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0', 'custom'],
            min: ['', '0.010', '0.015', '0.025', '0.040', '0.065', '0.10', '0.15', '0.25', '0.40', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0', 'custom']
        };
        
        return options[type].map(option => {
            let displayText = option;
            if (option === '') displayText = '不启用';
            if (option === 'custom') displayText = '自定义';
            
            // 精确匹配字符串值
            const selected = option === currentValue ? 'selected' : '';
            return `<option value="${option}" ${selected}>${displayText}</option>`;
        }).join('');
    }

    // 显示AQL设置内容区域
    document.getElementById('aqlSettingsLink').addEventListener('click', function(event) {
        event.preventDefault(); // 阻止默认的链接跳转行为
        document.getElementById('contentArea').style.display = 'block';
        loadAQLData();
    });

    // 隐藏AQL设置内容区域
    const sidebarLinks = document.querySelectorAll('.sidebar a');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(event) {
            if (event.target.id !== 'aqlSettingsLink') {
                document.getElementById('contentArea').style.display = 'none';
            }
        });
    });

    // 使用 MutationObserver 监听 DOM 变化
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // 在这里处理新增的节点
                initRowEvents(); // 如果需要初始化新行的事件
            }
        });
    });

    // 开始观察特定元素的变化
    observer.observe(document.getElementById('aqlTable'), { childList: true });

    // 初始化加载逻辑
    document.addEventListener('DOMContentLoaded', function() {
        // 获取所有左侧导航栏的链接
        const sidebarLinks = document.querySelectorAll('.sidebar a');

        // 为每个链接添加点击事件监听器
        sidebarLinks.forEach(link => {
            if (link.id === 'aqlSettingsLink') {
                link.addEventListener('click', function(event) {
                    event.preventDefault(); // 仅阻止 AQL 设置链接的默认行为
                    
                    // 移除所有链接的 active 类
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前点击的链接添加 active 类
                    this.classList.add('active');
                    
                    // 显示 AQL 设置内容
                    document.getElementById('contentArea').style.display = 'block';
                    loadAQLData();
                });
            } else if (link.getAttribute('href') !== "{{ url_for('index') }}") {
                link.addEventListener('click', function(event) {
                    event.preventDefault();
                    
                    // 移除所有链接的 active 类
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前点击的链接添加 active 类
                    this.classList.add('active');
                    
                    // 隐藏 AQL 设置内容
                    document.getElementById('contentArea').style.display = 'none';
                });
            }
        });

        initTableSort(); // 初始化排序功能
    });

    function performSearch() {
        const searchFields = document.querySelectorAll('.search-field');
        const searchParams = new URLSearchParams();
        
        searchFields.forEach((field, index) => {
            const searchField = field.querySelector('select').value;
            const searchValue = field.querySelector('input[name="search_value"]').value.trim();
            
            if (searchValue) {
                searchParams.append(`search_field_${index}`, searchField);
                searchParams.append(`search_value_${index}`, searchValue);
            }
        });
        
        // 添加当前页码和排序信息
        searchParams.append('page', currentPage);
        searchParams.append('page_size', pageSize);
        if (sortField) {
            searchParams.append('sort_field', sortField);
            searchParams.append('sort_order', sortOrder);
        }
        
        fetch(`/api/aql_settings?${searchParams.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.settings) {
                    updateTable(data.settings);
                    if (data.total_pages) {
                        updatePagination(data.total_pages);
                    }
                }
            })
            .catch(error => {
                console.error('搜索失败:', error);
                alert('搜索失败，请稍后重试');
            });
    }

    function addSearchField() {
        const searchContainer = document.getElementById('search-container');
        if (!searchContainer) return;
        
        const searchFields = searchContainer.querySelectorAll('.search-field');
        if (searchFields.length >= 5) return; // 限制最多5个搜索字段
        
        const newField = document.createElement('div');
        newField.className = 'search-field';
        
        // 根据当前搜索字段数量决定按钮显示
        const buttons = `
            <div class="button-group">
                <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                <button type="button" class="remove-btn" onclick="removeSearchField(this)">×</button>
            </div>
        `;

        newField.innerHTML = `
            <select name="search_field" style="width: 100px;">
                <option value="material_type">物料类型</option>
                <option value="inspection_item">检验项目</option>
                <option value="sampling_level">抽样水准</option>
                <option value="cr">CR</option>
                <option value="maj">MAJ</option>
                <option value="min">MIN</option>
            </select>
            <input type="text" name="search_value" style="width: 180px;" placeholder="请输入搜索内容...">
            ${buttons}
        `;

        searchContainer.appendChild(newField);

        // 更新所有搜索字段的按钮显示
        updateSearchFieldButtons();
    }

    // 添加更新按钮显示的函数
    function updateSearchFieldButtons() {
        const searchContainer = document.getElementById('search-container');
        const searchFields = searchContainer.querySelectorAll('.search-field');
        
        searchFields.forEach((field, index) => {
            const isFirstField = index === 0;
            const isLastField = index === searchFields.length - 1;
            
            // 移除现有的按钮
            const existingButtons = field.querySelector('.button-group');
            if (existingButtons) {
                existingButtons.remove();
            }
            
            // 移除所有现有按钮
            const existingAddBtn = field.querySelector('.add-search-btn');
            const existingSearchBtn = field.querySelector('.search-btn');
            const existingRemoveBtn = field.querySelector('.remove-btn');
            if (existingAddBtn) existingAddBtn.remove();
            if (existingSearchBtn) existingSearchBtn.remove();
            if (existingRemoveBtn) existingRemoveBtn.remove();
            
            if (isFirstField) {
                // 第一行显示 + 和搜索按钮
                field.insertAdjacentHTML('beforeend', `
                    <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                    <button type="submit" class="search-btn">搜索</button>
                `);
            } else if (isLastField && searchFields.length === 5) {
                // 第五行只显示 - 按钮
                field.insertAdjacentHTML('beforeend', `
                    <button type="button" class="remove-btn" onclick="removeSearchField(this)">×</button>
                `);
            } else {
                // 第二到第四行显示 + 和 - 按钮
                field.insertAdjacentHTML('beforeend', `
                    <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                    <button type="button" class="remove-btn" onclick="removeSearchField(this)">×</button>
                `);
            }
            
            // 如果已经有5个搜索字段，隐藏最后一个字段的添加按钮
            if (searchFields.length >= 5) {
                const lastField = searchFields[searchFields.length - 1];
                const addBtn = lastField.querySelector('.add-search-btn');
                if (addBtn) {
                    addBtn.style.display = 'none';
                }
            }
        });
    }

    function removeSearchField(button) {
        const field = button.closest('.search-field');
        if (field) {
            field.remove();
            
            // 更新所有搜索字段的按钮显示
            updateSearchFieldButtons();
        }
    }

    function updateTable(settings) {
        const tbody = document.querySelector('#aqlTable tbody');
        tbody.innerHTML = '';
        
        settings.forEach((row, index) => {
            const tr = document.createElement('tr');
            tr.dataset.id = row.id;
            tr.innerHTML = `
                <td><input type="checkbox" onchange="toggleRowActions(this)"></td>
                <td>${(currentPage - 1) * pageSize + index + 1}</td>
                <td>${row.unique_id || ''}</td>
                <td>${row.material_type || ''}</td>
                <td>${row.inspection_item || ''}</td>
                <td>${row.sampling_level || ''}</td>
                <td>${row.cr || ''}</td>
                <td>${row.maj || ''}</td>
                <td>${row.min || ''}</td>
            `;
            tbody.appendChild(tr);
        });
    }

    function generateSamplingLevelOptions(currentLevel) {
        const levels = ['S-1', 'S-2', 'S-3', 'S-4', 'G-I', 'G-II', 'G-III'];
        return levels.map(level => 
            `<option value="${level}" ${level === currentLevel ? 'selected' : ''}>${level}</option>`
        ).join('');
    }

    function generateAQLOptions(type, currentValue) {
        const options = [
            { value: '0.010', label: '0.010' },
            { value: '0.015', label: '0.015' },
            { value: '0.025', label: '0.025' },
            { value: '0.040', label: '0.040' },
            { value: '0.065', label: '0.065' },
            { value: '0.10', label: '0.10' },
            { value: '0.15', label: '0.15' },
            { value: '0.25', label: '0.25' },
            { value: '0.40', label: '0.40' },
            { value: '0.65', label: '0.65' },
            { value: '1.0', label: '1.0' },
            { value: '1.5', label: '1.5' },
            { value: '2.5', label: '2.5' },
            { value: '4.0', label: '4.0' },
            { value: '6.5', label: '6.5' },
            { value: '10', label: '10' },
            { value: '15', label: '15' },
            { value: '25', label: '25' },
            { value: '40', label: '40' },
            { value: 'custom', label: '自定义' }
        ];
        
        // 标准化当前值以进行比较
        const normalizedCurrentValue = currentValue ? parseFloat(currentValue).toFixed(3) : '';
        
        let optionsHtml = '<option value="">请选择</option>';
        optionsHtml += options.map(option => {
            const isSelected = option.value === 'custom' ? false : 
                (normalizedCurrentValue === parseFloat(option.value).toFixed(3));
            return `<option value="${option.value}" ${isSelected ? 'selected' : ''}>${option.label}</option>`;
        }).join('');
        
        // 如果当前值不在预设选项中，设置为自定义
        if (currentValue && !options.some(opt => parseFloat(opt.value).toFixed(3) === normalizedCurrentValue)) {
            optionsHtml += `<option value="custom" selected>自定义</option>`;
        }
        
        return optionsHtml;
    }

    function startEdit() {
        isTableEditing = true;
        const checkedRows = document.querySelectorAll('#aqlTable tbody input[type="checkbox"]:checked');
        if (checkedRows.length !== 1) return;

        const row = checkedRows[0].closest('tr');

        // 保存原始数据
        const originalData = {
            material_type: row.querySelector('td:nth-child(4)').textContent.trim(),
            inspection_item: row.querySelector('td:nth-child(5)').textContent.trim(),
            sampling_level: row.querySelector('td:nth-child(6)').textContent.trim(),
            cr: row.querySelector('td:nth-child(7)').textContent.trim(),
            maj: row.querySelector('td:nth-child(8)').textContent.trim(),
            min: row.querySelector('td:nth-child(9)').textContent.trim()
        };

        // 将单元格转换为可编辑状态
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            if (index < 3) return; // 跳过复选框、序号和唯一码列

            switch (index) {
                case 3: // 物料类型
                case 4: // 检验项目
                    cell.innerHTML = `<input type="text" class="edit-input" value="${originalData[index === 3 ? 'material_type' : 'inspection_item']}">`;
                    break;
                case 5: // 抽样水准
                    cell.innerHTML = `<select class="sampling-select">${generateSamplingLevelOptions(originalData.sampling_level)}</select>`;
                    break;
                case 6: // CR
                case 7: // MAJ
                case 8: // MIN
                    const fieldName = ['cr', 'maj', 'min'][index - 6];
                    const value = originalData[fieldName];
                    cell.innerHTML = `
                        <select class="aql-select" name="${fieldName}">
                            ${generateOptions(fieldName, value)}
                        </select>
                        <input type="number" class="custom-aql" style="display: none;" step="0.001" min="0" max="100">
                    `;
                    break;
            }
        });

        setupCustomAQLInputs(row);

        // 更新按钮状态
        document.getElementById('manageBtn').style.display = 'none';
        document.getElementById('deleteBtn').style.display = 'none';
        document.getElementById('addBtn').style.display = 'none'; // 添加这一行
        const saveBtn = document.getElementById('saveBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        saveBtn.style.display = 'inline';
        cancelBtn.style.display = 'inline';

        // 绑定事件
        saveBtn.onclick = () => saveEdit();
        cancelBtn.onclick = () => cancelEdit();

        row.dataset.originalHtml = row.innerHTML;
    }

    function setupCustomAQLInputs(row) {
        row.querySelectorAll('.aql-select').forEach(select => {
            const customInput = select.nextElementSibling;
            select.addEventListener('change', () => {
                customInput.style.display = select.value === 'custom' ? 'inline' : 'none';
                if (select.value === 'custom') {
                    customInput.value = '';
                    customInput.focus();
                }
            });
        });
    }

    // 添加切换行操作按钮的函数
    function toggleAllRows(checkbox) {
        const allCheckboxes = document.querySelectorAll('#aqlTable tbody input[type="checkbox"]');
        allCheckboxes.forEach(cb => cb.checked = checkbox.checked);
        toggleRowActions(checkbox);
    }

    function deleteSelected() {
        const checkedRows = document.querySelectorAll('#aqlTable tbody input[type="checkbox"]:checked');
        if (checkedRows.length === 0) {
            alert('请选择要删除的记录');
            return;
        }
        
        if (!confirm(`确定要删除选中的 ${checkedRows.length} 条记录吗？`)) {
            return;
        }

        const deletePromises = Array.from(checkedRows).map(checkbox => {
            const id = checkbox.closest('tr').dataset.id;
            return fetch(`/api/aql_settings/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`删除失败: ${response.statusText}`);
                }
                return response.json();
            });
        });

        Promise.all(deletePromises)
            .then(() => {
                loadAQLData();
                resetButtons();
            })
            .catch(error => {
                console.error('Error deleting settings:', error);
                alert('删除失败: ' + error.message);
            });
    }

    async function saveEdit(e) {
        if (e) e.preventDefault();
        
        const checkedRow = document.querySelector('tbody input[type="checkbox"]:checked');
        if (!checkedRow) {
            alert('请选择要编辑的行');
            return;
        }

        const row = checkedRow.closest('tr');
        if (!row) return;

        try {
            // 获取表单数据
            const formData = {
                material_type: row.querySelector('td:nth-child(4) input').value.trim(),
                inspection_item: row.querySelector('td:nth-child(5) input').value.trim(),
                sampling_level: row.querySelector('td:nth-child(6) select').value,
                cr: getAQLValue(row, 7),
                maj: getAQLValue(row, 8),
                min: getAQLValue(row, 9)
            };

            // 验证必填字段
            if (!formData.material_type || !formData.inspection_item) {
                alert('物料类型和检验项目为必填项！');
                return;
            }

            // 验证数值格式
            const numericFields = ['cr', 'maj', 'min'];
            for (const field of numericFields) {
                if (formData[field] && isNaN(parseFloat(formData[field]))) {
                    alert(`${field.toUpperCase()} 必须是有效的数字`);
                    return;
                }
            }

            // 检查重复记录
            const isDuplicate = await checkDuplicate(formData.material_type, formData.inspection_item);
            if (isDuplicate) return;

            const id = row.dataset.id;
            const response = await fetch(`/api/aql_settings/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            if (data.status === 'success') {
                alert('保存成功');
                await loadAQLData();
                resetButtons();
                isTableEditing = false;
            } else {
                throw new Error(data.message || '保存失败');
            }
        } catch (error) {
            console.error('保存失败:', error);
            alert(`保存失败: ${error.message}`);
        }
    }

    function getAQLValue(row, columnIndex) {
        const cell = row.querySelector(`td:nth-child(${columnIndex})`);
        const select = cell.querySelector('select');
        if (!select) return null;
        
        if (select.value === 'custom') {
            const customInput = cell.querySelector('input[type="number"]');
            return customInput && customInput.value ? customInput.value : null;
        }
        return select.value === '' ? null : select.value;
    }

    function cancelEdit() {
        isTableEditing = false;
        const editingRow = document.querySelector('#aqlTable tbody tr[data-original-html]');
        if (editingRow) {
            editingRow.innerHTML = editingRow.dataset.originalHtml;
            delete editingRow.dataset.originalHtml;
        }
        
        // 重置按钮状态
        document.getElementById('manageBtn').style.display = 'none';
        document.getElementById('deleteBtn').style.display = 'none';
        document.getElementById('saveBtn').style.display = 'none';
        document.getElementById('cancelBtn').style.display = 'none';
        document.getElementById('addBtn').style.display = 'inline'; // 添加这一行
        
        // 重新加载数据
        loadAQLData();
    }

    // 修改 resetButtons 函数
    function resetButtons() {
        const saveBtn = document.getElementById('saveBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const addBtn = document.getElementById('addBtn');
        const manageBtn = document.getElementById('manageBtn');
        const deleteBtn = document.getElementById('deleteBtn');
        const saveNewBtn = document.getElementById('saveNewBtn');

        // 移除事件监听器
        if (saveBtn) {
            saveBtn.onclick = null;
            saveBtn.style.display = 'none';
        }
        if (cancelBtn) {
            cancelBtn.onclick = null;
            cancelBtn.style.display = 'none';
        }
        if (saveNewBtn) {
            saveNewBtn.style.display = 'none';
        }
        if (addBtn) {
            addBtn.style.display = 'inline'; // 添加这一行
            addBtn.textContent = '添加新规范';
            addBtn.onclick = showAddRow;
        }
        if (manageBtn) {
            manageBtn.style.display = 'none';
            manageBtn.disabled = true;
        }
        if (deleteBtn) {
            deleteBtn.style.display = 'none';
            deleteBtn.disabled = true;
        }
    }

    // 修改 cancelAdd 函数
    async function cancelAdd() {
        isTableEditing = false;
        try {
            await loadAQLData();
        resetButtons();
        } catch (error) {
            console.error('取消添加失败:', error);
            alert('取消添加失败: ' + error.message);
        }
    }

    // 修改生成唯一ID的函数
    async function generateUniqueId() {
        try {
            const response = await fetch('/api/aql_settings/generate_id');
            const data = await response.json();
            return data.unique_id;
        } catch (error) {
            console.error('Error generating unique ID:', error);
            // 如果API调用失败，使用默认生成方式
            const date = new Date();
            const year = date.getFullYear().toString().slice(-2);
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `AQL${year}${month}${day}001`;
        }
    }

    // 修改 initTableSort 函数
    // 初始化表格排序功能
    function initTableSort() {
        const headers = document.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            // 移除现有的事件监听器
            header.replaceWith(header.cloneNode(true));
        });

        // 重新获取更新后的表头元素
        document.querySelectorAll('th[data-sort]').forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                // 检查是否正在编辑
                const isEditing = document.querySelector('.editing') || 
                                document.querySelector('tr[data-id="new"]');
                if (isEditing) return;

                const field = header.dataset.sort;
                
                // 更新排序状态
                if (sortField === field) {
                    // 切换排序顺序
                    sortOrder = sortOrder === 'ASC' ? 'DESC' : 'ASC';
                } else {
                    // 新的排序字段
                    sortField = field;
                    sortOrder = 'ASC';
                }
                
                // 更新所有表头的排序指示器
                document.querySelectorAll('th[data-sort]').forEach(h => {
                    h.classList.remove('sort-asc', 'sort-desc');
                });
                
                // 更新当前表头的排序指示器
                header.classList.add(sortOrder === 'ASC' ? 'sort-asc' : 'sort-desc');
                
                // 重新加载数据
                loadAQLData();
            });
        });
    }

    // 添加悬停提示功能
    let tooltipTimeout;
    
    function initTooltips() {
        const cells = document.querySelectorAll('#aqlTable td:nth-child(4), #aqlTable td:nth-child(5)');
        
        cells.forEach(cell => {
            cell.addEventListener('mouseenter', (e) => {
                if (cell.scrollWidth > cell.offsetWidth) {
                    tooltipTimeout = setTimeout(() => {
                        const tooltip = document.createElement('div');
                        tooltip.className = 'tooltip';
                        tooltip.textContent = cell.textContent;
                        document.body.appendChild(tooltip);
                        
                        const rect = cell.getBoundingClientRect();
                        tooltip.style.left = `${rect.left}px`;
                        tooltip.style.top = `${rect.bottom + 5}px`;
                        tooltip.style.display = 'block';
                    }, 1000);
                }
            });
            
            cell.addEventListener('mouseleave', () => {
                clearTimeout(tooltipTimeout);
                const tooltip = document.querySelector('.tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }

    function showDuplicateTooltip(message) {
        const tooltip = document.createElement('div');
        tooltip.className = 'duplicate-details';
        tooltip.innerHTML = `
            <p>${message.replace(/\n/g, '<br>')}</p>
        `;
        document.body.appendChild(tooltip);

        // 设置样式以显示在页面中央
        tooltip.style.position = 'fixed';
        tooltip.style.top = '50%';
        tooltip.style.left = '50%';
        tooltip.style.transform = 'translate(-50%, -50%)';
        tooltip.style.zIndex = '1000';

        // 添加关闭按钮
        const closeButton = document.createElement('button');
        closeButton.textContent = '关闭';
        closeButton.style.marginTop = '10px';
        closeButton.onclick = () => {
            tooltip.remove();
        };
        tooltip.appendChild(closeButton);

        // 自动关闭提示框（可选）
        setTimeout(() => {
            tooltip.remove();
        }, 5000); // 5秒后自动关闭
    }

    // 添加检查重复记录的函数
    async function checkDuplicate(materialType, inspectionItem) {
        if (!materialType || !inspectionItem) return false;
        
        try {
            const params = new URLSearchParams({
                material_type: materialType,
                inspection_item: inspectionItem
            });
            
            const response = await fetch(`/api/aql_settings/check_duplicate?${params}`);
            const data = await response.json();
            
            if (data.status === 'duplicate') {
                showDuplicateTooltip(`已存在相同的记录：\n物料类型: ${data.duplicate_info.material_type}\n检验项目: ${data.duplicate_info.inspection_item}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('检查重复记录时出错:', error);
            return false;
        }
    }

    // 添加导航栏激活状态控制
    document.addEventListener('DOMContentLoaded', function() {
    // 获取所有左侧导航栏的链接
        const sidebarLinks = document.querySelectorAll('.sidebar a');

        // 为每个链接添加点击事件监听器
        sidebarLinks.forEach(link => {
            if (link.id === 'aqlSettingsLink') {
                link.addEventListener('click', function(event) {
                    event.preventDefault(); // 仅阻止 AQL 设置链接的默认行为
                    
                    // 移除所有链接的 active 类
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前点击的链接添加 active 类
                    this.classList.add('active');
                    
                    // 显示 AQL 设置内容
                    document.getElementById('contentArea').style.display = 'block';
                    loadAQLData();
                });
            } else if (link.getAttribute('href') !== "{{ url_for('index') }}") {
                link.addEventListener('click', function(event) {
                    event.preventDefault();
                    
                    // 移除所有链接的 active 类
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前点击的链接添加 active 类
                    this.classList.add('active');
                    
                    // 隐藏 AQL 设置内容
                    document.getElementById('contentArea').style.display = 'none';
                });
            }
        });

        // 初始化排序功能
        initTableSort();
    });

    //路径配置存储（settings.html的保存功能）：
    function saveImagePath() {
            const path = document.getElementById('image_base_path').value;
            fetch('/save_image_path', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({path: path})
            }).then(response => {
                if(response.ok) alert('路径保存成功');
                else alert('保存失败');
            });
        }

    document.addEventListener('DOMContentLoaded', function() {
        const sidebarLinks = document.querySelectorAll('.sidebar a');
        const contentAreas = {
            'generalLink': document.getElementById('generalLink_contentArea'),
            'materialConfirmLink': document.getElementById('materialConfirmArea'),
            'aqlSettingsLink': document.getElementById('contentArea')
        };

        sidebarLinks.forEach(link => {
            if (link.getAttribute('href') !== "{{ url_for('index') }}") {
                link.addEventListener('click', function(event) {
                    event.preventDefault();
                    
                    // 移除所有链接的 active 类
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前点击的链接添加 active 类
                    this.classList.add('active');
                    
                    // 隐藏所有内容区域
                    Object.values(contentAreas).forEach(area => {
                        if (area) area.style.display = 'none';
                    });
                    
                    // 显示对应的内容区域
                    const areaId = this.id;
                    if (contentAreas[areaId]) {
                        contentAreas[areaId].style.display = 'block';
                        if (areaId === 'aqlSettingsLink') {
                            loadAQLData();
                        }
                    }
                });
            }
        });

        // 初始化排序功能
        initTableSort();
        
        // 默认显示物料样板确认书设置
        document.getElementById('materialConfirmLink').click();
    });

    // 加载图片路径
    function loadImagePath() {
        fetch('/settings/get_image_path')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('image_base_path').value = data.path;
                } else {
                    alert('加载图片路径失败：' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('加载图片路径时出错');
            });
    }

    // 保存图片路径
    function saveImagePath() {
        const path = document.getElementById('image_base_path').value;
        if (!path) {
            alert('请输入图片存储路径');
            return;
        }

        fetch('/settings/save_image_path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ path: path })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('图片路径保存成功');
            } else {
                alert('保存失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('保存图片路径时出错');
        });
    }

    // 页面加载时获取图片路径
    document.addEventListener('DOMContentLoaded', function() {
        loadImagePath();
    });

    let currentBrowsePath = '';

    function browseFolders() {
        loadFolderContents('');  // 从根目录开始
        $('#folderBrowserModal').modal('show');
    }

    function loadFolderContents(path) {
        fetch(`/settings/browse_folder?path=${encodeURIComponent(path)}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    currentBrowsePath = data.current_path;
                    document.getElementById('currentPath').value = data.current_path;
                    document.getElementById('upButton').disabled = !data.parent_path;
                    
                    let contentHtml = '';
                    
                    // 如果是根目录，显示驱动器和特殊文件夹
                    if (data.is_root) {
                        // 驱动器列表
                        const drivesHtml = data.drives.map(drive => 
                            `<div class="drive-item" onclick="loadFolderContents('${drive.path}')">
                                <i class="fas fa-hdd"></i>
                                ${drive.name}
                            </div>`
                        ).join('');
                        
                        // 特殊文件夹
                        const specialFoldersHtml = data.folders.map(folder => {
                            let icon = 'fa-folder';
                            if (folder.name === 'Desktop') icon = 'fa-desktop';
                            else if (folder.name === 'Downloads') icon = 'fa-download';
                            else if (folder.name === 'Pictures') icon = 'fa-file-image';
                            else if (folder.name === 'Documents') icon = 'fa-folder-open';
                            
                            return `<div class="folder-item" onclick="loadFolderContents('${folder.path}')">
                                <i class="fas ${icon}"></i>
                                ${folder.name}
                            </div>`;
                        }).join('');
                        
                        document.getElementById('drivesList').innerHTML = drivesHtml;
                        document.getElementById('foldersList').innerHTML = specialFoldersHtml;
                    } else {
                        // 普通文件夹列表
                        document.getElementById('drivesList').innerHTML = '';
                        const foldersHtml = data.folders.map(folder =>
                            `<div class="folder-item" onclick="loadFolderContents('${folder.path}')">
                                <i class="fas fa-folder"></i>
                                ${folder.name}
                            </div>`
                        ).join('');
                        document.getElementById('foldersList').innerHTML = foldersHtml;
                    }
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('加载文件夹内容时出错', 'error');
            });
    }

    function navigateToParent() {
        if (currentBrowsePath) {
            fetch(`/settings/browse_folder?path=${encodeURIComponent(currentBrowsePath)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.parent_path) {
                        loadFolderContents(data.parent_path);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('导航到上级目录时出错', 'error');
                });
        }
    }

    function navigateToRoot() {
        loadFolderContents('');
    }

    function selectCurrentFolder() {
        if (currentBrowsePath) {
            document.getElementById('image_base_path').value = currentBrowsePath;
            $('#folderBrowserModal').modal('hide');
        } else {
            showMessage('请选择一个文件夹', 'warning');
        }
    }

    // 初始化配置
    function initConfig() {
        fetch('/settings/init_config')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('image_base_path').value = data.config.image_base_path;
                    currentBrowsePath = data.config.image_base_path;
                    
                    // 设置图片重排序选项
                    if (data.config.enable_image_reordering === 'yes') {
                        document.getElementById('enable_image_reordering_yes').checked = true;
                    } else {
                        document.getElementById('enable_image_reordering_no').checked = true;
                    }
                } else {
                    showMessage('初始化配置失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('初始化配置时出错', 'error');
            });
    }

    // 添加保存图片重排序设置的函数
    function saveImageReordering() {
        const enableImageReordering = document.querySelector('input[name="enable_image_reordering"]:checked').value;
        
        fetch('/settings/save_image_reordering', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ enable_image_reordering: enableImageReordering })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('问题点图片重排序设置已保存');
            } else {
                alert('保存失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('保存问题点图片重排序设置时出错');
        });
    }

    // 页面加载时初始化配置
    document.addEventListener('DOMContentLoaded', function() {
        initConfig();
    });
</script>
</body>
</html>