#ctq-table {
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed; /* 固定表格布局 */
}

#ctq-table th, #ctq-table td {
    border: 1px solid #000; /* 确保边框宽度合理 */
    padding: 5px; /* 合理的内边距 */
    text-align: center;
    box-sizing: border-box; /* 确保宽度计算包括边框和填充 */
}

/* input:not([type="hidden"]) {
    border: 1px solid #ccc;
    padding: 5px;
    box-sizing: border-box;
}*/


/* 隐藏隐藏的 input 元素 */
#ctq-table input[type="hidden"] {
    display: none !important; /* 使用 !important 确保规则生效 */
}

