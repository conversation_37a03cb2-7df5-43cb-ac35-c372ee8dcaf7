
# 新增：构建问题点编号到图片路径的映射
image_path_map = {}
if confirm_images_response.get('success') and 'final_paths' in confirm_images_response:
    for path in confirm_images_response['final_paths']:
        filename = os.path.basename(path)
        if filename.startswith('question'):
            # 提取问题点编号
            parts = filename.split('_', 1)
            if len(parts) > 1 and parts[0].startswith('question') and parts[0][8:].isdigit():
                q_num = int(parts[0][8:])
                image_path_map[q_num] = path
                print(f"映射问题点 {q_num} 到图片路径: {path}")  # 增加日志

# 原循环处理逻辑修改如下
for i in range(1, 19):  # 修改范围从1-7到1-19，支持最多18个问题点
    question_text = data.get(f'question_{i}')
    # 使用问题点编号直接匹配图片路径
    image_path = image_path_map.get(i)
    
    # 当没有图片但有文本时，设置image_path为'无图片'
    if not image_path and question_text and question_text.strip() != '':
        image_path = '无图片'
        print(f"问题点 {i} 没有图片但有文本，设置image_path为'无图片'")  # 增加日志
    
    # 当存在图片但文本框为空时，自动补上'/'符号
    if image_path and image_path != '无图片' and (not question_text or question_text.strip() == ''):
        question_text = '/'
        print(f"问题点{i}存在图片但无文本，已自动补上'/'符号")  # 增加日志
    
    if question_text:  # 只有当有问题内容时才插入
        print(f"插入问题记录 {i}: {question_text}, image_path: {image_path}")  # 增加日志
        question_query = "INSERT INTO material_sample_questions (form_id, question_number, question_text, image_path) VALUES (%s, %s, %s, %s)"
        cursor.execute(question_query, [form_id, i, question_text, image_path])
