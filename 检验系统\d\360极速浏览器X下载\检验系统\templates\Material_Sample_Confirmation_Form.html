<!-- ... existing code ... -->
<style>
    /* 确保输入框高度与单元格一致 */
    td input[type="text"], td textarea {
        height: 100%;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        vertical-align: middle;
        border: none; /* 移除边框 */
        outline: none; /* 移除轮廓线 */
    }

    /* 调整单元格垂直对齐方式 */
    .custom-row-height td {
        vertical-align: middle;
        padding: 0; /* 移除单元格内边距 */
    }
</style>

<!-- ... existing code ... -->

<tr class="custom-row-height">
    <td class="align-center">1</td>
    <td data-id="size-1" data-image="true"><input type="text" name="size_1_position" id="size-1"></td>
    <td><input type="text" name="size_1_value" id="size-1-value"></td>
    <td><input type="text" name="size_1_min" id="size-1-min"></td>
    <td><input type="text" name="size_1_max" id="size-1-max"></td>
    <td><input type="text" name="size_1_measure_1" id="size-1-measure-1"></td>
    <td><input type="text" name="size_1_measure_2" id="size-1-measure-2"></td>
    <td><input type="text" name="size_1_measure_3" id="size-1-measure-3"></td>
    <td><input type="text" name="size_1_measure_4" id="size-1-measure-4"></td>
    <td><input type="text" name="size_1_measure_5" id="size-1-measure-5"></td>
    <td>
        <label><input type="radio" name="size_1_check" value="合格"> 合格</label>
        <label><input type="radio" name="size_1_check" value="不合格"> 不合格</label>
        <label><input type="radio" name="size_1_check" value="AOD"> AOD</label>
    </td>
    <td colspan="2"><textarea name="size_1_note" id="size-1-note"></textarea></td>
</tr>

<tr class="custom-row-height">
    <td class="align-center">2</td>
    <td data-id="size-2" data-image="true"><input type="text" name="size_2_position" id="size-2"></td>
    <td><input type="text" name="size_2_value" id="size-2-value"></td>
    <td><input type="text" name="size_2_min" id="size-2-min"></td>
    <td><input type="text" name="size_2_max" id="size-2-max"></td>
    <td><input type="text" name="size_2_measure_1" id="size-2-measure-1"></td>
    <td><input type="text" name="size_2_measure_2" id="size-2-measure-2"></td>
    <td><input type="text" name="size_2_measure_3" id="size-2-measure-3"></td>
    <td><input type="text" name="size_2_measure_4" id="size-2-measure-4"></td>
    <td><input type="text" name="size_2_measure_5" id="size-2-measure-5"></td>
    <td>
        <label><input type="radio" name="size_2_check" value="合格"> 合格</label>
        <label><input type="radio" name="size_2_check" value="不合格"> 不合格</label>
        <label><input type="radio" name="size_2_check" value="AOD"> AOD</label>
    </td>
    <td colspan="2"><textarea name="size_2_note" id="size-2-note"></textarea></td>
</tr>

<tr class="custom-row-height">
    <td class="align-center">3</td>
    <td data-id="size-3" data-image="true"><input type="text" name="size_3_position" id="size-3"></td>
    <td><input type="text" name="size_3_value" id="size-3-value"></td>
    <td><input type="text" name="size_3_min" id="size-3-min"></td>
    <td><input type="text" name="size_3_max" id="size-3-max"></td>
    <td><input type="text" name="size_3_measure_1" id="size-3-measure-1"></td>
    <td><input type="text" name="size_3_measure_2" id="size-3-measure-2"></td>
    <td><input type="text" name="size_3_measure_3" id="size-3-measure-3"></td>
    <td><input type="text" name="size_3_measure_4" id="size-3-measure-4"></td>
    <td><input type="text" name="size_3_measure_5" id="size-3-measure-5"></td>
    <td>
        <label><input type="radio" name="size_3_check" value="合格"> 合格</label>
        <label><input type="radio" name="size_3_check" value="不合格"> 不合格</label>
        <label><input type="radio" name="size_3_check" value="AOD"> AOD</label>
    </td>
    <td colspan="2"><textarea name="size_3_note" id="size-3-note"></textarea></td>
</tr>

<!-- ... existing code ... -->
</```
```

```
