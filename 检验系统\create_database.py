import mysql.connector
from datetime import datetime  # 使用这个导入
import sys
import time

# 数据库连接配置
db_config = {
    'user': 'root',
    'password': 'Jianzhi9.',
    'host': 'localhost',
    'database': 'inspection_system'
}

# 连接到数据库
def get_db_connection():
    return mysql.connector.connect(**db_config)

# 创建数据库
def create_database():
    # 连接到 MySQL 服务器
    conn = mysql.connector.connect(user=db_config['user'], password=db_config['password'], host=db_config['host'])
    cursor = conn.cursor()
    try:
        cursor.execute("CREATE DATABASE IF NOT EXISTS inspection_system")
        print("数据库创建成功")
    except mysql.connector.Error as err:
        print(f"数据库创建失败: {err}")
    finally:
        cursor.close()
        conn.close()

# 创建表
def create_tables():
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # 创建 materials 表（移除 supplier 字段）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS materials (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_code VARCHAR(255) NOT NULL,
                material_name VARCHAR(255) NOT NULL,
                norm VARCHAR(255),
                material VARCHAR(255),
                colour VARCHAR(255),
                version VARCHAR(255),
                remark VARCHAR(255),
                entry_time DATETIME NOT NULL
            )
        """)
        print("materials 表创建成功")

        # 创建 materials_supplier 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS materials_supplier (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_code VARCHAR(255) NOT NULL,
                supplier VARCHAR(255) NOT NULL,
                last_query_time DATETIME NOT NULL,
                UNIQUE KEY unique_supplier (material_code, supplier)
            )
        """)
        print("materials_supplier 表创建成功")

        # 创建 ctq_measurements 表
        # 假设最多支持 10 个 CTQ 测量点
        ctq_columns = ', '.join([f'position_{i + 1} VARCHAR(255), base_value_{i + 1} DECIMAL(10, 2), lower_tolerance_{i + 1} DECIMAL(10, 2), upper_tolerance_{i + 1} DECIMAL(10, 2)' for i in range(10)])
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS ctq_measurements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_id INT,
                {ctq_columns},
                FOREIGN KEY (material_id) REFERENCES materials(id)
            )
        """)
        print("ctq_measurements 表创建成功")

        # 创建 inspection_records 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inspection_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_id INT,
                supplier VARCHAR(255),   
                inspection_date DATE,
                OrderID VARCHAR(255),
                batch   VARCHAR(255),
                inspection_result VARCHAR(255),
                incoming_quantity INT,
                sampling_quantity INT,
                problem_1 VARCHAR(255),
                problem_1_quantity INT,
                problem_1_rate DECIMAL(5, 2),
                problem_2 VARCHAR(255),
                problem_2_quantity INT,
                problem_2_rate DECIMAL(5, 2),
                problem_3 VARCHAR(255),
                problem_3_quantity INT,
                problem_3_rate DECIMAL(5, 2),
                problem_4 VARCHAR(255),
                problem_4_quantity INT,
                problem_4_rate DECIMAL(5, 2),
                problem_5 VARCHAR(255),
                problem_5_quantity INT,
                problem_5_rate DECIMAL(5, 2),
                overall_defect_rate DECIMAL(5, 2),
                FOREIGN KEY (material_id) REFERENCES materials(id)
            )
        """)
        print("inspection_records 表创建成功")

        # 创建 ctq_measurements_data 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ctq_measurements_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                inspection_record_id INT,
                ctq_1_size1 DECIMAL(10,2),
                ctq_1_size2 DECIMAL(10,2),
                ctq_1_size3 DECIMAL(10,2),
                ctq_1_result VARCHAR(255),
                ctq_2_size1 DECIMAL(10,2),
                ctq_2_size2 DECIMAL(10,2),
                ctq_2_size3 DECIMAL(10,2),
                ctq_2_result VARCHAR(255),
                ctq_3_size1 DECIMAL(10,2),
                ctq_3_size2 DECIMAL(10,2),
                ctq_3_size3 DECIMAL(10,2),
                ctq_3_result VARCHAR(255),
                ctq_4_size1 DECIMAL(10,2),
                ctq_4_size2 DECIMAL(10,2),
                ctq_4_size3 DECIMAL(10,2),
                ctq_4_result VARCHAR(255),
                ctq_5_size1 DECIMAL(10,2),
                ctq_5_size2 DECIMAL(10,2),
                ctq_5_size3 DECIMAL(10,2),
                ctq_5_result VARCHAR(255),
                ctq_6_size1 DECIMAL(10,2),
                ctq_6_size2 DECIMAL(10,2),
                ctq_6_size3 DECIMAL(10,2),
                ctq_6_result VARCHAR(255),
                ctq_7_size1 DECIMAL(10,2),
                ctq_7_size2 DECIMAL(10,2),
                ctq_7_size3 DECIMAL(10,2),
                ctq_7_result VARCHAR(255),
                ctq_8_size1 DECIMAL(10,2),
                ctq_8_size2 DECIMAL(10,2),
                ctq_8_size3 DECIMAL(10,2),
                ctq_8_result VARCHAR(255),
                ctq_9_size1 DECIMAL(10,2),
                ctq_9_size2 DECIMAL(10,2),
                ctq_9_size3 DECIMAL(10,2),
                ctq_9_result VARCHAR(255),
                ctq_10_size1 DECIMAL(10,2),
                ctq_10_size2 DECIMAL(10,2),
                ctq_10_size3 DECIMAL(10,2),
                ctq_10_result VARCHAR(255),
                FOREIGN KEY (inspection_record_id) REFERENCES inspection_records(id)
            )
        """)
        print("ctq_measurements_data 表创建成功")

        # 修改 general_inspection_specifications 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS general_inspection_specifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                unique_id VARCHAR(20) NOT NULL,
                is_standard CHAR(1) NOT NULL DEFAULT 'N',
                material_type VARCHAR(255) NOT NULL,
                inspection_item VARCHAR(255) NOT NULL,
                inspection_method VARCHAR(255) NOT NULL,
                inspection_description TEXT NOT NULL,
                sampling_level VARCHAR(50) NOT NULL DEFAULT 'G-II',
                cr VARCHAR(10),
                maj VARCHAR(10),
                min VARCHAR(10),
                UNIQUE KEY unique_id (unique_id)
            )
        """)
        print("general_inspection_specifications 表创建成功")

        # 修改 aql_settings 表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS aql_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                unique_id VARCHAR(20) NOT NULL UNIQUE,
                material_type VARCHAR(255) NOT NULL,
                inspection_item VARCHAR(255) NOT NULL,
                sampling_level VARCHAR(50) NOT NULL DEFAULT 'G-II',
                cr DECIMAL(5,3),
                maj DECIMAL(5,3),
                min DECIMAL(5,3),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_material_inspection (material_type, inspection_item)
            )
        """)
        print("aql_settings 表创建成功")

        # 创建 Material_Sample_Confirmation_Form 基本信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_confirmation_form (
                id INT AUTO_INCREMENT PRIMARY KEY,
                report_code VARCHAR(20) NOT NULL UNIQUE,
                supplier TEXT,
                inspection_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sample_count INT,
                inspector TEXT,
                material_number TEXT,
                graph_number TEXT,
                material_name TEXT,
                drawing_version TEXT,
                material_texture TEXT,
                surface_processing TEXT,
                sample_status TEXT,
                other_textbox TEXT,
                final_judgment VARCHAR(10) DEFAULT '',
                opinion TEXT,
                review VARCHAR(10)
            )
        """)
        print("material_sample_confirmation_form 基本信息表创建成功")

        # 创建尺寸数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_size_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                size_number INT,
                position TEXT,
                value DECIMAL(10, 2),
                min_value DECIMAL(10, 2),
                max_value DECIMAL(10, 2),
                measure_1 DECIMAL(10, 2),
                measure_2 DECIMAL(10, 2),
                measure_3 DECIMAL(10, 2),
                measure_4 DECIMAL(10, 2),
                measure_5 DECIMAL(10, 2),
                check_result TEXT,
                note TEXT,
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id)
            )
        """)
        print("material_sample_size_data 尺寸数据表创建成功")

        # 创建外观检查表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_appearance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                check_number INT,
                check_result TEXT,
                note TEXT,
                other_info TEXT,
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id)
            )
        """)
        print("material_sample_appearance 外观检查表创建成功")

        # 创建功能检查表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_function (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                check_number INT,
                check_result TEXT,
                note TEXT,
                burnin_info TEXT,
                electrical_info TEXT,
                tests_info TEXT,
                other_test TEXT,
                other_info TEXT,
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id)
            )
        """)
        print("material_sample_function 功能检查表创建成功")

        # 创建问题记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                question_number INT,
                question_text TEXT,
                image_path varchar(255),
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id)
            )
        """)
        print("material_sample_questions 问题记录表创建成功")

        conn.commit()
        print("所有表创建成功")
    except mysql.connector.Error as err:
        print(f"表创建失败: {err}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

# 插入数据
def insert_data():
    conn = None
    cursor = None
    try:
        print("\n=== 开始插入数据 ===")
        conn = get_db_connection()
        cursor = conn.cursor()

        # 插入通用检验规范示例数据
        print("\n正在插入通用检验规范数据...")
        inspection_data = [
            {
                'material_id': 1,
                'supplier': '供应商A',
                'inspection_date': '2023-01-01',
                'OrderID': 'ORD2301A',
                'batch': 'BATCH001',
                'inspection_result': '合格',
                'incoming_quantity': 1000,
                'sampling_quantity': 50,
                'problem_1': '轻微划痕',
                'problem_1_quantity': 3,
                'problem_1_rate': 0.6,
                'overall_defect_rate': 0.6
            },
            {
                'material_id': 2,
                'supplier': '供应商B',
                'inspection_date': '2023-02-15',
                'OrderID': 'ORD2302B',
                'batch': 'BATCH002',
                'inspection_result': '不合格',
                'incoming_quantity': 800,
                'sampling_quantity': 40,
                'problem_1': '尺寸偏差',
                'problem_1_quantity': 8,
                'problem_1_rate': 20.0,
                'problem_2': '焊点虚焊',
                'problem_2_quantity': 5,
                'problem_2_rate': 12.5,
                'overall_defect_rate': 32.5
            },
            {
                'material_id': 3,
                'supplier': '供应商C',
                'inspection_date': '2023-03-10',
                'OrderID': 'ORD2303C',
                'batch': 'BATCH003',
                'inspection_result': '合格',
                'incoming_quantity': 1500,
                'sampling_quantity': 75,
                'problem_1': '标识错误',
                'problem_1_quantity': 2,
                'problem_1_rate': 0.27,
                'overall_defect_rate': 0.27
            },
            {
                'material_id': 4,
                'supplier': '供应商A',
                'inspection_date': '2023-04-05',
                'OrderID': 'ORD2304A',
                'batch': 'BATCH004',
                'inspection_result': '合格',
                'incoming_quantity': 2000,
                'sampling_quantity': 100,
                'problem_1': '无',
                'problem_1_quantity': 0,
                'problem_1_rate': 0.0,
                'overall_defect_rate': 0.0
            },
            {
                'material_id': 5,
                'supplier': '供应商D',
                'inspection_date': '2023-05-20',
                'OrderID': 'ORD2305D',
                'batch': 'BATCH005',
                'inspection_result': '不合格',
                'incoming_quantity': 1200,
                'sampling_quantity': 60,
                'problem_1': '色差',
                'problem_1_quantity': 10,
                'problem_1_rate': 16.67,
                'problem_2': '包装破损',
                'problem_2_quantity': 4,
                'problem_2_rate': 6.67,
                'overall_defect_rate': 23.34
            },
            {
                'material_id': 1,
                'supplier': '供应商B',
                'inspection_date': '2023-06-10',
                'OrderID': 'ORD2306B',
                'batch': 'BATCH006',
                'inspection_result': '合格',
                'incoming_quantity': 900,
                'sampling_quantity': 45,
                'problem_1': '边缘毛刺',
                'problem_1_quantity': 1,
                'problem_1_rate': 2.22,
                'overall_defect_rate': 2.22
            },
            {
                'material_id': 2,
                'supplier': '供应商C',
                'inspection_date': '2023-07-01',
                'OrderID': 'ORD2307C',
                'batch': 'BATCH007',
                'inspection_result': '不合格',
                'incoming_quantity': 700,
                'sampling_quantity': 35,
                'problem_1': '功能异常',
                'problem_1_quantity': 7,
                'problem_1_rate': 20.0,
                'problem_2': '接口松动',
                'problem_2_quantity': 3,
                'problem_2_rate': 8.57,
                'overall_defect_rate': 28.57
            },
            {
                'material_id': 3,
                'supplier': '供应商D',
                'inspection_date': '2023-08-15',
                'OrderID': 'ORD2308D',
                'batch': 'BATCH008',
                'inspection_result': '合格',
                'incoming_quantity': 2000,
                'sampling_quantity': 100,
                'problem_1': '无',
                'problem_1_quantity': 0,
                'problem_1_rate': 0.0,
                'overall_defect_rate': 0.0
            },
            {
                'material_id': 4,
                'supplier': '供应商A',
                'inspection_date': '2023-09-01',
                'OrderID': 'ORD2309A',
                'batch': 'BATCH009',
                'inspection_result': '合格',
                'incoming_quantity': 1800,
                'sampling_quantity': 90,
                'problem_1': '轻微变形',
                'problem_1_quantity': 2,
                'problem_1_rate': 2.22,
                'overall_defect_rate': 2.22
            },
            {
                'material_id': 5,
                'supplier': '供应商B',
                'inspection_date': '2023-10-10',
                'OrderID': 'ORD2310B',
                'batch': 'BATCH010',
                'inspection_result': '不合格',
                'incoming_quantity': 1100,
                'sampling_quantity': 55,
                'problem_1': '漏电',
                'problem_1_quantity': 5,
                'problem_1_rate': 9.09,
                'problem_2': '绝缘不足',
                'problem_2_quantity': 3,
                'problem_2_rate': 5.45,
                'overall_defect_rate': 14.54
            }
        ]


        gis_data = [
            {
                'unique_id': 'GIS250218001',
                'is_standard': 'Y',
                'material_type': '电子元件',
                'inspection_item': '外观检查',
                'inspection_method': '目视检查',
                'inspection_description': '检查表面是否有划痕、变形等缺陷',
                'sampling_level': 'G-II',
                'cr': '0.065',
                'maj': '1.0',
                'min': '2.5'
            },
            {
                'unique_id': 'GIS250218002',
                'is_standard': 'Y',
                'material_type': '原材料',
                'inspection_item': '尺寸检验',
                'inspection_method': '卡尺测量',
                'inspection_description': '按图纸要求测量关键尺寸',
                'sampling_level': 'G-I',
                'cr': '0.01',
                'maj': '0.65',
                'min': '2.5'
            },
            {
                'unique_id': 'GIS250218003',
                'is_standard': 'Y',
                'material_type': '成品',
                'inspection_item': '功能测试',
                'inspection_method': '功能测试仪',
                'inspection_description': '测试产品所有功能是否正常',
                'sampling_level': 'G-III',
                'cr': '0.25',
                'maj': '1.0',
                'min': '10'
            },
            {
                'unique_id': 'GIS250218004',
                'is_standard': 'N',
                'material_type': '包装材料',
                'inspection_item': '标识检查',
                'inspection_method': '目视对比',
                'inspection_description': '检查包装标识是否符合要求',
                'sampling_level': 'S-4',
                'cr': '0.65',
                'maj': '1.0',
                'min': '4.0'
            },
            {
                'unique_id': 'GIS250218005',
                'is_standard': 'Y',
                'material_type': '电子元件',
                'inspection_item': '电性能测试',
                'inspection_method': '万用表测量',
                'inspection_description': '测量电阻、电压等电气参数',
                'sampling_level': 'G-II',
                'cr': '0.25',
                'maj': '2.5',
                'min': '10'
            }
        ]
        
        # 插入 AQL 设置示例数据
        aql_data = [
            {
                'unique_id': 'AQL250218001',
                'material_type': '半成品',
                'inspection_item': '外观检验',
                'sampling_level': 'II',
                'cr': '0.0',
                'maj': '1.0',
                'min': '2.5'
            },
            {
                'unique_id': 'AQL250218002',
                'material_type': '原材料',
                'inspection_item': '尺寸检验',
                'sampling_level': 'I',
                'cr': '0.0',
                'maj': '0.65',
                'min': '1.5'
            },
            {
                'unique_id': 'AQL250218003',
                'material_type': '成品',
                'inspection_item': '功能测试',
                'sampling_level': 'III',
                'cr': '0.0',
                'maj': '0.4',
                'min': '1.0'
            },
            {
                'unique_id': 'AQL250218004',
                'material_type': '包装材料',
                'inspection_item': '标识检查',
                'sampling_level': 'I',
                'cr': '0.15',
                'maj': '1.5',
                'min': '4.0'
            },
            {
                'unique_id': 'AQL250218005',
                'material_type': '电子元件',
                'inspection_item': '电性能测试',
                'sampling_level': 'II',
                'cr': '0.1',
                'maj': '0.65',
                'min': '1.5'
            },
            {
                'unique_id': 'AQL250218006',
                'material_type': '五金',
                'inspection_item': '功能',
                'sampling_level': 'G-II',
                'cr': 0.065,
                'maj': 1.0,
                'min': 2.5
            },
            {
                'unique_id': 'AQL250218007',
                'material_type': '五金',
                'inspection_item': '外观',
                'sampling_level': 'G-II',
                'cr': 0.1,
                'maj': 1.5,
                'min': 4.0
            }
        ]

        # 插入物料示例数据
        material_data = [
            {
                'material_code': 'M250218001',
                'material_name': '铝合金外壳',
                'norm': '200x300x50mm',
                'material': 'AL6061',
                'colour': '银色',
                'version': 'A',
                'remark': '外观要求高',
                'entry_time': datetime.now()
            },
            {
                'material_code': 'M250218002',
                'material_name': '主控板',
                'norm': '100x150mm',
                'material': 'FR4',
                'colour': '绿色',
                'version': 'B',
                'remark': '四层板',
                'entry_time': datetime.now()
            },
            {
                'material_code': 'M250218003',
                'material_name': '包装盒',
                'norm': '250x350x100mm',
                'material': '瓦楞纸',
                'colour': '白色',
                'version': 'C',
                'remark': '三层加强',
                'entry_time': datetime.now()
            },
            {
                'material_code': 'M250218004',
                'material_name': '电源模块',
                'norm': '50x80x30mm',
                'material': '塑料',
                'colour': '黑色',
                'version': 'D',
                'remark': '过认证品',
                'entry_time': datetime.now()
            },
            {
                'material_code': 'M250218005',
                'material_name': '显示屏',
                'norm': '7寸',
                'material': 'LCD',
                'colour': '彩色',
                'version': 'E',
                'remark': 'IPS屏',
                'entry_time': datetime.now()
            }
        ]

        # 插入数据到数据库
        # 插入 inspection_records 数据
        
        for item in gis_data:
            try:
                print(f"插入 GIS 记录: {item['unique_id']}")
                cursor.execute("""
                    INSERT INTO general_inspection_specifications 
                    (unique_id, is_standard, material_type, inspection_item, 
                    inspection_method, inspection_description, sampling_level, cr, maj, min)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    item['unique_id'],
                    item['is_standard'],
                    item['material_type'],
                    item['inspection_item'],
                    item['inspection_method'],
                    item['inspection_description'],
                    item['sampling_level'],
                    item['cr'],
                    item['maj'],
                    item['min']
                ))
                print(f"GIS 记录 {item['unique_id']} 插入成功")
            except Exception as e:
                print(f"插入 GIS 记录 {item['unique_id']} 失败: {e}")
                raise
        
        for item in aql_data:
            try:
                print(f"插入 AQL 记录: {item['unique_id']}")
                cursor.execute("""
                    INSERT INTO aql_settings 
                    (unique_id, material_type, inspection_item, sampling_level, cr, maj, min)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    item['unique_id'],
                    item['material_type'],
                    item['inspection_item'],
                    item['sampling_level'],
                    item['cr'],
                    item['maj'],
                    item['min']
                ))
                print(f"AQL 记录 {item['unique_id']} 插入成功")
            except Exception as e:
                print(f"插入 AQL 记录 {item['unique_id']} 失败: {e}")
                raise

        for item in material_data:
            try:
                print(f"插入物料记录: {item['material_code']}")
                cursor.execute("""
                    INSERT INTO materials 
                    (material_code, material_name, norm, material, colour, version, remark, entry_time)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    item['material_code'],
                    item['material_name'],
                    item['norm'],
                    item['material'],
                    item['colour'],
                    item['version'],
                    item['remark'],
                    item['entry_time']
                ))
                
                material_id = cursor.lastrowid
                print(f"物料记录 {item['material_code']} 插入成功，ID: {material_id}")

                # 插入对应的 CTQ 测量数据
                print(f"正在为物料 {item['material_code']} 插入 CTQ 数据...")
                cursor.execute("""
                    INSERT INTO ctq_measurements 
                    (material_id, position_1, base_value_1, lower_tolerance_1, upper_tolerance_1,
                                position_2, base_value_2, lower_tolerance_2, upper_tolerance_2)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    material_id,
                    'L1', 100.0, -0.5, 0.5,
                    'W1', 50.0, -0.3, 0.3
                ))
                print(f"CTQ 数据插入成功")
            except Exception as e:
                print(f"插入物料记录 {item['material_code']} 失败: {e}")
                raise
            print("\n正在插入检验记录数据...")
        for item in inspection_data:
            try:
                # 构造基础字段
                required_fields = (
                    item['material_id'],
                    item['supplier'],
                    item['inspection_date'],
                    item['OrderID'],
                    item['batch'],
                    item['inspection_result'],
                    item['incoming_quantity'],
                    item['sampling_quantity']
                )
                
                # 处理可选问题字段（最多5个问题）
                problem_fields = []
                for i in range(1, 6):
                    problem = f"problem_{i}"
                    quantity = f"problem_{i}_quantity"
                    rate = f"problem_{i}_rate"
                    if problem in item:
                        problem_fields.extend([item[problem], item[quantity], item[rate]])
                    else:
                        problem_fields.extend([None, None, None])
                
                # 构造完整的参数元组
                all_fields = required_fields + tuple(problem_fields) + (item['overall_defect_rate'],)
                
                # 执行插入
                cursor.execute("""
                    INSERT INTO inspection_records 
                    (material_id, supplier, inspection_date, OrderID, batch, 
                    inspection_result, incoming_quantity, sampling_quantity,
                    problem_1, problem_1_quantity, problem_1_rate,
                    problem_2, problem_2_quantity, problem_2_rate,
                    problem_3, problem_3_quantity, problem_3_rate,
                    problem_4, problem_4_quantity, problem_4_rate,
                    problem_5, problem_5_quantity, problem_5_rate,
                    overall_defect_rate)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, 
                            %s, %s, %s, 
                            %s, %s, %s, 
                            %s, %s, %s, 
                            %s, %s, %s, 
                            %s, %s, %s, 
                            %s)
                """, all_fields)
                print(f"插入检验记录: {item['OrderID']} 成功")
            except Exception as e:
                print(f"插入检验记录失败: {e}")
                raise

        conn.commit()
        print("\n=== 所有数据插入成功 ===")
        
    except mysql.connector.Error as err:
        print(f"\n!!! 数据库错误: {err}")
        if conn:
            conn.rollback()
        raise
    except Exception as e:
        print(f"\n!!! 其他错误: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == '__main__':
    try:
        # 1. 创建数据库
        create_database()
        print("\n=== 数据库创建完成 ===")
        
        # 2. 创建表
        create_tables()
        print("\n=== 表创建完成 ===")
        
        # 3. 插入数据
        insert_data()
        print("\n=== 数据插入完成 ===")
        
        # 4. 显示成功消息并等待3秒后自动退出
        print("\n=== 所有操作已完成！系统将在3秒后自动退出... ===")
        time.sleep(30)
        sys.exit(0)
        
    except mysql.connector.Error as err:
        print(f"\n!!! 执行出错: {err}")
        print("\n按任意键退出...")
        input()
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n=== 用户中断操作，正在退出... ===")
        sys.exit(1)
    except Exception as e:
        print(f"\n!!! 意外错误: {e}")
        print("\n按任意键退出...")
        input()
        sys.exit(1)