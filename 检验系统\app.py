from flask import Flask, render_template, request, redirect, url_for, jsonify, Response, Blueprint
from db_config import DB_CONFIG as db_config
import mysql.connector
import uuid, random, string
import datetime
from datetime import date, datetime, time
from decimal import Decimal
from work_blueprints.Material_Sample_Confirmation_Form import Material_Sample_Confirmation_Form_bp
from work_blueprints.settings import settings_bp
from work_blueprints.material_sample_list import material_sample_list_bp
from work_blueprints.Material_Sample_Confirmation_Form_load_data import Material_Sample_Confirmation_Form_load_data_bp
from work_blueprints.Material_Sample_Confirmation_Form_modify import Material_Sample_Confirmation_Form_modify_bp, register_global_routes
from db_config import get_db_connection


app = Flask(__name__)


# 自定义 Jinja2 全局函数
app.jinja_env.globals.update(enumerate=enumerate)

# 注册蓝图
app.register_blueprint(
    Material_Sample_Confirmation_Form_bp,
    url_prefix='/Material_Sample_Confirmation_Form'  # 保持此前缀配置
)

app.register_blueprint(
    settings_bp,
    url_prefix='/settings'  # 添加settings蓝图
)

app.register_blueprint(
    material_sample_list_bp,
    url_prefix='/material_sample_list'  # 添加material_sample_list蓝图
)

app.register_blueprint(
    Material_Sample_Confirmation_Form_load_data_bp,
    url_prefix='/Material_Sample_Confirmation_Form/load-data'  # 前缀正确
)

app.register_blueprint(
    Material_Sample_Confirmation_Form_modify_bp,
    url_prefix='/Material_Sample_Confirmation_Form_modify'  # 保持此前缀配置
)

# 注册全局路由处理函数
register_global_routes(app)



# ===== 基本路由定义 =====
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/settings', strict_slashes=False)
def settings():
    return render_template('settings.html')

@app.route('/new_material', methods=['GET', 'POST'])
def new_material():
    if request.method == 'POST':
        material_code = request.form['material_code']       
        # 检查物料料号是否存在
        with get_db_connection() as conn:
            cursor = conn.cursor()
            query = "SELECT COUNT(*) FROM materials WHERE material_code = %s"
            cursor.execute(query, (material_code,))
            count = cursor.fetchone()[0]
            if count > 0:
                # 物料料号已存在，提示用户
                return '<script>alert("物料料号已存在，请重新输入！"); window.history.back();</script>'
        material_name = request.form['material_name']
        norm = request.form['norm']
        material = request.form['material']
        colour = request.form['colour']
        version = request.form['version']
        remark = request.form['remark']
        suppliers = request.form.getlist('supplier[]')
        entry_time = datetime.now()
        # 获取 CTQ 尺寸测量数据
        positions = request.form.getlist('position[]')
        base_values = request.form.getlist('base_value[]')
        lower_tolerances = request.form.getlist('lower_tolerance[]')
        upper_tolerances = request.form.getlist('upper_tolerance[]')
        # 使用 get_db_connection 函数获取数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            # 插入物料信息（不含供应商）
            cursor.execute(
                "INSERT INTO materials (material_code, material_name, norm, material,colour, version, remark, entry_time) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)",
                (material_code, material_name, norm, material,colour, version,remark, entry_time)
            )
            material_id = cursor.lastrowid
            # 动态构建 CTQ 插入语句
            columns = []
            values = [material_id]
            for i in range(len(positions)):
                position = positions[i].strip() if positions[i].strip() else None
                base_value = float(base_values[i]) if base_values[i].strip() and base_values[i].strip().replace('.', '',
                                                                                                            1).isdigit() else None
                lower_tolerance = float(lower_tolerances[i]) if lower_tolerances[i].strip() and lower_tolerances[i].strip().replace(
                    '.', '', 1).isdigit() else None
                upper_tolerance = float(upper_tolerances[i]) if upper_tolerances[i].strip() and upper_tolerances[i].strip().replace(
                    '.', '', 1).isdigit() else None
                columns.extend([f'position_{i + 1}', f'base_value_{i + 1}', f'lower_tolerance_{i + 1}', f'upper_tolerance_{i + 1}'])
                values.extend([position, base_value, lower_tolerance, upper_tolerance])
            columns_str = ', '.join(columns)
            placeholders = ', '.join(['%s'] * len(values))
            query = f"INSERT INTO ctq_measurements (material_id, {columns_str}) VALUES ({placeholders})"
            cursor.execute(query, tuple(values))

            # 插入供应商
            for supplier in suppliers:
                if supplier.strip():
                    cursor.execute(
                        "INSERT INTO materials_supplier (material_code, supplier, last_query_time) "
                        "VALUES (%s, %s, %s) "
                        "ON DUPLICATE KEY UPDATE last_query_time = VALUES(last_query_time)",
                        (material_code, supplier.strip(), entry_time)
                    )
            # 所有操作成功后再提交事务
            conn.commit()
            print("数据插入成功")
        except mysql.connector.Error as err:
            print(f"new_material数据库操作出错: {err}")
            conn.rollback()
        except ValueError as val_err:
            print(f"数据类型转换出错: {val_err}")
            conn.rollback()
        finally:
            cursor.close()
            conn.close()
        return redirect(url_for('new_material'))
    return render_template('new_material.html')


# 新增路由获取CTQ数据
@app.route('/get_ctq_data', methods=['GET'])
def get_ctq_data():
    material_code = request.args.get('material_code')
    ctq_data = []
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            # 获取物料ID
            cursor.execute("SELECT id FROM materials WHERE material_code = %s", (material_code,))
            material = cursor.fetchone()
            if material:
                material_id = material['id']
                # 查询CTQ数据
                columns = []
                for i in range(10):
                    columns.extend([
                        f'position_{i+1} as position_{i+1}',
                        f'base_value_{i+1} as base_value_{i+1}',
                        f'lower_tolerance_{i+1} as lower_tolerance_{i+1}',
                        f'upper_tolerance_{i+1} as upper_tolerance_{i+1}'
                    ])
                query = f"SELECT {', '.join(columns)} FROM ctq_measurements WHERE material_id = %s"
                cursor.execute(query, (material_id,))
                row = cursor.fetchone()
                
                # 整理有效数据
                for i in range(10):
                    position = row.get(f'position_{i+1}')
                    base = row.get(f'base_value_{i+1}')
                    lower = row.get(f'lower_tolerance_{i+1}')
                    upper = row.get(f'upper_tolerance_{i+1}')
                    if all(v is not None for v in [position, base, lower, upper]):
                        ctq_data.append({
                            'position': position,
                            'base_value': base,
                            'lower_tolerance': lower,
                            'upper_tolerance': upper
                        })
    except Exception as e:
        print(f"Error fetching CTQ data: {e}")
    return jsonify(ctq_data)

# 新增路由获取供应商数据

@app.route('/manage_supplier', methods=['POST'])
def manage_supplier():
    material_id = request.form.get('material_id')
    material_code = request.form.get('material_code')
    supplier = request.form.get('supplier')
    action = request.form.get('action')

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        if action == 'delete':
            query = "DELETE FROM materials_supplier WHERE material_code = %s AND supplier = %s"
            cursor.execute(query, (material_code, supplier))
            conn.commit()
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '未知操作'})
    except mysql.connector.Error as err:
        print(f"edit_material数据库操作出错: {err}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

    # 重定向回 edit_material 页面，并传递当前物料的 ID
    return redirect(url_for('edit_material', material_id=material_id))

@app.route('/get_suppliers', methods=['GET'])
def get_suppliers():
    material_code = request.args.get('material_code')
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT supplier FROM materials_supplier WHERE material_code = %s ORDER BY last_query_time DESC",
            (material_code,)
        )
        return jsonify([row[0] for row in cursor.fetchall()])

# 检验记录录入页面
@app.route('/inspection_record', methods=['GET', 'POST'])
def inspection_record():
    suppliers = []
    default_supplier = None
    material_code = request.args.get('material_code', '')
    selected_supplier = request.form.get('supplier')  # 获取选择的供应商

    if request.method == 'GET':
        with get_db_connection() as conn:
            cursor = conn.cursor()
            if material_code:
                # 查询所有供应商
                cursor.execute(
                    "SELECT supplier FROM materials_supplier WHERE material_code = %s ORDER BY last_query_time DESC",
                    (material_code,)
                )
                suppliers = [row[0] for row in cursor.fetchall()]

                # 获取 last_query_time 最近的供应商
                cursor.execute(
                    "SELECT supplier FROM materials_supplier WHERE material_code = %s ORDER BY last_query_time DESC LIMIT 1",
                    (material_code,)
                )
                default_supplier = cursor.fetchone()
                if default_supplier:
                    default_supplier = default_supplier[0]

    if request.method == 'POST':
        material_code = request.form.get('material_code')
        inspection_date = request.form.get('inspection_date')
        new_supplier = request.form.get('new_supplier')  # 获取新增供应商

        # 使用 get_db_connection 函数获取数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()

        # 处理检验日期
        if not inspection_date:
            inspection_date = datetime.now().strftime('%Y-%m-%d-%H-%M')
        else:
            try:
                # 尝试将日期字符串转换为日期对象，以确保格式正确
                datetime.strptime(inspection_date, '%Y-%m-%d-%H-%M')
            except ValueError:
                # 如果日期格式不正确，设置为当前日期
                inspection_date = datetime.now().strftime('%Y-%m-%d-%H-%M')

        # 根据物料料号查询物料信息
        try:
            query = "SELECT id FROM materials WHERE material_code = %s"
            cursor.execute(query, (material_code,))
            material = cursor.fetchone()

            if material:
                material_id = material[0]

                # 插入新增供应商
                if new_supplier:
                    cursor.execute(
                        "INSERT INTO materials_supplier (material_code, supplier, last_query_time) "
                        "VALUES (%s, %s, %s) "
                        "ON DUPLICATE KEY UPDATE last_query_time = VALUES(last_query_time)",
                        (material_code, new_supplier.strip(), datetime.now())
                    )

                # 获取表单中的问题点及数量
                problem_data = {}
                for i in range(1, 6):
                    problem_key = f'problem_{i}'
                    problem_quantity_key = f'problem_{i}_quantity'
                    problem_data[problem_key] = request.form.get(problem_key, '')
                    # 修复部分：检查是否为空字符串
                    quantity_value = request.form.get(problem_quantity_key, '0')
                    if quantity_value == '':
                        quantity_value = '0'
                    problem_data[problem_quantity_key] = float(quantity_value)

                # 获取抽检数量
                sampling_quantity = int(request.form.get('sampling_quantity', 0))

                # 计算不良率
                problem_rates = {}
                overall_defect_rate = 0
                for i in range(1, 6):
                    problem_quantity_key = f'problem_{i}_quantity'
                    problem_rate_key = f'problem_{i}_rate'
                    problem_quantity = problem_data[problem_quantity_key]
                    problem_rates[problem_rate_key] = problem_quantity / sampling_quantity if sampling_quantity > 0 else 0
                    overall_defect_rate += problem_rates[problem_rate_key]

                # 新增CTQ尺寸数据采集
                ctq_sizes = {}
                ctq_results = {}

                # 遍历表单数据，确保每个字段只取一次值
                for key, value in request.form.items():
                    if key.startswith('ctq_'):
                        parts = key.split('_')
                        index = parts[1]
                        field = '_'.join(parts[2:])
                        if field.startswith('size'):
                            ctq_sizes[f'ctq_{index}_{field}'] = value
                        elif field == 'result_hidden':
                            ctq_results[f'ctq_{index}_{field}'] = value

                # 插入检验记录
                query = """
                    INSERT INTO inspection_records (material_id, supplier, inspection_date, OrderID, batch, inspection_result, incoming_quantity, sampling_quantity,
                                                     problem_1, problem_1_quantity, problem_1_rate,
                                                     problem_2, problem_2_quantity, problem_2_rate,
                                                     problem_3, problem_3_quantity, problem_3_rate,
                                                     problem_4, problem_4_quantity, problem_4_rate,
                                                     problem_5, problem_5_quantity, problem_5_rate,
                                                     overall_defect_rate)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    material_id,
                    selected_supplier or default_supplier,  # 使用默认供应商或选择的供应商
                    inspection_date,
                    request.form.get('OrderID'),
                    request.form.get('batch'),
                    request.form.get('inspection_result'),
                    request.form.get('incoming_quantity'),
                    sampling_quantity,
                    problem_data['problem_1'],
                    problem_data['problem_1_quantity'],
                    problem_rates['problem_1_rate'],
                    problem_data['problem_2'],
                    problem_data['problem_2_quantity'],
                    problem_rates['problem_2_rate'],
                    problem_data['problem_3'],
                    problem_data['problem_3_quantity'],
                    problem_rates['problem_3_rate'],
                    problem_data['problem_4'],
                    problem_data['problem_4_quantity'],
                    problem_rates['problem_4_rate'],
                    problem_data['problem_5'],
                    problem_data['problem_5_quantity'],
                    problem_rates['problem_5_rate'],
                    overall_defect_rate
                )
                cursor.execute(query, values)   
                inspection_record_id = cursor.lastrowid  # 获取插入的检验记录的 ID

                # 动态构建插入语句
                columns = ['inspection_record_id']
                values = [inspection_record_id]

                for i in range(1, 11):
                    for field in ['size1', 'size2', 'size3', 'result']:
                        key = f'ctq_{i}_{field}'
                        value = ctq_sizes.get(key) if field.startswith('size') else ctq_results.get(f'{key}_hidden')
                        if value is not None:
                            columns.append(key)
                            values.append(value)

                query = f"INSERT INTO ctq_measurements_data ({', '.join(columns)}) VALUES ({', '.join(['%s'] * len(values))})"
                cursor.execute(query, tuple(values))

                 # 更新所选供应商的 last_query_time
                if selected_supplier:
                    cursor.execute(
                        "INSERT INTO materials_supplier (material_code, supplier, last_query_time) "
                        "VALUES (%s, %s, %s) "
                        "ON DUPLICATE KEY UPDATE last_query_time = VALUES(last_query_time)",
                        (material_code, selected_supplier, datetime.now())
                    )

                conn.commit()
        except mysql.connector.Error as err:
            print(f"inspection_record数据库操作出错: {err}")
            conn.rollback()
        finally:
            cursor.close()

        return redirect(url_for('inspection_record'))

    return render_template('inspection_record.html', 
                           suppliers=suppliers, 
                           default_supplier=default_supplier, 
                           material_code=material_code)


# 根据物料料号查询物料名称的接口
@app.route('/get_material_name', methods=['GET'])
def get_material_name():
    material_code = request.args.get('material_code')
    with get_db_connection() as conn:
        cursor = conn.cursor()
        try:
            # 获取物料基本信息
            cursor.execute("SELECT material_name FROM materials WHERE material_code = %s", (material_code,))
            material = cursor.fetchone()
            
            if material:
                # 获取最新供应商
                cursor.execute(
                    "SELECT supplier FROM materials_supplier WHERE material_code = %s ORDER BY last_query_time DESC LIMIT 1",
                    (material_code,)
                )
                supplier = cursor.fetchone()
                return jsonify({
                    'material_name': material[0],
                    'supplier': supplier[0] if supplier else '',
                    'exists': True
                })
            return jsonify({'material_name': '', 'supplier': '', 'exists': False})
        except mysql.connector.Error as err:
            print(f"数据库查询出错: {err}")
        finally:
            cursor.close()

# 根据物料料号查询物料规格、版本、颜色的接口
@app.route('/get_material_info', methods=['GET'])
def get_material_info():
    material_code = request.args.get('material_code')
    with get_db_connection() as conn:
        cursor = conn.cursor(dictionary=True)
        try:
            query = "SELECT norm, material, colour, version, remark FROM materials WHERE material_code = %s"
            cursor.execute(query, (material_code,))
            material_info = cursor.fetchone()
            if material_info:
                return jsonify(material_info)
            return jsonify({'norm': '', 'material': '', 'colour': '', 'version': '', 'remark': ''})
        except mysql.connector.Error as err:
            print(f"数据库查询出错: {err}")
        finally:
            cursor.close()


# 新增：检查物料编码是否存在的接口
@app.route('/check_material_code', methods=['GET'])
def check_material_code():
    material_code = request.args.get('material_code')
    with get_db_connection() as conn:
        cursor = conn.cursor()
        try:
            query = "SELECT COUNT(*) FROM materials WHERE material_code = %s"
            cursor.execute(query, (material_code,))
            count = cursor.fetchone()[0]
            if count > 0:
                return jsonify({'exists': True})
            return jsonify({'exists': False})
        except mysql.connector.Error as err:
            print(f"数据库查询出错: {err}")
        finally:
            cursor.close()


# 检验记录查询页面
@app.route('/inspection_query', methods=['GET', 'POST'])
def inspection_query():
    records = []
    if request.method == 'POST':
        # 修改为获取物料编码
        material_code = request.form.get('material_code')
        inspection_date = request.form.get('inspection_date')

        with get_db_connection() as conn:
            cursor = conn.cursor()
            try:
                query = """
                    SELECT m.material_code, 
                        m.material_name, ir.supplier, ir.inspection_date, OrderID, batch, ir.inspection_result,
                        ir.problem_1, ir.problem_1_quantity, ir.problem_1_rate,
                        ir.problem_2, ir.problem_2_quantity, ir.problem_2_rate,
                        ir.problem_3, ir.problem_3_quantity, ir.problem_3_rate,
                        ir.problem_4, ir.problem_4_quantity, ir.problem_4_rate,
                        ir.problem_5, ir.problem_5_quantity, ir.problem_5_rate,
                        ir.overall_defect_rate
                    FROM inspection_records ir
                    JOIN materials m ON ir.material_id = m.id
                    WHERE 1=1
                """
                conditions = []
                values = []
                if material_code:
                    conditions.append("m.material_code = %s")
                    values.append(material_code)
                if inspection_date:
                    conditions.append("ir.inspection_date = %s")
                    values.append(inspection_date)
                if conditions:
                    query += " AND " + " AND ".join(conditions)
                cursor.execute(query, tuple(values))
                records = cursor.fetchall()
            except mysql.connector.Error as err:
                print(f"数据库查询出错: {err}")
            finally:
                cursor.close()

    return render_template('inspection_query.html', records=records)


# 检验记录管理页面
@app.route('/inspection_management', methods=['GET', 'POST'])
def inspection_management():
    # 传递空的搜索字段和值
    return render_template('inspection_management.html', 
                         search_fields=[], 
                         search_values=[])

@app.route('/api/inspection_records', methods=['GET'])
def get_inspection_records():
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 基础查询
        query = """
            SELECT ir.*, m.material_code, m.material_name
            FROM inspection_records ir
            LEFT JOIN materials m ON ir.material_id = m.id
            ORDER BY ir.inspection_date DESC
        """
        
        cursor.execute(query)
        records = cursor.fetchall()
        
        # 格式化日期
        for record in records:
            if record['inspection_date']:
                record['inspection_date'] = record['inspection_date'].strftime('%Y-%m-%d')
        
        return jsonify(records)
        
    except mysql.connector.Error as err:
        print(f"MySQL错误: {err}")
        return jsonify({"error": f"数据库错误: {str(err)}"}), 500
    except Exception as e:
        print(f"获取检验记录出错: {e}")
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 删除检验记录
@app.route('/delete_inspection_record/<int:record_id>', methods=['GET'])
def delete_inspection_record(record_id):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 先删除关联的 ctq_measurements_data 记录
            delete_ctq_data_query = "DELETE FROM ctq_measurements_data WHERE inspection_record_id = %s"
            cursor.execute(delete_ctq_data_query, (record_id,))
            
            # 再删除检验记录
            delete_inspection_query = "DELETE FROM inspection_records WHERE id = %s"
            cursor.execute(delete_inspection_query, (record_id,))
            
            conn.commit()
    except mysql.connector.Error as err:
        print(f"数据库连接或删除出错: {err}")
        conn.rollback()
    return redirect(url_for('inspection_management'))


# 物料管理页面
@app.route('/material_management', methods=['GET', 'POST'])
def material_management():
    materials = []
    search_term = request.form.get('search_term', '')
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            if search_term:
                query = """
                    SELECT m.id, m.material_code, m.material_name, m.entry_time,
                           GROUP_CONCAT(ms.supplier) AS suppliers
                    FROM materials m
                    LEFT JOIN materials_supplier ms ON m.material_code = ms.material_code
                    WHERE m.material_code LIKE %s
                    GROUP BY m.id
                """
                cursor.execute(query, ('%' + search_term + '%',))
            else:
                query = """
                    SELECT m.id, m.material_code, m.material_name, m.entry_time,
                           GROUP_CONCAT(ms.supplier) AS suppliers
                    FROM materials m
                    LEFT JOIN materials_supplier ms ON m.material_code = ms.material_code
                    GROUP BY m.id
                """
                cursor.execute(query)
            materials = cursor.fetchall()
    except mysql.connector.Error as err:
        print(f"material_management数据库连接或查询出错: {err}")

    return render_template('material_management.html', materials=materials, search_term=search_term)



# 删除物料记录
@app.route('/delete_material/<int:material_id>', methods=['GET'])
def delete_material(material_id):
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            # 先删除关联的 CTQ 尺寸测量数据
            delete_ctq_query = "DELETE FROM ctq_measurements WHERE material_id = %s"
            cursor.execute(delete_ctq_query, (material_id,))

            # 再删除关联的检验记录
            delete_inspection_query = "DELETE FROM inspection_records WHERE material_id = %s"
            cursor.execute(delete_inspection_query, (material_id,))

            # 最后删除物料记录
            delete_material_query = "DELETE FROM materials WHERE id = %s"
            cursor.execute(delete_material_query, (material_id,))

            conn.commit()
    except mysql.connector.Error as err:
        print(f"数据库连接或删除出错: {err}")
    return redirect(url_for('material_management'))


# 物料管理页面里的修改物料信息
@app.route('/edit_material/<int:material_id>', methods=['GET', 'POST'])
def edit_material(material_id):
    suppliers = []  # 初始化 suppliers 变量

    if request.method == 'POST':
        material_code = request.form['material_code']
        material_name = request.form['material_name']
        norm = request.form['norm']
        material = request.form['material']
        colour = request.form['colour']
        version = request.form['version']
        remark = request.form['remark']
        suppliers = request.form.getlist('supplier[]')
        entry_time = datetime.now()

        # 获取 CTQ 尺寸测量数据
        positions = request.form.getlist('position[]')
        base_values = request.form.getlist('base_value[]')
        lower_tolerances = request.form.getlist('lower_tolerance[]')
        upper_tolerances = request.form.getlist('upper_tolerance[]')

        # 使用 get_db_connection 函数获取数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            # 更新物料信息
            query = "UPDATE materials SET material_code = %s, material_name = %s, norm = %s, material = %s, colour = %s, version = %s, remark = %s, entry_time = %s WHERE id = %s"
            cursor.execute(query, (material_code, material_name, norm, material, colour, version, remark, entry_time, material_id))

            # 删除原有的 CTQ 尺寸测量数据
            cursor.execute("DELETE FROM ctq_measurements WHERE material_id = %s", (material_id,))

            # 动态构建 CTQ 插入语句
            columns = []
            values = [material_id]
            for i in range(len(positions)):
                position = positions[i].strip() if positions[i].strip() else None
                base_value = float(base_values[i]) if base_values[i].strip() and base_values[i].strip().replace('.', '', 1).isdigit() else None
                lower_tolerance = float(lower_tolerances[i]) if lower_tolerances[i].strip() and lower_tolerances[i].strip().replace('.', '', 1).isdigit() else None
                upper_tolerance = float(upper_tolerances[i]) if upper_tolerances[i].strip() and upper_tolerances[i].strip().replace('.', '', 1).isdigit() else None

                columns.extend([f'position_{i + 1}', f'base_value_{i + 1}', f'lower_tolerance_{i + 1}', f'upper_tolerance_{i + 1}'])
                values.extend([position, base_value, lower_tolerance, upper_tolerance])

            if columns:  # 检查 columns 是否为空
                columns_str = ', '.join(columns)
                placeholders = ', '.join(['%s'] * len(values))
                query = f"INSERT INTO ctq_measurements (material_id, {columns_str}) VALUES ({placeholders})"
                print(f"Generated SQL: {query}")  # 添加日志输出
                cursor.execute(query, tuple(values))

            # 插入新的供应商信息
            new_suppliers = request.form.getlist('new_supplier[]')
            for supplier in new_suppliers:
                if supplier.strip():
                    insert_supplier_query = "INSERT INTO materials_supplier (material_code, supplier, last_query_time) VALUES (%s, %s, %s)"
                    cursor.execute(insert_supplier_query, (material_code, supplier.strip(), datetime.now()))

            # 所有操作成功后再提交事务
            conn.commit()
            print("数据插入成功")
            
            # 返回包含 JavaScript 代码的响应
            response_content = '<script>alert("更新成功"); window.close();</script>'
            return Response(response_content, mimetype='text/html')
        except mysql.connector.Error as err:
            print(f"edit_material数据库操作出错: {err}")
            conn.rollback()
        except ValueError as val_err:
            print(f"数据类型转换出错: {val_err}")
            conn.rollback()
        finally:
            cursor.close()
            conn.close()
        
        # 如果发生错误，重定向到编辑页面
        return redirect(url_for('edit_material', material_id=material_id))

    # 获取物料信息
    conn = get_db_connection()
    cursor = conn.cursor()
    material = None
    ctq_measurements = []  # 初始化 ctq_measurements 变量
    try:
        query = "SELECT material_code, material_name, norm, material, colour, version, remark, entry_time FROM materials WHERE id = %s"
        cursor.execute(query, (material_id,))
        material = cursor.fetchone()

        # 查询所有 CTQ 列
        columns = [f'position_{i + 1}, base_value_{i + 1}, lower_tolerance_{i + 1}, upper_tolerance_{i + 1}' for i in range(10)]
        columns_str = ', '.join(columns)
        query = f"SELECT {columns_str} FROM ctq_measurements WHERE material_id = %s"
        cursor.execute(query, (material_id,))
        result = cursor.fetchone()

        if result:
            for i in range(0, len(result), 4):
                position = result[i]
                base_value = result[i + 1]
                lower_tolerance = result[i + 2]
                upper_tolerance = result[i + 3]
                if any([position, base_value, lower_tolerance, upper_tolerance]):
                    ctq_measurements.append((position, base_value, lower_tolerance, upper_tolerance))

        # 获取供应商列表
        cursor.execute(
            "SELECT supplier FROM materials_supplier WHERE material_code = (SELECT material_code FROM materials WHERE id = %s)",
            (material_id,)
        )
        suppliers = [row[0] for row in cursor.fetchall()]

    except mysql.connector.Error as err:
        print(f"edit_material数据库连接或查询出错: {err}")
    finally:
        cursor.close()
        conn.close()

    # 在 Python 中使用 enumerate 处理数据
    ctq_measurements_with_index = list(enumerate(ctq_measurements, start=1))

    return render_template('edit_material.html', 
                         material=material, 
                         ctq_measurements_with_index=ctq_measurements_with_index,
                         suppliers=suppliers)


# 通用检验规范页面
@app.route('/general_inspection_specifications', methods=['GET', 'POST'])
def general_inspection_specifications():
    if request.method == 'POST':
        material_type = request.form['material_type']
        inspection_item = request.form['inspection_item']
        inspection_method = request.form['inspection_method']
        inspection_description = request.form['inspection_description']
        cr = int(request.form['cr'])
        maj = int(request.form['maj'])
        min_val = int(request.form['min'])

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            query = """
                INSERT INTO general_inspection_specifications (material_type, inspection_item, inspection_method, inspection_description, cr, maj, min)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(query, (material_type, inspection_item, inspection_method, inspection_description, cr, maj, min_val))
            conn.commit()
        except mysql.connector.Error as err:
            print(f"general_inspection_specifications数据库操作出错: {err}")
            conn.rollback()
        finally:
            cursor.close()
            conn.close()
        return redirect(url_for('general_inspection_specifications'))
    return render_template('general_inspection_specifications.html')

# 获取通用检验规范数据
@app.route('/api/general_inspection_specifications', methods=['GET'])
def get_general_inspection_specifications():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        # 获取搜索和排序参数
        search_fields = request.args.getlist('search_field[]')
        search_values = request.args.getlist('search_value[]')
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'DESC')
        
        # 构建基础查询
        base_query = "SELECT * FROM general_inspection_specifications"
        count_query = "SELECT COUNT(*) as total FROM general_inspection_specifications"
        params = []
        
        # 如果有搜索条件，添加 WHERE 子句
        if search_fields and search_values:
            conditions = []
            for field, value in zip(search_fields, search_values):
                if field and value:
                    if field == 'is_standard':
                        if value.upper() in ['Y', 'N']:
                            conditions.append(f"{field} = %s")
                            params.append(value.upper())
                    else:
                        conditions.append(f"{field} LIKE %s")
                        params.append(f"%{value}%")
            
            if conditions:
                where_clause = " WHERE " + " AND ".join(conditions)
                base_query += where_clause
                count_query += where_clause
        
        # 执行计数查询
        cursor.execute(count_query, tuple(params))
        total_records = cursor.fetchone()['total']
        total_pages = (total_records + page_size - 1) // page_size
        
        # 添加排序和分页
        base_query += f" ORDER BY {sort_field} {sort_order}"
        base_query += " LIMIT %s OFFSET %s"
        
        # 添加分页参数
        params.extend([page_size, (page - 1) * page_size])
        
        print(f"执行查询: {base_query}")  # 调试日志
        print(f"参数: {params}")     # 调试日志
        
        cursor.execute(base_query, tuple(params))
        specifications = cursor.fetchall()
        
        # 处理结果
        for spec in specifications:
            for key in ['cr', 'maj', 'min']:
                if spec[key] is not None:
                    spec[key] = str(float(spec[key])) if spec[key] != '' else ''
            spec['is_standard'] = 'Y' if spec['is_standard'] == 'Y' else 'N'
        
        return jsonify({
            'specifications': specifications,
            'total_pages': total_pages,
            'current_page': page,
            'total_records': total_records
        })
        
    except Exception as e:
        print(f"获取通用检验规范列表出错: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# 获取单个通用检验规范数据
@app.route('/api/general_inspection_specifications/<int:id>', methods=['GET'])
def get_general_inspection_specification(id):
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute("SELECT * FROM general_inspection_specifications WHERE id = %s", (id,))
        data = cursor.fetchone()
        if data:
            return jsonify(data)
        return jsonify({'message': '规范未找到'}), 404
    except mysql.connector.Error as err:
        print(f"获取单个通用检验规范数据出错: {err}")
    finally:
        cursor.close()
        conn.close()

# 修改生成唯一码的函数
def generate_unique_id():
    current_date = datetime.now().strftime('%y%m%d')
    prefix = f"GIS{current_date}"  # 格式: GIS + 年月日
    
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # 查询当天已有的最大序号
        query = "SELECT MAX(SUBSTRING(unique_id, 10)) FROM general_inspection_specifications WHERE unique_id LIKE %s"
        cursor.execute(query, (f"{prefix}%",))
        max_seq = cursor.fetchone()[0]
        
        if max_seq and max_seq.isdigit():
            # 提取序号部分并加1
            current_number = int(max_seq) + 1
        else:
            # 如果当天没有记录，从001开始
            current_number = 1
            
        # 确保序号部分是3位数，不足3位时前面补0
        unique_id = f"{prefix}{current_number:03d}"
        return unique_id
    except mysql.connector.Error as err:
        print(f"生成唯一ID时出错: {err}")
        return None
    finally:
        cursor.close()
        conn.close()

# 添加新的API端点用于获取唯一码
@app.route('/api/generate_unique_id', methods=['GET'])
def get_unique_id():
    unique_id = generate_unique_id()
    if unique_id:
        return jsonify({'unique_id': unique_id}), 200
    return jsonify({'message': '生成唯一码失败'}), 500

# app.py 修改检查重复的后端接口
@app.route('/api/general_inspection_specifications/check_duplicate', methods=['GET'])
def check_duplicate_specification():
    conn = None
    cursor = None
    try:
        material_type = request.args.get('material_type')
        inspection_item = request.args.get('inspection_item')
        
        # 如果任一字段为空则不检查
        if not all([material_type, inspection_item]):
            return jsonify({
                'status': 'ok',
                'message': '未发现重复记录'
            })
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT unique_id, material_type, inspection_item, inspection_method, inspection_description
            FROM general_inspection_specifications 
            WHERE material_type = %s 
            AND inspection_item = %s
            AND inspection_method = %s
            AND inspection_description = %s
        """
        params = [material_type, inspection_item, '', '']
        
        print(f"执行查询: {query}，参数: {params}")
        
        cursor.execute(query, tuple(params))
        result = cursor.fetchone()
        
        if result:
            print(f"找到重复记录: {result}")
            return jsonify({
                'status': 'duplicate',
                'message': '已存在完全相同的记录',
                'duplicate_info': result
            }), 409
        
        return jsonify({
            'status': 'ok',
            'message': '未发现重复记录'
        })
        
    except Exception as e:
        print(f"检查重复记录时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# app.py 修改 add_general_inspection_specification 函数
@app.route('/api/general_inspection_specifications', methods=['POST'])
def add_general_inspection_specification():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': '未接收到数据'}), 400
            
        # 验证所有必填字段
        required_fields = ['material_type', 'inspection_item', 'inspection_method', 'inspection_description']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                'status': 'error', 
                'message': f'缺少必填字段: {", ".join(missing_fields)}'
            }), 400

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # 检查完全匹配的重复记录
            cursor.execute("""
                SELECT unique_id, material_type, inspection_item, inspection_method, inspection_description
                FROM general_inspection_specifications 
                WHERE material_type = %s 
                AND inspection_item = %s
                AND inspection_method = %s
                AND inspection_description = %s
            """, (
                data['material_type'], 
                data['inspection_item'],
                data['inspection_method'],
                data['inspection_description']
            ))
            
            duplicate = cursor.fetchone()
            if duplicate:
                return jsonify({
                    'status': 'duplicate',
                    'message': f'已存在完全相同的记录，唯一码为：{duplicate["unique_id"]}',
                    'duplicate_info': duplicate
                }), 409

            # 生成唯一ID
            today = datetime.now().strftime('%y%m%d')
            prefix = f"GIS{today}"
            
            # 查询当天最大序号
            cursor.execute(
                "SELECT MAX(CAST(SUBSTRING(unique_id, 10) AS UNSIGNED)) as max_seq "
                "FROM general_inspection_specifications WHERE unique_id LIKE %s",
                (f"{prefix}%",)
            )
            result = cursor.fetchone()
            max_seq = result['max_seq'] if result['max_seq'] is not None else 0
            unique_id = f"{prefix}{max_seq + 1:03d}"

            # 处理 is_standard 字段
            is_standard = 'Y' if data.get('is_standard', '').lower() in ['true', '1', 'y'] else 'N'

            # 插入新记录
            cursor.execute("""
                INSERT INTO general_inspection_specifications 
                (unique_id, is_standard, material_type, inspection_item, 
                inspection_method, inspection_description, sampling_level, cr, maj, min)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                unique_id,
                is_standard,
                data['material_type'],
                data['inspection_item'],
                data.get('inspection_method', ''),
                data.get('inspection_description', ''),
                data.get('sampling_level', 'II'),
                data.get('cr'),
                data.get('maj'),
                data.get('min')
            ))
            
            conn.commit()
            
            return jsonify({
                'status': 'success',
                'message': '添加成功',
                'id': cursor.lastrowid,
                'unique_id': unique_id
            }), 201

        except mysql.connector.Error as e:
            conn.rollback()
            print(f"MySQL错误: {e}")
            return jsonify({
                'status': 'error',
                'message': f'数据库错误: {str(e)}'
            }), 500
            
    except Exception as e:
        print(f"添加通用检验规范出错: {e}")
        return jsonify({
            'status': 'error',
            'message': f'系统错误: {str(e)}'
        }), 500
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


def update_unique_id_field():
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("""
            ALTER TABLE general_inspection_specifications 
            MODIFY COLUMN unique_id VARCHAR(20) NOT NULL
        """)
        conn.commit()
        print("unique_id 字段长度已更新")
    except mysql.connector.Error as err:
        print(f"修改字段长度失败: {err}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

# ===== AQL 设置路由 =====
@app.route('/api/aql_settings', methods=['GET'])
def get_aql_settings():
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')
        
        # 构建搜索条件
        search_conditions = []
        search_values = []
        
        # 处理多个搜索条件
        i = 0
        while True:
            field = request.args.get(f'search_field_{i}')
            value = request.args.get(f'search_value_{i}')
            
            if not field or not value:
                break
                
            search_conditions.append(f"{field} LIKE %s")
            search_values.append(f"%{value}%")
            i += 1
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 修改查询语句，使用 CAST 和 DECIMAL 来确保精确的小数位数
        query = """
            SELECT 
                id,
                unique_id,
                material_type,
                inspection_item,
                sampling_level,
                CASE 
                    WHEN cr IS NULL THEN ''
                    ELSE CAST(CAST(cr AS DECIMAL(10,3)) AS CHAR)
                END as cr,
                CASE 
                    WHEN maj IS NULL THEN ''
                    ELSE CAST(CAST(maj AS DECIMAL(10,3)) AS CHAR)
                END as maj,
                CASE 
                    WHEN min IS NULL THEN ''
                    ELSE CAST(CAST(min AS DECIMAL(10,3)) AS CHAR)
                END as min
            FROM aql_settings
        """
        if search_conditions:
            query = f"SELECT * FROM ({query}) as t WHERE " + " AND ".join(search_conditions)
        query += f" ORDER BY {sort_field} {sort_order}"
        
        # 获取总记录数
        count_query = f"SELECT COUNT(*) as total FROM ({query}) as t"
        cursor.execute(count_query, search_values)
        total_records = cursor.fetchone()['total']
        
        # 添加分页
        query += " LIMIT %s OFFSET %s"
        search_values.extend([page_size, (page - 1) * page_size])
        
        cursor.execute(query, search_values)
        settings = cursor.fetchall()
        
        total_pages = (total_records + page_size - 1) // page_size
        
        # 处理返回的数值，确保保留必要的小数位
        for setting in settings:
            for field in ['cr', 'maj', 'min']:
                if setting[field]:
                    # 如果是标准的AQL值，保持原样
                    std_values = ['0.010', '0.015', '0.025', '0.040', '0.065', 
                                '0.10', '0.15', '0.25', '0.40', '0.65',
                                '1.0', '1.5', '2.5', '4.0', '6.5', '10.0']
                    value = setting[field]
                    # 如果不是标准值，确保正确的格式
                    if value not in std_values:
                        try:
                            # 转换为Decimal以保持精度
                            d = Decimal(value)
                            # 格式化为字符串，保留3位小数
                            setting[field] = format(d, '.3f').rstrip('0').rstrip('.')
                        except:
                            pass  # 如果转换失败，保持原值
        
        return jsonify({
            'settings': settings,
            'total_pages': total_pages,
            'current_page': page
        })
        
    except Exception as e:
        print(f"获取 AQL 设置出错: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/aql_settings', methods=['POST'])
def create_aql_setting():
    try:
        data = request.get_json()
        
        # 转换数值类型
        cr = float(data['cr']) if data['cr'] else None
        maj = float(data['maj']) if data['maj'] else None
        min = float(data['min']) if data['min'] else None
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO aql_settings 
            (unique_id, material_type, inspection_item, sampling_level, cr, maj, min)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            data['unique_id'],
            data['material_type'],
            data['inspection_item'],
            data['sampling_level'],
            cr,
            maj,
            min
        ))
        
        conn.commit()
        return jsonify({'status': 'success', 'message': '保存成功'})
    except Exception as e:
        print(f"创建AQL设置出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/aql_settings/<int:id>', methods=['PUT'])
def update_aql_setting(id):
    try:
        data = request.get_json()
        # 转换数值类型
        cr = float(data['cr']) if data['cr'] else None
        maj = float(data['maj']) if data['maj'] else None
        min = float(data['min']) if data['min'] else None
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE aql_settings 
            SET material_type = %s,
                inspection_item = %s,
                sampling_level = %s,
                cr = %s,
                maj = %s,
                min = %s
            WHERE id = %s
        """, (
            data['material_type'],
            data['inspection_item'],
            data['sampling_level'],
            cr,
            maj,
            min,
            id
        ))
        
        conn.commit()
        return jsonify({'status': 'success', 'message': '更新成功'})
    except Exception as e:
        print(f"更新AQL设置出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/aql_settings/<int:id>', methods=['DELETE'])
def delete_aql_setting(id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM aql_settings WHERE id = %s", (id,))
        conn.commit()
        return jsonify({'status': 'success', 'message': '删除成功'}), 200
    except Exception as e:
        print(f"删除AQL设置出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

def generate_aql_id():
    today = datetime.now().strftime('%y%m%d')
    prefix = f"AQL{today}"
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute(
            "SELECT MAX(CAST(SUBSTRING(unique_id, 10) AS UNSIGNED)) "
            "FROM aql_settings WHERE unique_id LIKE %s",
            (f"{prefix}%",)
        )
        max_seq = cursor.fetchone()[0] or 0
        return f"{prefix}{max_seq + 1:03d}"
    except Exception as e:
        print(f"生成AQL ID失败: {e}")
        return f"{prefix}001"
    finally:
        cursor.close()
        conn.close()

# app.py 修改 update_general_inspection_specification 函数
@app.route('/api/general_inspection_specifications/<int:id>', methods=['PUT'])
def update_general_inspection_specification(id):
    try:
        data = request.get_json()
        conn = get_db_connection()
        cursor = conn.cursor()

        # 处理 is_standard 字段
        is_standard = 'Y' if data.get('is_standard', '').lower() in ['true', '1', 'y'] else 'N'
        
        cursor.execute("""
            UPDATE general_inspection_specifications 
            SET is_standard = %s,
                material_type = %s,
                inspection_item = %s,
                inspection_method = %s,
                inspection_description = %s,
                sampling_level = %s,
                cr = %s,
                maj = %s,
                min = %s
            WHERE id = %s
        """, (
            is_standard,
            data['material_type'],
            data['inspection_item'],
            data['inspection_method'],
            data['inspection_description'],
            data['sampling_level'],
            data['cr'],
            data['maj'],
            data['min'],
            id
        ))
        
        conn.commit()
        return jsonify({'message': '更新成功'})
    except Exception as e:
        print(f"更新通用检验规范出错: {e}")
        return jsonify({'message': f'更新失败: {str(e)}'}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/api/general_inspection_specifications/<int:id>', methods=['DELETE'])
def delete_general_inspection_specification(id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM general_inspection_specifications WHERE id = %s", (id,))
        conn.commit()
        
        return jsonify({'message': '删除成功'})
    except Exception as e:
        print(f"删除通用检验规范出错: {e}")
        return jsonify({'message': f'删除失败: {str(e)}'}), 500
    finally:
        cursor.close()
        conn.close()

# 将这个路由移到其他 API 路由的位置
@app.route('/api/general_inspection_specifications/max_id', methods=['GET'])
def get_max_id():
    try:
        prefix = request.args.get('prefix')  # 格式应该是 GISyyMMdd
        if not prefix or len(prefix) != 9:  # GIS + 6位日期
            return jsonify({'error': '无效的前缀格式'}), 400
            
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询指定日期前缀的最大序号
        query = """
            SELECT MAX(CAST(RIGHT(unique_id, 3) AS UNSIGNED)) as max_id
            FROM general_inspection_specifications 
            WHERE unique_id LIKE %s
        """
        cursor.execute(query, (f"{prefix}%",))
        result = cursor.fetchone()
        max_id = result[0] if result[0] is not None else 0
        
        return jsonify({'max_id': max_id})
        
    except Exception as e:
        print(f"获取最大ID出错: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/aql_settings/match', methods=['GET'])
def match_aql_settings():
    try:
        material_type = request.args.get('material_type')
        inspection_item = request.args.get('inspection_item')
        
        if not material_type or not inspection_item:
            return jsonify({'found': False})
            
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT sampling_level, 
                   CAST(cr AS DECIMAL(10,3)) as cr,
                   CAST(maj AS DECIMAL(10,3)) as maj,
                   CAST(min AS DECIMAL(10,3)) as min
            FROM aql_settings 
            WHERE material_type = %s 
            AND inspection_item = %s
            LIMIT 1
        """, (material_type, inspection_item))
        
        result = cursor.fetchone()
        if result:
            # 格式化数值为标准格式字符串
            def format_aql_value(value):
                if value is None:
                    return ''
                # 转换为字符串并移除末尾的0
                str_val = f"{float(value):.3f}".rstrip('0').rstrip('.')
                # 对于整数，添加.0
                if '.' not in str_val:
                    str_val += '.0'
                return str_val

            return jsonify({
                'found': True,
                'sampling_level': result['sampling_level'],
                'cr': format_aql_value(result['cr']),
                'maj': format_aql_value(result['maj']),
                'min': format_aql_value(result['min'])
            })
        return jsonify({'found': False})
        
    except Exception as e:
        print(f"匹配 AQL 设置出错: {e}")
        return jsonify({'found': False, 'error': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@app.route('/api/aql_settings/generate_id', methods=['GET'])
def generate_aql_id():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        today = datetime.now().strftime('%y%m%d')
        prefix = f"AQL{today}"
        
        # 查询当天最大序号
        cursor.execute("""
            SELECT MAX(SUBSTRING(unique_id, 10)) 
            FROM aql_settings 
            WHERE unique_id LIKE %s
        """, (f"{prefix}%",))
        
        max_seq = cursor.fetchone()[0]
        
        if max_seq and max_seq.isdigit():
            next_seq = int(max_seq) + 1
        else:
            next_seq = 1
            
        unique_id = f"{prefix}{next_seq:03d}"
        
        return jsonify({'unique_id': unique_id})
    except Exception as e:
        print(f"生成AQL ID出错: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@app.route('/api/aql_settings/check_duplicate', methods=['GET'])
def check_duplicate_aql():
    try:
        material_type = request.args.get('material_type')
        inspection_item = request.args.get('inspection_item')
        
        if not all([material_type, inspection_item]):
            return jsonify({
                'status': 'ok',
                'message': '未发现重复记录'
            })
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT material_type, inspection_item
            FROM aql_settings 
            WHERE material_type = %s 
            AND inspection_item = %s
        """
        cursor.execute(query, (material_type, inspection_item))
        
        result = cursor.fetchone()
        
        if result:
            return jsonify({
                'status': 'duplicate',
                'message': '已存在相同的记录',
                'duplicate_info': result
            })
        
        return jsonify({
            'status': 'ok',
            'message': '未发现重复记录'
        })
        
    except Exception as e:
        print(f"检查重复记录时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# ===== 应用启动 =====
if __name__ == '__main__':
    update_unique_id_field()
    app.run(host='0.0.0.0', port=5000, debug=True)