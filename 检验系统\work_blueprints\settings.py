from flask import Blueprint, render_template, request, jsonify
from db_config import get_db_connection
import mysql.connector
from datetime import datetime
import json
import os

# 添加系统相关的导入
import platform
import subprocess
from pathlib import Path

try:
    import pythoncom
    import win32com.client
    from win32com.shell import shell, shellcon
    import win32file
    import win32api
    import win32con
    HAS_WIN32 = True
except ImportError:
    HAS_WIN32 = False
    print("警告: Windows API 模块导入失败，将使用基础文件系统功能")

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/', strict_slashes=False)
def settings():
    return render_template('settings.html')

@settings_bp.route('/save_image_path', methods=['POST'])
def save_image_path():
    try:
        data = request.get_json()
        path = data.get('path')
        
        if not path:
            return jsonify({
                'status': 'error',
                'message': '路径不能为空'
            }), 400
            
        # 确保路径存在
        try:
            os.makedirs(path, exist_ok=True)
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'无法创建路径：{str(e)}'
            }), 500
            
        # 确保配置目录存在
        config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
        os.makedirs(config_dir, exist_ok=True)
        
        config_path = os.path.join(config_dir, 'settings.json')
        
        # 读取现有配置（如果存在）
        config = {}
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
        # 更新图片路径
        config['image_base_path'] = path
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
            
        return jsonify({
            'status': 'success',
            'message': '图片路径保存成功'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@settings_bp.route('/get_image_path', methods=['GET'])
def get_image_path():
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'settings.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                image_path = config.get('image_base_path', 'D:/检验系统图片/')
        else:
            image_path = 'D:/检验系统图片/'
            
        return jsonify({
            'status': 'success',
            'path': image_path
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@settings_bp.route('/api/aql_settings', methods=['GET'])
def get_aql_settings():
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        sort_field = request.args.get('sort_field', 'id')
        sort_order = request.args.get('sort_order', 'asc')
        
        # 构建搜索条件
        search_conditions = []
        search_values = []
        
        # 处理多个搜索条件
        i = 0
        while True:
            field = request.args.get(f'search_field_{i}')
            value = request.args.get(f'search_value_{i}')
            
            if not field or not value:
                break
                
            search_conditions.append(f"{field} LIKE %s")
            search_values.append(f"%{value}%")
            i += 1
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT 
                id,
                unique_id,
                material_type,
                inspection_item,
                sampling_level,
                CASE 
                    WHEN cr IS NULL THEN ''
                    ELSE CAST(CAST(cr AS DECIMAL(10,3)) AS CHAR)
                END as cr,
                CASE 
                    WHEN maj IS NULL THEN ''
                    ELSE CAST(CAST(maj AS DECIMAL(10,3)) AS CHAR)
                END as maj,
                CASE 
                    WHEN min IS NULL THEN ''
                    ELSE CAST(CAST(min AS DECIMAL(10,3)) AS CHAR)
                END as min
            FROM aql_settings
        """
        if search_conditions:
            query = f"SELECT * FROM ({query}) as t WHERE " + " AND ".join(search_conditions)
        query += f" ORDER BY {sort_field} {sort_order}"
        
        # 获取总记录数
        count_query = f"SELECT COUNT(*) as total FROM ({query}) as t"
        cursor.execute(count_query, search_values)
        total_records = cursor.fetchone()['total']
        
        # 添加分页
        query += " LIMIT %s OFFSET %s"
        search_values.extend([page_size, (page - 1) * page_size])
        
        cursor.execute(query, search_values)
        settings = cursor.fetchall()
        
        total_pages = (total_records + page_size - 1) // page_size
        
        # 处理返回的数值，确保保留必要的小数位
        for setting in settings:
            for field in ['cr', 'maj', 'min']:
                if setting[field]:
                    # 如果是标准的AQL值，保持原样
                    std_values = ['0.010', '0.015', '0.025', '0.040', '0.065', 
                                '0.10', '0.15', '0.25', '0.40', '0.65',
                                '1.0', '1.5', '2.5', '4.0', '6.5', '10.0']
                    value = setting[field]
                    # 如果不是标准值，确保正确的格式
                    if value not in std_values:
                        try:
                            # 转换为Decimal以保持精度
                            d = Decimal(value)
                            # 格式化为字符串，保留3位小数
                            setting[field] = format(d, '.3f').rstrip('0').rstrip('.')
                        except:
                            pass  # 如果转换失败，保持原值
        
        return jsonify({
            'settings': settings,
            'total_pages': total_pages,
            'current_page': page
        })
        
    except Exception as e:
        print(f"获取 AQL 设置出错: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@settings_bp.route('/api/aql_settings', methods=['POST'])
def create_aql_setting():
    try:
        data = request.get_json()
        
        # 转换数值类型
        cr = float(data['cr']) if data['cr'] else None
        maj = float(data['maj']) if data['maj'] else None
        min = float(data['min']) if data['min'] else None
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO aql_settings 
            (unique_id, material_type, inspection_item, sampling_level, cr, maj, min)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (
            data['unique_id'],
            data['material_type'],
            data['inspection_item'],
            data['sampling_level'],
            cr,
            maj,
            min
        ))
        
        conn.commit()
        return jsonify({'status': 'success', 'message': '保存成功'})
    except Exception as e:
        print(f"创建AQL设置出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@settings_bp.route('/api/aql_settings/<int:id>', methods=['PUT'])
def update_aql_setting(id):
    try:
        data = request.get_json()
        # 转换数值类型
        cr = float(data['cr']) if data['cr'] else None
        maj = float(data['maj']) if data['maj'] else None
        min = float(data['min']) if data['min'] else None
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            UPDATE aql_settings 
            SET material_type = %s,
                inspection_item = %s,
                sampling_level = %s,
                cr = %s,
                maj = %s,
                min = %s
            WHERE id = %s
        """, (
            data['material_type'],
            data['inspection_item'],
            data['sampling_level'],
            cr,
            maj,
            min,
            id
        ))
        
        conn.commit()
        return jsonify({'status': 'success', 'message': '更新成功'})
    except Exception as e:
        print(f"更新AQL设置出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@settings_bp.route('/api/aql_settings/<int:id>', methods=['DELETE'])
def delete_aql_setting(id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM aql_settings WHERE id = %s", (id,))
        conn.commit()
        return jsonify({'status': 'success', 'message': '删除成功'}), 200
    except Exception as e:
        print(f"删除AQL设置出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@settings_bp.route('/api/aql_settings/match', methods=['GET'])
def match_aql_settings():
    try:
        material_type = request.args.get('material_type')
        inspection_item = request.args.get('inspection_item')
        
        if not material_type or not inspection_item:
            return jsonify({'found': False})
            
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT sampling_level, 
                   CAST(cr AS DECIMAL(10,3)) as cr,
                   CAST(maj AS DECIMAL(10,3)) as maj,
                   CAST(min AS DECIMAL(10,3)) as min
            FROM aql_settings 
            WHERE material_type = %s 
            AND inspection_item = %s
            LIMIT 1
        """, (material_type, inspection_item))
        
        result = cursor.fetchone()
        if result:
            # 格式化数值为标准格式字符串
            def format_aql_value(value):
                if value is None:
                    return ''
                # 转换为字符串并移除末尾的0
                str_val = f"{float(value):.3f}".rstrip('0').rstrip('.')
                # 对于整数，添加.0
                if '.' not in str_val:
                    str_val += '.0'
                return str_val

            return jsonify({
                'found': True,
                'sampling_level': result['sampling_level'],
                'cr': format_aql_value(result['cr']),
                'maj': format_aql_value(result['maj']),
                'min': format_aql_value(result['min'])
            })
        return jsonify({'found': False})
        
    except Exception as e:
        print(f"匹配 AQL 设置出错: {e}")
        return jsonify({'found': False, 'error': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@settings_bp.route('/api/aql_settings/generate_id', methods=['GET'])
def generate_aql_id():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        today = datetime.now().strftime('%y%m%d')
        prefix = f"AQL{today}"
        
        # 查询当天最大序号
        cursor.execute("""
            SELECT MAX(SUBSTRING(unique_id, 10)) 
            FROM aql_settings 
            WHERE unique_id LIKE %s
        """, (f"{prefix}%",))
        
        max_seq = cursor.fetchone()[0]
        
        if max_seq and max_seq.isdigit():
            next_seq = int(max_seq) + 1
        else:
            next_seq = 1
            
        unique_id = f"{prefix}{next_seq:03d}"
        
        return jsonify({'unique_id': unique_id})
    except Exception as e:
        print(f"生成AQL ID出错: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@settings_bp.route('/api/aql_settings/check_duplicate', methods=['GET'])
def check_duplicate_aql():
    try:
        material_type = request.args.get('material_type')
        inspection_item = request.args.get('inspection_item')
        
        if not all([material_type, inspection_item]):
            return jsonify({
                'status': 'ok',
                'message': '未发现重复记录'
            })
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        query = """
            SELECT material_type, inspection_item
            FROM aql_settings 
            WHERE material_type = %s 
            AND inspection_item = %s
        """
        cursor.execute(query, (material_type, inspection_item))
        
        result = cursor.fetchone()
        
        if result:
            return jsonify({
                'status': 'duplicate',
                'message': '已存在相同的记录',
                'duplicate_info': result
            })
        
        return jsonify({
            'status': 'ok',
            'message': '未发现重复记录'
        })
        
    except Exception as e:
        print(f"检查重复记录时出错: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_drives():
    """获取系统驱动器列表"""
    try:
        if not HAS_WIN32:
            # 如果没有 win32api，使用基本方法
            drives = []
            for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
                drive = f"{letter}:/"
                if os.path.exists(drive):
                    drives.append({
                        'path': drive,
                        'name': drive,
                        'type': 'drive'
                    })
            return drives
            
        drives = []
        drive_bits = win32file.GetLogicalDrives()
        for letter in range(65, 91):  # A-Z
            if drive_bits & (1 << (letter - 65)):
                drive = chr(letter) + ':/'
                try:
                    drive_type = win32file.GetDriveType(drive)
                    if drive_type in [win32con.DRIVE_FIXED, win32con.DRIVE_REMOVABLE]:
                        drive_name = ''
                        try:
                            volume_name = win32api.GetVolumeInformation(drive)[0]
                            drive_name = volume_name if volume_name else ''
                        except:
                            pass
                        drives.append({
                            'path': drive,
                            'name': f"{drive} {drive_name}".strip(),
                            'type': 'drive'
                        })
                except Exception as e:
                    print(f"获取驱动器信息出错 {drive}: {str(e)}")
                    continue
        return drives
    except Exception as e:
        print(f"获取驱动器列表出错: {str(e)}")
        return []

def get_special_folders():
    """获取系统特殊文件夹"""
    special_folders = {}
    try:
        if not HAS_WIN32:
            # 如果没有 win32api，使用基本方法
            home = str(Path.home())
            basic_folders = {
                'Desktop': os.path.join(home, 'Desktop'),
                'Documents': os.path.join(home, 'Documents'),
                'Downloads': os.path.join(home, 'Downloads'),
                'Pictures': os.path.join(home, 'Pictures')
            }
            return {k: v for k, v in basic_folders.items() if os.path.exists(v)}
            
        # 初始化 COM
        pythoncom.CoInitialize()
        try:
            # 使用 shell.SHGetFolderPath 获取特殊文件夹
            special_folders = {
                'Desktop': shell.SHGetFolderPath(0, shellcon.CSIDL_DESKTOP, 0, 0),
                'Documents': shell.SHGetFolderPath(0, shellcon.CSIDL_PERSONAL, 0, 0),
                'Pictures': shell.SHGetFolderPath(0, shellcon.CSIDL_MYPICTURES, 0, 0)
            }
            
            # 下载文件夹需要特殊处理
            try:
                downloads = os.path.join(os.path.expanduser('~'), 'Downloads')
                if os.path.exists(downloads):
                    special_folders['Downloads'] = downloads
            except:
                pass
                
            # 只返回存在的文件夹
            return {k: v for k, v in special_folders.items() if os.path.exists(v)}
            
        finally:
            pythoncom.CoUninitialize()
                
    except Exception as e:
        print(f"获取特殊文件夹列表出错: {str(e)}")
        return {}

@settings_bp.route('/browse_folder', methods=['GET'])
def browse_folder():
    try:
        current_path = request.args.get('path', '')
        print(f"请求浏览路径: {current_path}")
        
        # 如果没有指定路径，显示驱动器列表和特殊文件夹
        if not current_path:
            # 获取驱动器列表
            drives = get_drives()
            print(f"获取到的驱动器: {drives}")
            
            # 获取特殊文件夹
            special_folders = get_special_folders()
            print(f"获取到的特殊文件夹: {special_folders}")
            
            folders = [{'path': path, 'name': name, 'type': 'special'} 
                      for name, path in special_folders.items()]
                      
            return jsonify({
                'status': 'success',
                'current_path': '',
                'parent_path': None,
                'drives': drives,
                'folders': folders,
                'is_root': True
            })
            
        # 如果指定了路径，显示该路径下的文件夹
        if os.path.exists(current_path):
            folders = []
            try:
                for item in os.listdir(current_path):
                    full_path = os.path.join(current_path, item)
                    if os.path.isdir(full_path):
                        try:
                            # 检查是否为隐藏文件夹
                            is_hidden = False
                            if HAS_WIN32:
                                try:
                                    attrs = win32api.GetFileAttributes(full_path)
                                    is_hidden = bool(attrs & win32con.FILE_ATTRIBUTE_HIDDEN)
                                except:
                                    pass
                            
                            if not is_hidden:
                                folders.append({
                                    'path': full_path,
                                    'name': item,
                                    'type': 'folder'
                                })
                        except Exception as e:
                            print(f"处理文件夹出错 {full_path}: {str(e)}")
                            continue
                            
                # 获取父目录
                parent_path = str(Path(current_path).parent)
                if parent_path == current_path:  # 如果是根目录
                    parent_path = None
                    
                return jsonify({
                    'status': 'success',
                    'current_path': current_path,
                    'parent_path': parent_path,
                    'folders': sorted(folders, key=lambda x: x['name'].lower()),
                    'is_root': False
                })
                
            except PermissionError:
                print(f"访问文件夹权限不足: {current_path}")
                return jsonify({
                    'status': 'error',
                    'message': '没有权限访问该文件夹'
                }), 403
                
        print(f"请求的路径不存在: {current_path}")
        return jsonify({
            'status': 'error',
            'message': '指定的路径不存在'
        }), 404
        
    except Exception as e:
        print(f"浏览文件夹时出错: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f"浏览文件夹时出错: {str(e)}"
        }), 500

@settings_bp.route('/save_image_reordering', methods=['POST'])
def save_image_reordering():
    try:
        data = request.get_json()
        enable_reordering = data.get('enable_image_reordering')
        
        if enable_reordering not in ['yes', 'no']:
            return jsonify({
                'status': 'error',
                'message': '无效的设置值，必须是"yes"或"no"'
            }), 400
            
        # 确保配置目录存在
        config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
        os.makedirs(config_dir, exist_ok=True)
        
        config_path = os.path.join(config_dir, 'settings.json')
        
        # 读取现有配置（如果存在）
        config = {}
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
        # 更新图片重排序设置
        config['enable_image_reordering'] = enable_reordering
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
            
        return jsonify({
            'status': 'success',
            'message': '图片重排序设置保存成功'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@settings_bp.route('/init_config', methods=['GET'])
def init_config():
    try:
        config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
        config_path = os.path.join(config_dir, 'settings.json')
        
        # 如果配置目录不存在，创建它
        os.makedirs(config_dir, exist_ok=True)
        
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(config_path):
            default_config = {
                'image_base_path': 'D:/检验系统图片/',
                'enable_image_reordering': 'no'
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=4)
                
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 确保图片基础路径存在
        os.makedirs(config['image_base_path'], exist_ok=True)
        
        # 确保配置包含enable_image_reordering字段
        if 'enable_image_reordering' not in config:
            config['enable_image_reordering'] = 'no'
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
        return jsonify({
            'status': 'success',
            'config': config
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500 