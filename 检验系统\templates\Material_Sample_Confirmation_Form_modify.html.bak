<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>物料样板确认书</title>
    <style>
        .image-upload-cell 
        .image-upload-box{
            height: 120px;
            position: relative;
        }
        body {
            width: 1000px;  /* 根据实际需求调整 */
            margin: 0 auto;
            font-family: "Microsoft YaHei"; 
            font-size: 12px;
        }
        table {
            table-layout: fixed;  /* 固定表格布局 */
            width: 100%;
            max-width: 1000px;
            border-collapse: collapse;
            border: 2px solid #000000;
            border-spacing: 0; /* 显式声明 */
        }
        td input[type="text"] {
            width: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
            height: 100%;
            border: none;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border: 2px solid #000000;
            border-radius: 5px;
        }
        td input[type="text"] {
            height: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border-radius: 5px;
        }
        td {
            border: 1px solid #000000;
            padding: 2px 1px;
            vertical-align: middle;
            line-height: 1.2;
        }
        .main-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            background: #D9D9D9;
            padding: 8px 0;
        }
        .module-title {
            background: #F2F2F2;
            font-weight: bold;
            padding-left: 4px;
        }
        .sub-header {
            background: #E6E6E6;
        }
        .col-no { width: 35px; }
        .col-pos { width: 90px; }
        .col-value { width: 55px; }
        .col-measure { width: 35px; }
        .col-check { width: 70px; }
        .col-note { width: 100px; }
        .align-center { text-align: center; }
        .align-left { text-align: left; padding-left: 4px; }
        .check-box {
            width: 14px;
            height: 14px;
            border: 1px solid #000;
            display: inline-block;
            margin: 0 5px;
            vertical-align: middle;
        }
        input[type="checkbox"] {
            margin: 0 3px 0 10px;
        }

        .custom-row-height {
            width: 100%; 
            border-collapse: collapse;
            table-layout: fixed;
            border: none;
            border-spacing: 0; /* 显式声明 */
            height: 10px; /* 根据需要调整高度 */
        }

        .custom-row-height td {
            height: 10px; /* 确保单元格的高度与行高一致 */
            vertical-align: middle;
        }
        textarea {
            /* border: none !important; /* 移除边框 
            resize: none !important;; 
            overflow: auto !important;;
            outline: none !important;;*/
            width: 100%; /* 输入框宽度与列宽一致 */
            height: auto; /* 自动调整高度 */
            min-height: 50px; /* 最小高度 */
            max-height: 200px; /* 最大高度 */
            padding: 4px 3px; /* 与td padding保持一致 */
            margin: 0;
            font-size: 13px;
            box-sizing: border-box !important;; /* 包含内边距和边框 */
            border: 1px solid transparent!important;; /* 默认无边框 */
            resize: vertical!important;; /* 允许垂直调整大小 */
            overflow-y: auto!important;; /* 允许垂直滚动 */
            outline: none!important;; /* 移除焦点时的轮廓 */
        }
        textarea:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        textarea[name^="appearance_"][name$="_note"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important;
        }

        textarea[name^="appearance_"][name$="_note"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }       

        textarea[name^="function_"][name$="_note"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important;
        }

        textarea[name^="function_"][name$="_note"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        textarea[name^="function_"][name$="_other"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 58px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important; /* 移除边框 */;
        }

        textarea[name^="function_"][name$="_other"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        input[name="review"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important; /* 移除边框 */
            border-radius: 5px;
        }
        .checkbox-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .col-compliance {
            width: 100px;  /* 根据需求调整具体数值 */
            text-align: center;
        }
        label {
            margin-left: 1px; /* 文字与复选框的间距 */
            cursor: pointer; /* 鼠标悬停时显示手型 */
            user-select: none; /* 禁止文字被选中 */
        }
        /* 图片上传样式 */
        .image-upload-box {
            position: relative;
            width: 100%;
            height: 80px;
            border: 2px dashed #ccc;
            cursor: pointer;
            transition: all 0.2s ease;
            pointer-events: none; /* 禁用容器本身的点击事件 */
            background-color: #f9f9f9; /* 添加背景色 */
        }
        .image-upload-box * {
            pointer-events: auto; /* 允许子元素接收点击事件 */
        }

        .image-upload-box:hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        .image-upload-box.drag-over {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
            box-shadow: 0 0 8px rgba(0, 123, 255, 0.5);
        }

        .image-upload-box.active-hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        /* 删除按钮样式修改 */
        .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10; /* 提高z-index确保按钮在最上层 */
            opacity: 0.5;
            transition: all 0.3s ease-in-out;
            pointer-events: auto; /* 确保按钮可以接收点击事件 */
            overflow: hidden;
            font-size: 14px;
            text-align: center;
            padding: 0;
            white-space: nowrap;
            box-sizing: border-box;
        }
        
        .delete-btn:hover {
            background: #ff0000;
            opacity: 1;
            width: 50px;
            border-radius: 15px;
            padding: 0 5px;
            justify-content: center;
            font-size: 12px; /* 调整字体大小使删除文字完全显示 */
            line-height: 20px;
        }

        /* 移除伪元素内容 */
        /*/*.delete-btn::before {
            content: "×";
        }

        .delete-btn:hover::before {
            content: "删除";
        }*/

        .preview-image {
            width: 100%;
            height: 100%; /* 根据图片比例自动调整 */
            object-fit: contain; /* 保持图片比例 */
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2; /* 设置图片层级低于删除按钮 */
        }

        .upload-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            white-space: nowrap;
            font-size: 14px;
            text-align: center;
            width: 90%;
            font-weight: bold;
        }

        .upload-shortcut {
            display: block;
            font-size: 10px;
            color: #999;
            margin-top: 4px;
        }

        .image-upload-box.uploading .upload-overlay {
            content: '上传中...';
        }

        .image-note {
            width: 100%;
            height: 20px;
            margin-top: 2px;
            font-size: 12px;
        }

        input[type="file"] {
            display: none;
        }
        .image-upload-box.uploading::after {
            content: "处理中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 14px;
            z-index: 6;
        }
        /* 可选：保持复选框样式 */
        input[name="final_judgment"] {
            display: none;
        }
        input[name="final_judgment"] + label {
            padding: 4px 8px;
            border: 1px solid #999;
            border-radius: 4px;
            cursor: pointer;
        }
        input[name="final_judgment"]:checked + label {
            background-color: #007bff;
            color: white;
        }
 
        .image-upload-box.has-image .delete-btn {
            display: block;
            opacity: 0.5;
        }

        .image-upload-box.has-image:hover .delete-btn {
            opacity: 1;
        }

        .delete-btn:hover {
            opacity: 1; /* 鼠标悬停在按钮上时完全不透明 */
        }

        .feedback-message {
            position: fixed;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 4px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
            min-width: 200px;
            max-width: 80%;
        }

        .feedback-message.show {
            opacity: 1;
        }

        /* 添加图片压缩功能 */
        .image-upload-box.uploading::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 5;
        }

        .image-upload-box.uploading::after {
            content: "处理中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 14px;
            z-index: 6;
        }

        .paste-button {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: rgba(0, 123, 255, 0.85);
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 3;
            opacity: 0.9;
            display: block; /* 改为一直显示 */
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: 28px; /* 初始状态只显示图标的宽度 */
            text-align: center; /* 文字居中 */
            overflow: hidden; /* 隐藏溢出内容 */
            white-space: nowrap; /* 防止文字换行 */
        }

        .copy-button {
            position: absolute;
            bottom: 33px; /* 位于粘贴按钮上方 */
            right: 5px;
            background: rgba(255, 165, 0, 0.85); /* 橙色背景 */
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 3;
            opacity: 0.9;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: 28px; /* 初始状态只显示图标的宽度 */
            text-align: center; /* 文字居中 */
            overflow: hidden; /* 隐藏溢出内容 */
            white-space: nowrap; /* 防止文字换行 */
        }

        .upload-button {
            position: absolute;
            bottom: 61px; /* 增加与复制按钮的间隙 */
            right: 5px;
            background: rgba(76, 175, 80, 0.85);
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 3;
            opacity: 0.9;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            width: 28px; /* 初始状态只显示图标的宽度 */
            text-align: center; /* 文字居中 */
            overflow: hidden; /* 隐藏溢出内容 */
            white-space: nowrap; /* 防止文字换行 */
        }

        /* 移除悬停在单元格上时按钮展开的样式 */
        .image-upload-box:hover .paste-button {
            opacity: 1; /* 只改变透明度 */
        }

        .image-upload-box:hover .copy-button {
            opacity: 1; /* 只改变透明度 */
        }

        .image-upload-box:hover .upload-button {
            opacity: 1;
        }

        .image-upload-box.has-image .paste-button {
            bottom: 5px; /* 保持在底部位置 */
        }

        .image-upload-box.has-image .copy-button {
            bottom: 33px; /* 与粘贴按钮保持一致的间隙 */
        }

        .image-upload-box.has-image .upload-button {
            bottom: 61px; /* 与复制按钮保持一致的间隙 */
        }

        /* 仅当悬停在按钮本身上时才展开 */
        .paste-button:hover {
            opacity: 1;
            background: rgba(0, 123, 255, 1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            width: 70px; /* 悬停按钮本身时展开 */
        }

        .copy-button:hover {
            opacity: 1;
            background: rgba(255, 165, 0, 1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            width: 70px; /* 悬停按钮本身时展开 */
        }

        .upload-button:hover {
            opacity: 1;
            background: rgba(76, 175, 80, 1);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            width: 70px; /* 悬停按钮本身时展开 */
        }

        /* 为按钮添加图标内容 */
        /*
        .paste-button::before {
            content: "📋";
        }

        .paste-button:hover::before {
            content: "📋 粘贴";
        }

        .upload-button::before {
            content: "📂";
        }

        .upload-button:hover::before {
            content: "📂 上传";
        }
        */

        /* 强制显示尺寸1-4行 */
        #size-row-1, #size-row-2, #size-row-3, #size-row-4 {
            display: table-row !important;
        }
        
        /* 尺寸行增加/删除按钮样式 */
        #add-size-row, #delete-size-row {
            font-size: 14px;
            font-weight: 600;
            padding: 6px 22px;
            margin: 0 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }
        
        #add-size-row {
            background-color: #4CAF50;
            color: white;
        }
        
        #delete-size-row {
            background-color: #f44336;
            color: white;
        }
        
        #add-size-row:hover, #delete-size-row:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        #add-size-row:active, #delete-size-row:active {
            transform: translateY(1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        #add-size-row:hover {
            background-color: #45a049;
        }
        
        #delete-size-row:hover {
            background-color: #e53935;
        }
        
        #add-size-row::before, #delete-size-row::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.7s ease;
        }
        
        #add-size-row:hover::before, #delete-size-row:hover::before {
            left: 100%;
        }

        /* 更新强制显示样式 - 不再基于行号，而是使用新的逻辑 */
        tr[id^="size-row-"] {
            display: table-row !important; /* 所有尺寸行默认显示 */
        }

        tr.confirmed-empty-row {
            display: none !important; /* 只有明确标记为空的行才隐藏 */
        }
        
        /* 强制显示前3行 */
        #size-row-1, #size-row-2, #size-row-3 {
            display: table-row !important;
        }
        
        /* 确保第4行和第5行的样式与其他行一致 */
        #size-row-4, #size-row-5 {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        /* 确保第4行和第5行的单元格样式与其他行一致 */
        #size-row-4 td, #size-row-5 td {
            border: 1px solid #000000;
            padding: 2px 1px;
            vertical-align: middle;
            line-height: 1.2;
        }
    </style>
    <style>
        /* 图片放大模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            cursor: zoom-in; /* 初始光标 */
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .image-modal.show {
            opacity: 1;
        }
        .image-modal.grabbing {
            cursor: grabbing !important;
        }

        /* 图片容器 */
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            max-width: 90%;
            max-height: 90%;
            border: 2px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, filter 0.3s ease;
            filter: blur(2px);
        }
        .modal-content.show {
            transform: translate(-50%, -50%) scale(1);
            filter: blur(0);
        }
        .modal-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s linear infinite;
            z-index: 1001;
            display: none;
        }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
            z-index: 1002;
        }
        .close:hover {
            opacity: 1;
        }
        .image-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 20px;
            padding: 5px 15px;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .image-modal:hover .image-controls {
            opacity: 0.7;
        }
        .image-controls:hover {
            opacity: 1 !important;
        }
        .control-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            margin: 0 10px;
            cursor: pointer;
            padding: 5px 10px;
        }
        .control-btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-radius: 4px;
        }
        .tooltip {
            position: absolute;
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <form id="material-form" method="POST" action="/Material_Sample_Confirmation_Form_modify/update" enctype="multipart/form-data">
        <table>
            <!-- 主标题 -->
            <tr>
                <td colspan="14" class="main-title">物料样板确认书
                    <button type="button" id="submit-btn" style="float: right; margin-right: 20px;" onclick="submitFormToCorrectUrl(event)">保存修改</button>
                </td>
            </tr>
            <!-- 报告编码 -->
            <tr>
                <td colspan="14">报告编码: <span id="report-code">{{ report_code }}</span></td>
            </tr>   

            <!-- 供应商信息 -->
            <tr>
                <td colspan="3" class="align-left">供应商/车间<br>（代理商要注明原厂）</td>
                <td colspan="3" class="align-center">
                    <input type="text" name="supplier" value="{{ supplier }}">
                </td>
                <td colspan="2" class="align-center">送检日期</td>
                <td colspan="2" class="align-center">
                    <input type="date" name="inspection_date" value="{{ inspection_date }}">
                </td>
                <td class="align-center">样板数量</td>
                <td class="align-center">
                    <input type="text" name="sample_count" value="{{ sample_count }}">
                </td>
                <td class="align-center">检验员</td>
                <td class="align-center">
                    <input type="text" name="inspector" value="{{ inspector }}">
                </td>
            </tr>

            <!-- 物料信息行 -->
            <tr>
                <td colspan="3">物料料号</td>          
                <td colspan="3">图号</td>
                <td colspan="3">物料名称</td>
                <td colspan="1">图纸版本</td>
                <td colspan="2"> 材质</td>
                <td colspan="2">表面处理</td>
            </tr>

            <!-- 物料信息输入框 -->
            <tr>
                <td colspan="3" style="height: 20px;">
                    <input type="text" name="material_number" id="material-number" value="{{ material_number }}">
                </td>
                <td colspan="3" style="height: 20px;">
                    <input type="text" name="graph_number" value="{{ graph_number }}">
                </td>
                <td colspan="3" style="height: 20px;">
                    <input type="text" name="material_name" value="{{ material_name }}">
                </td>
                <td colspan="1" style="height: 20px;">
                    <input type="text" name="drawing_version" value="{{ drawing_version }}">
                </td>
                <td colspan="2" style="height: 20px;">
                    <input type="text" name="material_texture" value="{{ material_texture }}">
                </td>
                <td colspan="2" style="height: 20px;">
                    <input type="text" name="surface_processing" value="{{ surface_processing }}">
                </td>
            </tr>

            <!-- 样板状态 -->
            <tr>
                <td colspan="14" class="align-left" style="height: 25px;">
                    样板提供状态：
                    <!-- 复选框动态选中 -->
                    <input type="checkbox" id="trial-sample" name="sample_status[]" value="试产样品"
                        {{ 'checked' if '试产样品' in sample_status_list else '' }}>
                    <label for="trial-sample">试产样品</label>

                    <input type="checkbox" id="big-sample" name="sample_status[]" value="大货样品"
                        {{ 'checked' if '大货样品' in sample_status_list else '' }}>
                    <label for="big-sample">大货样品</label>

                    <input type="checkbox" id="template" name="sample_status[]" value="重制样板"
                        {{ 'checked' if '重制样板' in sample_status_list else '' }}>
                    <label for="template">重制样板</label>

                    <input type="checkbox" id="other" name="sample_status[]" value="其他"
                        {{ 'checked' if '其他' in sample_status_list else '' }}>
                    <label for="other">其他：</label>

                    <!-- 文本框显示和内容填充 -->
                    <input type="text" id="other_textbox" name="other_textbox"
                        data-display="{{ 'true' if '其他' in sample_status_list else 'false' }}"
                        value="{{ other_textbox_value }}">
                    
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // 初始化显示状态
                            const textbox = document.getElementById('other_textbox');
                            const shouldDisplay = textbox.getAttribute('data-display') === 'true';
                            textbox.style.display = shouldDisplay ? 'inline-block' : 'none';
                            
                            // 监听复选框变化
                            const checkbox = document.getElementById('other');
                            checkbox.addEventListener('change', function() {
                                textbox.style.display = this.checked ? 'inline-block' : 'none';
                            });
                        });
                    </script>
                </td>
            </tr>

            <!-- ================= 尺寸检查 ================= -->
            <tr class="module-title">
                <td id="size-section-cell" rowspan="{% if size_row_count < 3 %}5{% else %}{{ size_row_count + 2 }}{% endif %}" style="width: 30px;">一、尺寸</td>
                <td colspan="5" class="align-center">图纸尺寸</td>
                <td colspan="5" class="align-center">实测尺寸(mm)</td>
                <td colspan="1" class="col-compliance-size align-center" rowspan="2">符合性</td>
                <td colspan="2" class="align-center" rowspan="2">注意事项</td>
            </tr>
            <tr class="sub-header">
                <td class="col-no align-center">NO</td>
                <td class="col-pos">位置</td>
                <td class="col-value">标准值</td>
                <td class="col-value">最小值</td>
                <td class="col-value">最大值</td>
                <td class="col-measure">1</td>
                <td class="col-measure">2</td>
                <td class="col-measure">3</td>
                <td class="col-measure">4</td>
                <td class="col-measure">5</td>
            </tr>

            <!-- 尺寸数据行（固定前三条） -->
            <tr class="custom-row-height" id="size-row-1" style="display: table-row !important;">
                <td class="align-center">1</td>
                <td data-id="size-1" data-image="true" class="align-center">
                    <input type="text" name="size_1_position" id="size-1" value="{{ size_1_position }}">
                </td>
                <td class="align-center"><input type="text" name="size_1_value" id="size-1-value" value="{{ size_1_value }}"></td>
                <td class="align-center"><input type="text" name="size_1_min" id="size-1-min" value="{{ size_1_min }}"></td>
                <td class="align-center"><input type="text" name="size_1_max" id="size-1-max" value="{{ size_1_max }}"></td>
                <td class="align-center"><input type="text" name="size_1_measure_1" id="size-1-measure-1" value="{{ size_1_measure_1 }}"></td>
                <td class="align-center"><input type="text" name="size_1_measure_2" id="size-1-measure-2" value="{{ size_1_measure_2 }}"></td>
                <td class="align-center"><input type="text" name="size_1_measure_3" id="size-1-measure-3" value="{{ size_1_measure_3 }}"></td>
                <td class="align-center"><input type="text" name="size_1_measure_4" id="size-1-measure-4" value="{{ size_1_measure_4 }}"></td>
                <td class="align-center"><input type="text" name="size_1_measure_5" id="size-1-measure-5" value="{{ size_1_measure_5 }}"></td>
                <td>
                    <label><input type="radio" name="size_1_check" value="合格" {{ 'checked' if size_1_check == '合格' else '' }}> 合格</label>
                    <label><input type="radio" name="size_1_check" value="不合格" {{ 'checked' if size_1_check == '不合格' else '' }}> 不合格</label>
                    <label><input type="radio" name="size_1_check" value="AOD" {{ 'checked' if size_1_check == 'AOD' else '' }}> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_1_note">{{ size_1_note }}</textarea></td>
            </tr>

            <!-- 更多尺寸行 - 第2行 -->
            <tr class="custom-row-height" id="size-row-2" style="display: table-row !important;">
                <td class="align-center">2</td>
                <td data-id="size-2" data-image="true" class="align-center">
                    <input type="text" name="size_2_position" id="size-2" value="{{ size_2_position }}">
                </td>
                <td class="align-center"><input type="text" name="size_2_value" id="size-2-value" value="{{ size_2_value }}"></td>
                <td class="align-center"><input type="text" name="size_2_min" id="size-2-min" value="{{ size_2_min }}"></td>
                <td class="align-center"><input type="text" name="size_2_max" id="size-2-max" value="{{ size_2_max }}"></td>
                <td class="align-center"><input type="text" name="size_2_measure_1" id="size-2-measure-1" value="{{ size_2_measure_1 }}"></td>
                <td class="align-center"><input type="text" name="size_2_measure_2" id="size-2-measure-2" value="{{ size_2_measure_2 }}"></td>
                <td class="align-center"><input type="text" name="size_2_measure_3" id="size-2-measure-3" value="{{ size_2_measure_3 }}"></td>
                <td class="align-center"><input type="text" name="size_2_measure_4" id="size-2-measure-4" value="{{ size_2_measure_4 }}"></td>
                <td class="align-center"><input type="text" name="size_2_measure_5" id="size-2-measure-5" value="{{ size_2_measure_5 }}"></td>
                <td>
                    <label><input type="radio" name="size_2_check" value="合格" {{ 'checked' if size_2_check == '合格' else '' }}> 合格</label>
                    <label><input type="radio" name="size_2_check" value="不合格" {{ 'checked' if size_2_check == '不合格' else '' }}> 不合格</label>
                    <label><input type="radio" name="size_2_check" value="AOD" {{ 'checked' if size_2_check == 'AOD' else '' }}> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_2_note">{{ size_2_note }}</textarea></td>
            </tr>

            <!-- 更多尺寸行 - 第3行 -->
            <tr class="custom-row-height" id="size-row-3" style="display: table-row !important;">
                <td class="align-center">3</td>
                <td data-id="size-3" data-image="true" class="align-center">
                    <input type="text" name="size_3_position" id="size-3" value="{{ size_3_position }}">
                </td>
                <td class="align-center"><input type="text" name="size_3_value" id="size-3-value" value="{{ size_3_value }}"></td>
                <td class="align-center"><input type="text" name="size_3_min" id="size-3-min" value="{{ size_3_min }}"></td>
                <td class="align-center"><input type="text" name="size_3_max" id="size-3-max" value="{{ size_3_max }}"></td>
                <td class="align-center"><input type="text" name="size_3_measure_1" id="size-3-measure-1" value="{{ size_3_measure_1 }}"></td>
                <td class="align-center"><input type="text" name="size_3_measure_2" id="size-3-measure-2" value="{{ size_3_measure_2 }}"></td>
                <td class="align-center"><input type="text" name="size_3_measure_3" id="size-3-measure-3" value="{{ size_3_measure_3 }}"></td>
                <td class="align-center"><input type="text" name="size_3_measure_4" id="size-3-measure-4" value="{{ size_3_measure_4 }}"></td>
                <td class="align-center"><input type="text" name="size_3_measure_5" id="size-3-measure-5" value="{{ size_3_measure_5 }}"></td>
                <td>
                    <label><input type="radio" name="size_3_check" value="合格" {{ 'checked' if size_3_check == '合格' else '' }}> 合格</label>
                    <label><input type="radio" name="size_3_check" value="不合格" {{ 'checked' if size_3_check == '不合格' else '' }}> 不合格</label>
                    <label><input type="radio" name="size_3_check" value="AOD" {{ 'checked' if size_3_check == 'AOD' else '' }}> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_3_note">{{ size_3_note }}</textarea></td>
            </tr>
            <!-- 动态渲染其他尺寸数据 -->
            {% for data in size_data %}
                {% if loop.index > 3 %} <!-- 跳过前5条固定行 -->
                <tr class="custom-row-height dynamic-size-row" id="size-row-{{ data.size_number }}">
                    <td class="align-center">{{ data.size_number }}</td>
                    <td data-id="size-{{ data.size_number }}" data-image="true" class="align-center">
                        <input type="text" name="size_{{ data.size_number }}_position" id="size-{{ data.size_number }}" value="{{ data.position }}">
                    </td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_value" id="size-{{ data.size_number }}-value" value="{{ data.value }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_min" id="size-{{ data.size_number }}-min" value="{{ data.min_value }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_max" id="size-{{ data.size_number }}-max" value="{{ data.max_value }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_measure_1" id="size-{{ data.size_number }}-measure-1" value="{{ data.measure_1 }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_measure_2" id="size-{{ data.size_number }}-measure-2" value="{{ data.measure_2 }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_measure_3" id="size-{{ data.size_number }}-measure-3" value="{{ data.measure_3 }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_measure_4" id="size-{{ data.size_number }}-measure-4" value="{{ data.measure_4 }}"></td>
                    <td class="align-center"><input type="text" name="size_{{ data.size_number }}_measure_5" id="size-{{ data.size_number }}-measure-5" value="{{ data.measure_5 }}"></td>
                    <td>
                        <label><input type="radio" name="size_{{ data.size_number }}_check" value="合格" {{ 'checked' if data.check_result == '合格' else '' }}> 合格</label>
                        <label><input type="radio" name="size_{{ data.size_number }}_check" value="不合格" {{ 'checked' if data.check_result == '不合格' else '' }}> 不合格</label>
                        <label><input type="radio" name="size_{{ data.size_number }}_check" value="AOD" {{ 'checked' if data.check_result == 'AOD' else '' }}> AOD</label>
                    </td>
                    <td colspan="2"><textarea name="size_{{ data.size_number }}_note">{{ data.note }}</textarea></td>
                </tr>
                {% endif %}
            {% endfor %}

            <!-- 添加和删除尺寸行的按钮 -->
            <tr id="size-buttons-row">
                <td colspan="14" style="text-align: center; padding: 10px;">
                    <button type="button" id="add-size-row">添加尺寸行</button>
                    <button type="button" id="delete-size-row">删除尺寸行</button>
                </td>
            </tr>

            <!-- ================= 外观检查 ================= -->
            <tr class="module-title">
                <td rowspan="5" style="width: 30px;">二、外观</td>
                <td class="col-no align-center">NO</td>
                <td colspan="9" class="align-center">要求描述</td>
                <td class="col-check align-center col-compliance">符合性</td>
                <td class="col-note align-center" colspan="2">注意事项</td>
            </tr>
            <!-- 外观条目 -->
            <tr>
                <td class="align-center">1</td>
                <td colspan="9" class="align-left">表面刮痕、凹痕、裂纹、沙孔等不良符合《外观标准》要求</td>
                <td>
                    <label><input type="radio" name="appearance_1_check" value="合格" {{ 'checked' if appearance_data[0]['check_result'] == '合格' else '' }}> 合格</label>
                    <label><input type="radio" name="appearance_1_check" value="不合格" {{ 'checked' if appearance_data[0]['check_result'] == '不合格' else '' }}> 不合格</label>
                    <label><input type="radio" name="appearance_1_check" value="AOD" {{ 'checked' if appearance_data[0]['check_result'] == '/' else '' }}> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_1_note">{{ appearance_data[0]['note'] }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">2</td>
                <td colspan="9" class="align-left">不能出现表面生锈、披锋刮手、焊接无脱焊、表面抛光纹路、光泽度符合要求</td>
                <td>
                    <label><input type="radio" name="appearance_2_check" value="合格" {% if appearance_data[1]['check_result'] == '合格' %}checked{% endif %}> 合格</label>
                    <label><input type="radio" name="appearance_2_check" value="不合格" {% if appearance_data[1]['check_result'] == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="appearance_2_check" value="/" {% if appearance_data[1]['check_result'] == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_2_note">{{ appearance_data[1]['note'] }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">3</td>
                <td colspan="9" class="align-left">无明显的气味</td>
                <td>
                    <label><input type="radio" name="appearance_3_check" value="合格" {% if appearance_data[2]['check_result'] == '合格' %}checked{% endif %}> 合格</label>
                    <label><input type="radio" name="appearance_3_check" value="不合格" {% if appearance_data[2]['check_result'] == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="appearance_3_check" value="/" {% if appearance_data[2]['check_result'] == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="appearance_3_note">{{ appearance_data[2]['note'] }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">4</td>
                <td colspan="9" class="align-left">
                    <textarea name="appearance_4_other" id="appearance-other" placeholder="其他/特殊要求：">{{ appearance_data[3]['other_info'] }}</textarea>
                </td>
                <td>
                    <label><input type="radio" name="appearance_4_check" value="合格" {% if appearance_data[3]['check_result'] == '合格' %}checked{% endif %}> 合格</label>
                    <label><input type="radio" name="appearance_4_check" value="不合格" {% if appearance_data[3]['check_result'] == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="appearance_4_check" value="/" {% if appearance_data[3]['check_result'] == '/' %}checked{% endif %}> /</label>
                 </td>
                <td colspan="2"><textarea name="appearance_4_note">{{ appearance_data[3]['note'] }}</textarea></td>
            </tr>
            <tr class="module-title">
                <td rowspan="7" class="align-center">三、功能/可靠性</td>
                <td class="align-center">NO.</td>
                <td colspan="9" class="align-center">要求描述</td>
                <td>符合性</td>
                <td colspan="2">注意事项</td>
            </tr>
            <tr>
                <td class="align-center">1</td>
                <td colspan="9" class="align-left">牙管内、外螺牙顺畅（管缝无高起）；弯管的折弯角度符合要求</td>
                <td>
                    <label><input type="radio" name="function_1_check" value="合格" {% if function_1_check == '合格' %}checked{% endif %}> 合格</label>
                    <label><input type="radio" name="function_1_check" value="不合格" {% if function_1_check == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="function_1_check" value="/" {% if function_1_check == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="function_1_note">{{ function_1_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">2</td>
                <td colspan="9" class="align-left">部件的认证标识参数符合要求</td>
                <td>
                    <label><input type="radio" name="function_2_check" value="合格" {% if function_2_check == '合格' %}checked{% endif %}> 合格</label></label>
                    <label><input type="radio" name="function_2_check" value="不合格" {% if function_2_check == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="function_2_check" value="/" {% if function_2_check == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="function_2_note">{{ function_2_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">3</td>
                <td colspan="9" class="align-left">使用配合件进行试装配检验</td>
                <td>
                    <label><input type="radio" name="function_3_check" value="合格" {% if function_3_check == '合格' %}checked{% endif %}> 合格</label></label>
                    <label><input type="radio" name="function_3_check" value="不合格" {% if function_3_check == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="function_3_check" value="/" {% if function_3_check == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="function_3_note">{{ function_3_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">4</td>
                <td colspan="9" class="align-left">
                    电气性能：光电参数、耐压测试、亮灯性能、老化测试：
                    <input type="text" name="function_4_burnin" value="{{ function_4_burnin }}" style="width: 300px;display: inline-block;border: 1px solid #000;"> 
                    <br><br>
                    电阻、电感、电容等关键电气参数：
                    <input type="text" name="function_4_electrical" value="{{ function_4_electrical }}" style="width: 300px;display: inline-block;border: 1px solid #000;"></input>
                </td>
                <td>
                    <label><input type="radio" name="function_4_check" value="合格" {% if function_4_check == '合格' %}checked{% endif %}> 合格</label></label>
                    <label><input type="radio" name="function_4_check" value="不合格" {% if function_4_check == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="function_4_check" value="/" {% if function_4_check == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="function_4_note">{{ function_4_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">5</td>
                <td colspan="9" class="align-left" style="font-size: 13px;">
                    <!-- 添加复选框 -->
                    <input type="checkbox" id="function_5_tests_salt_fog" name="function_5_tests[]" value="盐雾测试" {{ 'checked' if '盐雾测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_salt_fog">盐雾测试</label>
                    
                    <input type="checkbox" id="function_5_tests_thickness" name="function_5_tests[]" value="厚度测试" {{ 'checked' if '厚度测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_thickness">厚度测试</label>
                    
                    <input type="checkbox" id="function_5_tests_flame_retardant" name="function_5_tests[]" value="阻燃测试" {{ 'checked' if '阻燃测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_flame_retardant">阻燃测试</label>
                    
                    <input type="checkbox" id="function_5_tests_hardness" name="function_5_tests[]" value="硬度测试" {{ 'checked' if '硬度测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_hardness">硬度测试</label>
                    
                    <input type="checkbox" id="function_5_tests_adhesion" name="function_5_tests[]" value="附着力测试" {{ 'checked' if '附着力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_adhesion">附着力测试</label><br>
                    
                    <input type="checkbox" id="function_5_tests_environment" name="function_5_tests[]" value="环境测试" {{ 'checked' if '环境测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_environment">环境测试</label>
                    
                    <input type="checkbox" id="function_5_tests_stress" name="function_5_tests[]" value="应力测试" {{ 'checked' if '应力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_stress">应力测试</label>
                    
                    <input type="checkbox" id="function_5_tests_torque" name="function_5_tests[]" value="扭力测试" {{ 'checked' if '扭力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_torque">扭力测试</label>
                    
                    <input type="checkbox" id="function_5_tests_impact" name="function_5_tests[]" value="冲击测试" {{ 'checked' if '冲击测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_impact">冲击测试</label>
                    
                    <input type="checkbox" id="function_5_tests_pull_force" name="function_5_tests[]" value="推拉力测试" {{ 'checked' if '推拉力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_pull_force">推拉力测试</label><br>
                    
                    <input type="checkbox" id="function_5_tests_scratch_resistance" name="function_5_tests[]" value="耐刮擦性测试" {{ 'checked' if '耐刮擦性测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_scratch_resistance">耐刮擦性测试</label>
                    
                    <input type="checkbox" id="function_5_tests_other" name="function_5_tests[]" value="其他测试" {{ 'checked' if '其他测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_other">其他测试：</label>
                    <input type="text" id="function_5_other_test" name="function_5_other_test" value="{{ function_5_other_test }}" style="width: 300px;display: inline-block;border: 1px solid #000;"></input>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // 获取复选框元素
                            const checkbox = document.getElementById('function_5_tests_other');
                            const textbox = document.getElementById('function_5_other_test');
                    
                            // 监听复选框的状态变化
                            checkbox.addEventListener('change', function() {
                                // 根据复选框是否被选中，显示或隐藏文本框
                                textbox.style.display = this.checked ? 'inline-block' : 'none';
                            });
                        });
                    </script>
                </td>
                <td>
                    <label><input type="radio" name="function_5_check" value="合格" {% if function_5_check == '合格' %}checked{% endif %}> 合格</label>
                    <label><input type="radio" name="function_5_check" value="不合格" {% if function_5_check == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="function_5_check" value="/" {% if function_5_check == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="function_5_note">{{ function_5_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">6</td>
                <td colspan="9" class="align-left"><textarea name="function_6_other" placeholder="其他/特殊要求：">{{ function_6_other }}</textarea></td>

                <td>
                    <label><input type="radio" name="function_6_check" value="合格" {% if function_6_check == '合格' %}checked{% endif %}> 合格</label>
                    <label><input type="radio" name="function_6_check" value="不合格" {% if function_6_check == '不合格' %}checked{% endif %}> 不合格</label>
                    <label><input type="radio" name="function_6_check" value="/" {% if function_6_check == '/' %}checked{% endif %}> /</label>
                </td>
                <td colspan="2"><textarea name="function_6_note">{{ function_6_note }}</textarea></td>
            </tr>


            <!-- ================= 补充问题点 ================= -->
            <tr class="module-title">
                <td colspan="14">四、补充问题点/题点</td>
            </tr>
            {% set actual_question_count = questions|length %}
            {% for question_group in questions|batch(3) %}
            <tr class="question-row">
                {% for q in question_group %}
                <td colspan="{% if loop.index == 1 %}4{% else %}5{% endif %}" class="image-upload-cell">
                    <div class="image-upload-box{% if q.question_number in valid_question_numbers and question_images[q.question_number] and not question_images[q.question_number].no_image %} has-image{% endif %}" data-question-number="{{ q.question_number }}">
                        
                        {% if q.question_number in valid_question_numbers and question_images[q.question_number] and not question_images[q.question_number].no_image %}
                            <img class="preview-image" src="{{ question_images[q.question_number].full_path }}" />
                            <button class="delete-btn" type="button" onclick="deleteImage('{{ q.question_number }}')">×</button>
                        {% else %}
                            <div class="upload-overlay">无图片</div>
                        {% endif %}
                    </div>
                </td>
                {% endfor %}
                {# 确保每行有3个问题点 #}
                {% for i in range(question_group|length, 3) %}
                <td colspan="{% if loop.index0 + question_group|length == 0 %}4{% else %}5{% endif %}" class="image-upload-cell">
                    <div class="image-upload-box" data-question-number="empty-{{ loop.index }}">
                        <div class="upload-overlay">无图片</div>
                    </div>
                </td>
                {% endfor %}
            </tr>
            <tr class="question-text-row">
                {% for q in question_group %}
                <td colspan="{% if loop.index == 1 %}4{% else %}5{% endif %}" style="height: 10px;">
                    <textarea name="question_{{ q.question_number }}_text" placeholder="请输入问题描述">{{ q.question_text if q.question_text is defined else '' }}</textarea>
                </td>
                {% endfor %}
                {# 确保每行有3个文本区域 #}
                {% for i in range(question_group|length, 3) %}
                <td colspan="{% if loop.index0 + question_group|length == 0 %}4{% else %}5{% endif %}" style="height: 10px;">
                    <textarea name="question_empty_{{ loop.index }}_text" placeholder="请输入问题描述"></textarea>
                </td>
                {% endfor %}
            </tr>
            {% endfor %}

            {# 如果现有问题点不足6个，自动补充空问题行 #}
            {% if actual_question_count < 6 %}
                {% for row_index in range((actual_question_count + 2) // 3, 2) %}
                <tr class="question-row">
                    {% for col_index in range(3) %}
                    <td colspan="{% if loop.index == 1 %}4{% else %}5{% endif %}" class="image-upload-cell">
                        <div class="image-upload-box" data-question-number="auto-{{ row_index }}-{{ col_index }}">
                            <div class="upload-overlay">无图片</div>
                        </div>
                    </td>
                    {% endfor %}
                </tr>
                <tr class="question-text-row">
                    {% for col_index in range(3) %}
                    <td colspan="{% if loop.index == 1 %}4{% else %}5{% endif %}" style="height: 10px;">
                        <textarea name="question_auto_{{ row_index }}_{{ col_index }}_text" placeholder="请输入问题描述"></textarea>
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            {% endif %}

            <!-- 添加问题点按钮行 -->
            <tr id="question-buttons-row">
                <td colspan="14" class="align-center" style="padding: 8px 0; background-color: #f9f9f9;height: 10px;height: 14px;">
                    <button type="button" id="add-question-row" style="font-size: 12px;">
                        <span style="margin-right: 1px;"></span>增加问题点
                    </button>
                    <button type="button" id="delete-question-row" style="font-size: 12px;">
                        <span style="margin-right: 1px;"></span>删除问题点
                    </button>
                </td>
            </tr>

            <!-- ================= 最终判定 ================= -->
            <tr>
                <td rowspan="2" colspan="2" class="align-center">最终判定</td>
                <td colspan="10" class="align-left" style="height: 30px;font-size: 13px;">
                    <!-- 使用单选按钮实现互斥选择 -->
                    <input type="radio" name="final_judgment" value="合格"{{ 'checked' if final_judgment == '合格' else '' }} id="qualified-radio"> 
                    <label for="qualified-radio">合格</label>
                    <input type="radio" name="final_judgment" value="不合格"{{ 'checked' if final_judgment == '不合格' else '' }} id="not-qualified-radio"> 
                    <label for="not-qualified-radio">不合格</label>
                </td>
                
                <td colspan="2" class="align-center">审核</td>
            </tr>
            <tr>
                <td colspan="1" class="align-left">意见：</td>
                <td colspan="9">
                    <textarea id="opinion" name="opinion" rows="2" cols="55">{{ opinion }}</textarea>
                </td>
                <td colspan="1">
                    {{ review }}
                </td>
                <td colspan="1"></td>
            </tr>
        </table>
    </form>

    <!-- 图片放大模态框 -->
    <div id="imageModal" class="image-modal">
        <span class="close">&times;</span>
        <div class="modal-loader"></div>
        <img class="modal-content" id="modalImage">
        <div class="image-controls">
            <button class="control-btn" id="zoom-out" onclick="zoomOut()">−</button>
            <button class="control-btn" id="reset-view" onclick="resetZoom()">重置</button>
            <button class="control-btn" id="zoom-in" onclick="zoomIn()">+</button>
        </div>
        <div class="tooltip" id="image-tooltip"></div>
    </div>

    <script>
        // 添加调试函数，在页面加载时检查所有可能的表单提交行为
        function debugFormSubmission() {
            console.log('开始调试表单提交行为...');
            
            // 检查表单的action属性
            const allForms = document.querySelectorAll('form');
            console.log(`页面中共有 ${allForms.length} 个表单`);
            
            allForms.forEach((form, index) => {
                console.log(`表单 #${index + 1}:`, {
                    id: form.id || '无ID',
                    action: form.action,
                    method: form.method,
                    elements: form.elements.length
                });
                
                // 检查表单中的提交按钮
                const submitButtons = form.querySelectorAll('button[type="submit"], input[type="submit"]');
                console.log(`表单 #${index + 1} 中有 ${submitButtons.length} 个提交按钮`);
            });
            
            // 检查所有可能的提交按钮
            const allSubmitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
            console.log(`页面中共有 ${allSubmitButtons.length} 个提交按钮`);
            
            // 检查所有按钮的点击事件
            const allButtons = document.querySelectorAll('button');
            console.log(`页面中共有 ${allButtons.length} 个按钮`);
            
            // 检查提交按钮
            const submitBtn = document.getElementById('submit-btn');
            if (submitBtn) {
                console.log('找到提交按钮:', {
                    id: submitBtn.id,
                    type: submitBtn.type,
                    parentForm: submitBtn.form ? submitBtn.form.id : '不在表单中'
                });
            } else {
                console.log('未找到ID为submit-btn的按钮');
            }
            
            // 检查是否有内联的onclick属性
            const elementsWithOnclick = document.querySelectorAll('[onclick]');
            console.log(`页面中有 ${elementsWithOnclick.length} 个元素包含onclick属性`);
            
            elementsWithOnclick.forEach((el, index) => {
                console.log(`元素 #${index + 1} 的onclick:`, el.getAttribute('onclick'));
            });
            
            console.log('表单提交调试完成');
        }

        // 定义一个新的函数，强制使用正确的URL提交表单
        function submitFormToCorrectUrl(e) {
            console.log('submitFormToCorrectUrl被调用');
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // 表单数据预处理：检查并清理所有尺寸测量值，确保都是有效数字
            const numericInputs = document.querySelectorAll('input[name*="_measure_"]');
            numericInputs.forEach(input => {
                const value = input.value.trim();
                // 检查是否为有效数字
                if (value !== "" && isNaN(parseFloat(value))) {
                    console.warn(`发现非数字输入值: ${input.name} = "${value}"，已清空`);
                    input.value = ""; // 清空非数字值
                    // 可选：给用户提示
                    showFeedbackMessage(`"${input.name}"字段包含非数字值"${value}"，已自动清空`, 'warning');
                }
            });

            // 检查min、max和value字段
            const sizeValueInputs = document.querySelectorAll('input[name*="_min"], input[name*="_max"], input[name*="_value"]');
            sizeValueInputs.forEach(input => {
                const value = input.value.trim();
                // 允许空值，但填写的值必须是数字
                if (value !== "" && isNaN(parseFloat(value))) {
                    console.warn(`发现非数字输入值: ${input.name} = "${value}"，已清空`);
                    input.value = ""; // 清空非数字值
                }
            });
            
            // 检查sample_count字段，如果为空则设置默认值0
            const sampleCountInput = document.querySelector('input[name="sample_count"]');
            if (sampleCountInput && (sampleCountInput.value === "" || sampleCountInput.value.trim() === "")) {
                console.log('样板数量为空，设置为默认值0');
                sampleCountInput.value = "0";
            }
            
            // 在提交前重新排序尺寸行，确保数据连续
            reorderSizeRows();
            
            // 在提交前根据设置决定是否重排序问题点
            const enableReordering = getImageReorderingSetting();
            console.log(`提交表单前检查图片重排序设置: ${enableReordering}`);
            if (enableReordering.toLowerCase() === 'yes') {
                console.log('图片重排序设置为"是"，执行问题点重排序');
                reorderQuestionPoints();
            } else {
                console.log('图片重排序设置为"否"，跳过问题点重排序');
            }
            
            // 关键调试：打印所有尺寸行的状态和数据
            console.log('提交前检查所有尺寸行:');
            document.querySelectorAll('tr[id^="size-row-"]').forEach(row => {
                if (row.id) {
                    console.log(`行 ${row.id} 显示状态: ${row.classList.contains('empty-size-row') ? '隐藏' : '显示'}`);
                    
                    // 获取所有字段
                    const inputs = row.querySelectorAll('input[type="text"]');
                    if (inputs.length > 0) {
                        console.log(`行 ${row.id} 内容:`);
                        inputs.forEach(input => {
                            if (input.name) {
                                console.log(`- ${input.name}: "${input.value}"`);
                            }
                        });
                    }
                }
            });
            
            // 确保所有有数据的行不被标记为empty-size-row
            document.querySelectorAll('tr[id^="size-row-"]').forEach(row => {
                const isEmpty = checkRowIsEmpty(row);
                if (!isEmpty && row.classList.contains('empty-size-row')) {
                    console.log(`提交前移除行 ${row.id} 的empty-size-row类，因为它有数据`);
                    row.classList.remove('empty-size-row');
                }
            });
            
            // 确保前3行不被标记为empty-size-row
            document.querySelectorAll('#size-row-1, #size-row-2, #size-row-3').forEach(row => {
                if (row.classList.contains('empty-size-row')) {
                    console.log(`提交前移除基本行 ${row.id} 的empty-size-row类`);
                    row.classList.remove('empty-size-row');
                }
            });
            
            // 创建新的FormData对象
            const formData = new FormData();
            
            // 获取表单元素
            const form = document.getElementById('material-form');
            
            // 先添加非尺寸行的表单字段
            for (const element of form.elements) {
                if (!element.name) continue;
                
                // 跳过已处理的复选框和尺寸行相关字段
                if (element.name === 'sample_status[]') continue;
                if (element.name.startsWith('size_') && 
                    (element.name.includes('_position') || 
                     element.name.includes('_value') || 
                     element.name.includes('_min') || 
                     element.name.includes('_max') || 
                     element.name.includes('_measure_') || 
                     element.name.includes('_note'))) continue;
                
                // 跳过问题点相关字段，将在后面处理
                if (element.name.startsWith('question_') && 
                    (element.name.includes('_text') || 
                     element.name.includes('_image'))) continue;
                
                // 处理不同类型的输入
                if (element.type === 'checkbox' || element.type === 'radio') {
                    if (element.checked) {
                        formData.append(element.name, element.value);
                    }
                } else {
                    // 其他字段正常添加
                    formData.append(element.name, element.value);
                }
            }
            
            // 特殊处理尺寸行数据 - 直接从DOM获取每个字段，不依赖行的显示状态
            for (let i = 1; i <= 30; i++) { // 假设最多30个尺寸行
                // 前3行数据始终处理，无论是否为空
                if (i <= 3) {
                    console.log(`处理第${i}行数据(基础行):`);
                    const positionInput = document.querySelector(`input[name="size_${i}_position"]`);
                    const valueInput = document.querySelector(`input[name="size_${i}_value"]`);
                    const minInput = document.querySelector(`input[name="size_${i}_min"]`);
                    const maxInput = document.querySelector(`input[name="size_${i}_max"]`);
                    
                    if (positionInput) formData.append(`size_${i}_position`, positionInput.value || "");
                    if (valueInput) formData.append(`size_${i}_value`, valueInput.value || "");
                    if (minInput) formData.append(`size_${i}_min`, minInput.value || "");
                    if (maxInput) formData.append(`size_${i}_max`, maxInput.value || "");
                    
                    // 处理measure字段
                    for (let j = 1; j <= 5; j++) {
                        const measureInput = document.querySelector(`input[name="size_${i}_measure_${j}"]`);
                        if (measureInput) {
                            const value = measureInput.value.trim();
                            if (value === "" || value === "NULL") {
                                formData.append(`size_${i}_measure_${j}`, "");
                            } else if (!isNaN(parseFloat(value))) {
                                formData.append(`size_${i}_measure_${j}`, value);
                            } else {
                                formData.append(`size_${i}_measure_${j}`, "");
                            }
                        }
                    }
                    
                    // 处理note字段
                    const noteTextarea = document.querySelector(`textarea[name="size_${i}_note"]`);
                    if (noteTextarea) formData.append(`size_${i}_note`, noteTextarea.value || "");
                    
                    // 处理单选按钮
                    const checkedRadio = document.querySelector(`input[name="size_${i}_check"]:checked`);
                    if (checkedRadio) formData.append(`size_${i}_check`, checkedRadio.value);
                    
                    continue;
                }
                
                // 对于第4-30行，只处理非空行
                const row = document.getElementById(`size-row-${i}`);
                if (row) {
                    const isEmpty = checkRowIsReallyEmpty(row);
                    if (!isEmpty) {
                        console.log(`处理第${i}行数据(非空行):`);
                        const positionInput = document.querySelector(`input[name="size_${i}_position"]`);
                        const valueInput = document.querySelector(`input[name="size_${i}_value"]`);
                        const minInput = document.querySelector(`input[name="size_${i}_min"]`);
                        const maxInput = document.querySelector(`input[name="size_${i}_max"]`);
                        
                        if (positionInput) formData.append(`size_${i}_position`, positionInput.value || "");
                        if (valueInput) formData.append(`size_${i}_value`, valueInput.value || "");
                        if (minInput) formData.append(`size_${i}_min`, minInput.value || "");
                        if (maxInput) formData.append(`size_${i}_max`, maxInput.value || "");
                        
                        // 处理measure字段
                        for (let j = 1; j <= 5; j++) {
                            const measureInput = document.querySelector(`input[name="size_${i}_measure_${j}"]`);
                            if (measureInput) {
                                const value = measureInput.value.trim();
                                if (value === "" || value === "NULL") {
                                    formData.append(`size_${i}_measure_${j}`, "");
                                } else if (!isNaN(parseFloat(value))) {
                                    formData.append(`size_${i}_measure_${j}`, value);
                                } else {
                                    formData.append(`size_${i}_measure_${j}`, "");
                                }
                            }
                        }
                        
                        // 处理note字段
                        const noteTextarea = document.querySelector(`textarea[name="size_${i}_note"]`);
                        if (noteTextarea) formData.append(`size_${i}_note`, noteTextarea.value || "");
                        
                        // 处理单选按钮
                        const checkedRadio = document.querySelector(`input[name="size_${i}_check"]:checked`);
                        if (checkedRadio) formData.append(`size_${i}_check`, checkedRadio.value);
                    }
                }
            }
            
            // 处理问题点数据
            // 获取所有问题点的数据和映射关系
            const { questionData, questionMapping } = getQuestionPointsData();
            console.log('问题点数据:', questionData);
            console.log('问题点映射关系:', questionMapping);
            
            // 第一阶段：收集所有问题点文本框的数据
            console.log('第一阶段：收集所有问题点文本和图片数据');
            
            // 创建一个统一的数据结构来存储所有问题点信息
            const questionInfoByNumber = {};
            
            // 收集所有文本框数据
            const allTextareas = document.querySelectorAll('textarea[name^="question_"][name$="_text"]');
            const textareaData = {};
            
            // 先收集所有文本框的数据，确保不会丢失
            allTextareas.forEach(textarea => {
                const name = textarea.getAttribute('name');
                if (name && name.startsWith('question_') && name.endsWith('_text')) {
                    const questionNumber = name.replace('question_', '').replace('_text', '');
                    
                    // 检查是否有原始文本内容（从隐藏字段中获取）
                    const originalTextInput = document.querySelector(`input[name="question_${questionNumber}_original_text"]`);
                    let textContent = '';
                    
                    if (originalTextInput && originalTextInput.value) {
                        // 优先使用保存的原始文本
                        textContent = originalTextInput.value.trim();
                        console.log(`使用问题点 ${questionNumber} 的原始文本内容: "${textContent.substring(0, 30)}..."`);
                    } else if (textarea.value && textarea.value.trim() !== '') {
                        // 如果没有原始文本，使用当前文本框的值
                        textContent = textarea.value.trim();
                        console.log(`收集到问题点 ${questionNumber} 的文本: "${textContent.substring(0, 30)}..."`);
                    }
                    
                    if (textContent) {
                        textareaData[questionNumber] = textContent;
                        
                        // 确保文本框显示正确的内容
                        textarea.value = textContent;
                        
                        // 将数据添加到统一的数据结构
                        if (!questionInfoByNumber[questionNumber]) {
                            questionInfoByNumber[questionNumber] = { text: textContent };
                        } else {
                            questionInfoByNumber[questionNumber].text = textContent;
                        }
                    }
                }
            });
            
            // 收集所有图片数据（不限于7-18，收集全部问题点1-18的图片）
            document.querySelectorAll('.image-upload-box[data-has-image="true"]').forEach(box => {
                const questionNumber = box.getAttribute('data-question-number');
                if (questionNumber && parseInt(questionNumber) >= 7 && parseInt(questionNumber) <= 18) {
                    const previewImage = box.querySelector('.preview-image');
                    if (previewImage && previewImage.src) {
                        if (!questionInfoByNumber[questionNumber]) {
                            questionInfoByNumber[questionNumber] = { imageSrc: previewImage.src };
                        } else {
                            questionInfoByNumber[questionNumber].imageSrc = previewImage.src;
                        }
                        console.log(`收集到问题点 ${questionNumber} 的图片: ${previewImage.src.substring(0, 30)}...`);
                    }
                }
            });

            // 收集所有图片数据 - 替换为全部问题点1-18的图片收集
            document.querySelectorAll('.image-upload-box[data-has-image="true"]').forEach(box => {
                const questionNumber = box.getAttribute('data-question-number');
                if (questionNumber) {
                    const previewImage = box.querySelector('.preview-image');
                    if (previewImage && previewImage.src) {
                        if (!questionInfoByNumber[questionNumber]) {
                            questionInfoByNumber[questionNumber] = { imageSrc: previewImage.src };
                        } else {
                            questionInfoByNumber[questionNumber].imageSrc = previewImage.src;
                        }
                        console.log(`收集到问题点 ${questionNumber} 的图片: ${previewImage.src.substring(0, 30)}...`);
                    }
                }
            });
            
            // 收集所有图片数据（收集全部问题点1-18的图片）
            document.querySelectorAll('.image-upload-box[data-has-image="true"]').forEach(box => {
                const questionNumber = box.getAttribute('data-question-number');
                if (questionNumber) { // 不限制问题点编号范围，收集所有图片
                    const previewImage = box.querySelector('.preview-image');
                    if (previewImage && previewImage.src) {
                        if (!questionInfoByNumber[questionNumber]) {
                            questionInfoByNumber[questionNumber] = { imageSrc: previewImage.src };
                        } else {
                            questionInfoByNumber[questionNumber].imageSrc = previewImage.src;
                        }
                        console.log(`收集到问题点 ${questionNumber} 的图片: ${previewImage.src.substring(0, 30)}...`);
                    }
                }
            });
            
            console.log('收集到的问题点信息:', Object.keys(questionInfoByNumber).length);
            
            // 获取重排序设置
            const formSubmitReorderingSetting = getImageReorderingSetting();
            console.log(`提交表单时的重排序设置: ${formSubmitReorderingSetting}`);
            
            // 收集所有文本框数据的副本，按原始编号索引
            const allTextareaDataByOriginal = {};
            
            // 先查找所有带data-original-number属性的文本区域
            document.querySelectorAll('textarea[name^="question_"][name$="_text"]').forEach(textarea => {
                const originalNumber = textarea.getAttribute('data-original-number');
                if (originalNumber) {
                    allTextareaDataByOriginal[originalNumber] = textarea.value;
                    console.log(`收集到带原始编号的文本区域: ${originalNumber}: "${textarea.value.substring(0, 30)}..."`);
                }
            });
            
            // 打印所有问题点数据的详细信息，便于调试
            console.log('提交前的问题点数据详情:');
            questionData.forEach((data, index) => {
                if (data) {
                    console.log(`问题点 ${index + 1}:`, {
                        originalNumber: data.originalNumber,
                        text: data.text ? data.text.substring(0, 30) + '...' : '',
                        position: data.position,
                        currentPosition: data.currentPosition
                    });
                }
            });
            
            // 判断重排序模式
            if (formSubmitReorderingSetting.toLowerCase() === 'no') {
                console.log('非重排序模式下提交问题点数据 - 直接使用原始编号和位置');
                
                // 在非重排序模式下，按原始编号处理数据
                for (const data of questionData) {
                    if (!data || !data.originalNumber) continue;
                    
                    const originalNumber = data.originalNumber;
                    const formFieldName = `question_${originalNumber}_text`;
                    
                    // 1. 优先使用通过data-original-number属性收集的文本
                    if (allTextareaDataByOriginal[originalNumber]) {
                        formData.append(formFieldName, allTextareaDataByOriginal[originalNumber]);
                        console.log(`非重排序模式 - 使用原始编号关联文本: ${originalNumber}: "${allTextareaDataByOriginal[originalNumber].substring(0, 30)}..."`);
                    }
                    // 2. 其次使用隐藏字段中的原始文本
                    else {
                        const originalTextInput = document.querySelector(`input[name="question_${originalNumber}_original_text"]`);
                        if (originalTextInput && originalTextInput.value) {
                            formData.append(formFieldName, originalTextInput.value);
                            console.log(`非重排序模式 - 使用隐藏字段文本: ${originalNumber}: "${originalTextInput.value.substring(0, 30)}..."`);
                        }
                        // 3. 再次尝试使用从textareaData中收集的数据
                        else if (textareaData[originalNumber]) {
                            formData.append(formFieldName, textareaData[originalNumber]);
                            console.log(`非重排序模式 - 使用全局收集的文本: ${originalNumber}: "${textareaData[originalNumber].substring(0, 30)}..."`);
                        }
                        // 4. 最后使用问题点数据中的文本
                        else {
                            formData.append(formFieldName, data.text || "");
                            console.log(`非重排序模式 - 使用问题点数据中的文本: ${originalNumber}: "${(data.text || "").substring(0, 30)}..."`);
                        }
                    }
                    
                    // 添加图片路径（如果有）
                    if (data.imageSrc) {
                        formData.append(`question_${originalNumber}_image`, data.imageSrc);
                    }
                }
            } else {
                console.log('重排序模式下提交问题点数据 - 使用连续编号');
                
                // 第二阶段：处理收集到的数据并提交
                console.log('第二阶段：处理收集到的数据并提交表单');
                
                // 重排序模式下，先清除所有问题点文本字段，避免重复
                for (let i = 1; i <= 18; i++) {
                    // 使用delete方法清除已有的字段
                    const textFieldName = `question_${i}_text`;
                    const imageFieldName = `question_${i}_image`;
                    const originalNumberFieldName = `question_${i}_original_number`;
                    
                    // 检查字段是否存在，然后删除
                    if (formData.has(textFieldName)) formData.delete(textFieldName);
                    if (formData.has(imageFieldName)) formData.delete(imageFieldName);
                    if (formData.has(originalNumberFieldName)) formData.delete(originalNumberFieldName);
                }
                console.log('重排序模式 - 已清除所有问题点字段，避免重复');
                
                // 创建问题点原始编号到新编号的映射
                const originalToNewNumberMap = {};
                questionData.forEach((data, index) => {
                    if (data && data.originalNumber) {
                        originalToNewNumberMap[data.originalNumber] = index + 1;
                    }
                });
                console.log('原始编号到新编号的映射:', originalToNewNumberMap);
                
                // 重排序模式下，按连续编号处理数据
                for (let i = 0; i < questionData.length; i++) {
                    const questionNumber = i + 1;
                    const data = questionData[i];
                    
                    if (data) {
                        // 使用原始编号查找对应的文本和图片内容
                        const originalNumber = data.originalNumber || questionNumber.toString();
                        const formFieldName = `question_${questionNumber}_text`;
                        
                        // 从统一数据结构中查找文本
                        if (questionInfoByNumber[originalNumber] && questionInfoByNumber[originalNumber].text) {
                            formData.append(formFieldName, questionInfoByNumber[originalNumber].text);
                            console.log(`重排序模式 - 从统一数据结构中获取文本: ${questionNumber}(原始:${originalNumber}): "${questionInfoByNumber[originalNumber].text.substring(0, 30)}..."`);
                        } 
                        // 优先使用隐藏字段中的原始文本
                        else if (originalTextInput && originalTextInput.value) {
                            const originalTextInput = document.querySelector(`input[name="question_${originalNumber}_original_text"]`);
                            formData.append(formFieldName, originalTextInput.value);
                            console.log(`重排序模式 - 使用隐藏字段原始文本: ${questionNumber}(原始:${originalNumber}): "${originalTextInput.value.substring(0, 30)}..."`);
                        }
                        // 其次使用文本区域的最新数据
                        else if (allTextareaDataByOriginal[originalNumber]) {
                            formData.append(formFieldName, allTextareaDataByOriginal[originalNumber]);
                            console.log(`重排序模式 - 使用原始编号关联文本: ${questionNumber}(原始:${originalNumber}): "${allTextareaDataByOriginal[originalNumber].substring(0, 30)}..."`);
                        }
                        // 再次尝试使用从textareaData中收集的数据
                        else if (textareaData[originalNumber]) {
                            formData.append(formFieldName, textareaData[originalNumber]);
                            console.log(`重排序模式 - 使用收集的文本数据: ${questionNumber}(原始:${originalNumber}): "${textareaData[originalNumber].substring(0, 30)}..."`);
                        } 
                        // 最后使用问题点数据中的文本
                        else {
                            formData.append(formFieldName, data.text || "");
                            console.log(`重排序模式 - 使用问题点数据中的文本: ${questionNumber}(原始:${originalNumber}): "${(data.text || "").substring(0, 30)}..."`);
                        }
                        
                        // 处理所有问题点(1-18)的图片，使用统一的逻辑
                        // 从统一数据结构中获取图片（优先）
                        if (questionInfoByNumber[originalNumber] && questionInfoByNumber[originalNumber].imageSrc) {
                            formData.append(`question_${questionNumber}_image`, questionInfoByNumber[originalNumber].imageSrc);
                            console.log(`重排序模式 - 从统一数据结构中获取图片 - 问题点${questionNumber}(原始:${originalNumber})`);
                        }
                        // 否则使用问题点数据中的图片
                        else if (data.imageSrc) {
                            formData.append(`question_${questionNumber}_image`, data.imageSrc);
                            console.log(`重排序模式 - 使用问题点数据中的图片 - 问题点${questionNumber}(原始:${originalNumber})`);
                        }
                        
                        // 添加原始问题点编号，用于后端更新图片路径
                        if (data.originalNumber) {
                            formData.append(`question_${questionNumber}_original_number`, data.originalNumber);
                        }
                    } else {
                        formData.append(`question_${questionNumber}_text`, "");
                    }
                }
                
                // 添加一个标记，表示使用了重排序模式
                formData.append('using_reordering_mode', 'yes');
            }
            
            // 确保所有收集到的文本都被添加到FormData中
            // 以防有些文本没有对应的问题点数据，但却存在于DOM中
            // 仅在非重排序模式下执行此操作
            if (formSubmitReorderingSetting.toLowerCase() !== 'yes') {
                console.log('非重排序模式 - 添加额外的问题点文本');
                for (const [originalNumber, text] of Object.entries(allTextareaDataByOriginal)) {
                    const formKey = `question_${originalNumber}_text`;
                    if (!formData.has(formKey) && text && text.trim() !== '') {
                        formData.append(formKey, text);
                        console.log(`添加额外的问题点文本 ${originalNumber}: "${text.substring(0, 30)}..."`);
                    }
                }
                for (const [originalNumber, text] of Object.entries(textareaData)) {
                    const formKey = `question_${originalNumber}_text`;
                    if (!formData.has(formKey) && text && text.trim() !== '') {
                        formData.append(formKey, text);
                        console.log(`添加额外的问题点文本 ${originalNumber}: "${text.substring(0, 30)}..."`);
                    }
                }
            } else {
                console.log('重排序模式 - 跳过添加额外的问题点文本，避免重复');
            }
            
            // 添加问题点映射关系，用于后端更新图片路径
            formData.append('question_mapping', JSON.stringify(questionMapping));
            
            // 添加report_code
            const reportCode = document.getElementById('report-code').textContent;
            formData.append('report_code', reportCode);
            console.log('报告编码:', reportCode);
            
            // 添加样本状态复选框值
            const otherCheckbox = document.getElementById('other');
            const trialSampleCheckbox = document.getElementById('trial-sample');
            const bigSampleCheckbox = document.getElementById('big-sample');
            const templateCheckbox = document.getElementById('template');

            // 创建包含所有选中复选框值的数组
            const sampleStatus = [];
            if (trialSampleCheckbox && trialSampleCheckbox.checked) sampleStatus.push('试产样品');
            if (bigSampleCheckbox && bigSampleCheckbox.checked) sampleStatus.push('大货样品');
            if (templateCheckbox && templateCheckbox.checked) sampleStatus.push('重制样板');
            if (otherCheckbox && otherCheckbox.checked) sampleStatus.push('其他');

            // 添加样本状态复选框值
            sampleStatus.forEach(status => {
                formData.append('sample_status[]', status);
            });
            console.log('样本状态:', sampleStatus);
            
            // 记录表单中的关键字段
            console.log('供应商:', formData.get('supplier'));
            console.log('物料料号:', formData.get('material_number'));
            console.log('检验日期:', formData.get('inspection_date'));
            console.log('样板数量:', formData.get('sample_count'));
            
            // 调试输出部分数值字段
            console.log('尺寸1测量值1:', formData.get('size_1_measure_1'));
            console.log('尺寸1测量值2:', formData.get('size_1_measure_2'));
            
            if(confirm('确定要保存修改吗？')) {
                try {
                    // 在用户确认后才执行问题点映射关系保存，并传入true表示已确认
                    storeQuestionMapping(true);
                    
                    // 在提交前再次检查是否有数据的行被隐藏了
                    const hiddenRowsWithData = [];
                    document.querySelectorAll('tr[id^="size-row-"]').forEach(row => {
                        if (row.style.display === 'none') {
                            // 检查隐藏的行是否有数据
                            const isEmpty = checkRowIsEmpty(row);
                            if (!isEmpty) {
                                hiddenRowsWithData.push(row.id);
                                // 记录行的数据
                                console.warn(`发现隐藏但有数据的行: ${row.id}`);
                                const inputs = row.querySelectorAll('input[type="text"], textarea');
                                inputs.forEach(input => {
                                    if (input.value && input.value.trim() !== '') {
                                        console.warn(`- ${input.name}: ${input.value}`);
                                    }
                                });
                            }
                        }
                    });
                    
                    // 如果有隐藏但有数据的行，显示警告
                    if (hiddenRowsWithData.length > 0) {
                        const confirmSubmit = confirm(`警告：发现${hiddenRowsWithData.length}个隐藏但有数据的行(${hiddenRowsWithData.join(', ')})。这些数据将会被提交。是否继续？`);
                        if (!confirmSubmit) {
                            console.log('用户取消提交');
                            // 显示这些行
                            hiddenRowsWithData.forEach(rowId => {
                                const row = document.getElementById(rowId);
                                if (row) {
                                    row.style.display = 'table-row';
                                    console.log(`强制显示行: ${rowId}`);
                                }
                            });
                            // 更新尺寸部分的rowspan
                            updateSizeSectionRowspan();
                            return false;
                        }
                    }
                    
                    // 显示提交中提示
                    showFeedbackMessage('正在提交修改...');
                    
                    // 强制使用正确的URL
                    const host = window.location.origin;
                    const updateUrl = `${host}/Material_Sample_Confirmation_Form_modify/update`;
                    console.log('强制使用正确的提交URL:', updateUrl);
                    
                    const startTime = performance.now();
                    fetch(updateUrl, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        }
                    })
                    .then(response => {
                        console.log('服务器响应状态:', response.status);
                        console.log('响应头:', JSON.stringify([...response.headers]));
                        
                        if (!response.ok) {
                            throw new Error(`服务器返回错误状态: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        const endTime = performance.now();
                        console.log(`表单提交耗时: ${endTime - startTime}ms`);
                        console.log('服务器响应数据:', data);
                        
                        if (data.success) {
                            // 显示成功消息
                            showFeedbackMessage('修改成功！');
                            
                            // 可选：延迟后刷新页面或重定向
                            setTimeout(() => {
                                if (data.redirect_url) {
                                    window.location.href = data.redirect_url;
                                } else {
                                    // 刷新页面显示更新后的数据
                                    window.location.reload();
                                }
                            }, 1500);
                        } else {
                            // 显示错误消息
                            showFeedbackMessage(`保存失败: ${data.error || '未知错误'}`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('提交失败:', error);
                        console.error('错误详情:', error.message);
                        
                        // 检查是否有响应对象
                        if (error instanceof Error && error.message.includes('服务器返回错误状态')) {
                            // 这是一个fetch错误，需要从上一个then的response中获取详细信息
                            // 重新发送一个请求来获取错误详情
                            fetch(updateUrl, {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                                    'Pragma': 'no-cache',
                                    'Expires': '0'
                                }
                            })
                            .then(response => response.json())
                            .then(errorData => {
                                console.error('服务器返回的详细错误信息:', errorData);
                                
                                if (errorData.details) {
                                    console.error('错误详情:', errorData.details);
                                }
                                
                                if (errorData.traceback) {
                                    console.error('完整错误堆栈:', errorData.traceback);
                                }
                                
                                // 显示更详细的错误信息
                                showFeedbackMessage(`服务器错误: ${errorData.error || '未知错误'}`, 'error');
                            })
                            .catch(innerError => {
                                // 如果无法获取详细错误信息，则显示原始错误
                                console.error('无法获取详细错误信息:', innerError);
                                showFeedbackMessage(`提交错误: ${error.message}`, 'error');
                            });
                        } else {
                            // 这是其他类型的错误
                            showFeedbackMessage(`提交过程中发生错误: ${error.message}`, 'error');
                        }
                        
                        console.error('错误堆栈:', error.stack);
                        console.error('提交URL:', updateUrl);
                        console.error('报告编码:', reportCode);
                    });
                } catch (error) {
                    console.error('提交处理过程中发生异常:', error);
                    console.error('异常堆栈:', error.stack);
                    showFeedbackMessage(`提交处理异常: ${error.message}`, 'error');
                }
            }
            
            return false; // 确保返回false以阻止默认提交行为
        }

        // 添加反馈消息函数
        function showFeedbackMessage(message, type = 'info') {
            // 检查是否已存在反馈消息元素
            let feedbackDiv = document.querySelector('.feedback-message');
            
            // 如果不存在，创建一个
            if (!feedbackDiv) {
                feedbackDiv = document.createElement('div');
                feedbackDiv.className = 'feedback-message';
                document.body.appendChild(feedbackDiv);
            }
            
            // 设置消息内容
            feedbackDiv.textContent = message;
            
            // 显示消息
            feedbackDiv.classList.add('show');
            
            // 2秒后隐藏
            setTimeout(() => {
                feedbackDiv.classList.remove('show');
            }, 2000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 原有的DOMContentLoaded事件代码 ...

            // 运行调试函数
            debugFormSubmission();
            
            // 调试信息 - 输出当前页面URL和路径
            console.log('当前页面URL:', window.location.href);
            console.log('当前页面路径:', window.location.pathname);
            console.log('当前页面主机:', window.location.host);
            
            // 添加安全的表单检查
            try {
                // 调试表单配置
                const form = document.getElementById('material-form');
                
                if (form) {
                    console.log('表单配置:', {
                        id: form.id,
                        action: form.action,
                        method: form.method,
                        enctype: form.enctype
                    });
                    
                    // 确保表单有正确的action属性
                    if (!form.action.includes('/Material_Sample_Confirmation_Form_modify/update')) {
                        const host = window.location.origin;
                        form.action = `${host}/Material_Sample_Confirmation_Form_modify/update`;
                        console.log('已自动设置表单action:', form.action);
                    }
                    
                    // 全局阻止表单提交，改用自定义函数
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Form 默认提交被拦截');
                        return submitFormToCorrectUrl(e);
                    });
                } else {
                    console.error('未找到ID为material-form的表单元素，这可能会导致提交功能无法正常工作');
                }
            } catch (e) {
                console.error('初始化表单时出错:', e);
            }

            // 添加全局拦截器，防止所有可能的表单提交
            window.addEventListener('submit', function(e) {
                if (e.target.tagName === 'FORM') {
                    console.log('捕获到全局表单提交事件，已拦截');
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 如果是我们的表单，使用正确的函数提交
                    if (e.target.id === 'material-form') {
                        return submitFormToCorrectUrl(e);
                    }
                }
            }, true);  // 使用捕获阶段
            
            // 初始化所有现有图片
            document.querySelectorAll('.preview-image').forEach(img => {
                addZoomFunctionality(img);
            });
            
            // 初始化所有图片上传框
            initializeImageUploadBoxes(document);
            
            // 初始化尺寸行显示
            initializeSizeRows();
            
            // 使用更长的延迟以确保数据加载完成
            setTimeout(() => {
                console.log('执行延迟检查，确保第4行数据显示...');
                
                // 强制检查并显示有数据的行，特别是第4行
                enforceSizeRowsDisplay();
                
                // 针对第4行特殊处理
                const row4 = document.getElementById('size-row-4');
                if (row4) {
                    // 检查第4行是否有数据
                    const inputs = row4.querySelectorAll('input[type="text"]');
                    let hasData = false;
                    
                    inputs.forEach(input => {
                        if (input.value && input.value.trim() !== '') {
                            hasData = true;
                            console.log(`发现第4行有数据: ${input.name} = "${input.value}"`);
                        }
                    });
                    
                    if (hasData) {
                        // 如果有数据，强制显示
                        row4.style.display = 'table-row';
                        console.log('强制显示第4行，因为检测到它有数据');
                        // 更新rowspan
                        updateSizeSectionRowspan();
                    }
                }
                
                // 检查所有行的显示状态
                document.querySelectorAll('tr[id^="size-row-"]').forEach(row => {
                    console.log(`行 ${row.id} 最终显示状态: ${row.style.display}`);
                });
            }, 500);
            
            // 初始化尺寸行按钮事件
            const addSizeRowBtn = document.getElementById('add-size-row');
            if (addSizeRowBtn) {
                // 移除可能存在的旧事件处理程序
                addSizeRowBtn.removeEventListener('click', addSizeRow);
                // 添加新的事件处理程序
                addSizeRowBtn.addEventListener('click', addSizeRow);
                console.log('已绑定添加尺寸行按钮事件');
            }
            
            const deleteSizeRowBtn = document.getElementById('delete-size-row');
            if (deleteSizeRowBtn) {
                // 移除可能存在的旧事件处理程序
                deleteSizeRowBtn.removeEventListener('click', deleteSizeRow);
                // 添加新的事件处理程序
                deleteSizeRowBtn.addEventListener('click', deleteSizeRow);
                console.log('已绑定删除尺寸行按钮事件');
            }
            
            // 初始化问题点按钮事件
            const addQuestionRowBtn = document.getElementById('add-question-row');
            if (addQuestionRowBtn) {
                addQuestionRowBtn.addEventListener('click', function() {
                    addQuestionRow();
                });
            }
            
            const deleteQuestionRowBtn = document.getElementById('delete-question-row');
            if (deleteQuestionRowBtn) {
                deleteQuestionRowBtn.addEventListener('click', function() {
                    deleteQuestionRow();
                });
            }
            
            // 全局变量跟踪最后点击和最后悬停的上传框
            window.lastClickedBox = null;
            window.lastHoveredBox = null;

            // 直接绑定提交按钮点击事件
            const submitBtn = document.getElementById('submit-btn');
            if(submitBtn) {
                // 已经在HTML中通过onclick属性绑定了事件，这里不需要重复绑定
                console.log('提交按钮已通过HTML属性绑定事件');
                
                // 确保按钮可见
                submitBtn.style.display = 'inline-block';
            } else {
                console.error('无法找到提交按钮!');
            }
        });

        // 为图片添加放大功能
        function addZoomFunctionality(img) {
            // 获取模态框元素
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const closeBtn = document.querySelector('.close');
            const loader = modal.querySelector('.modal-loader');
            const tooltip = modal.querySelector('.tooltip');
            
            if (!modal || !modalImg || !closeBtn) {
                console.error('找不到模态框元素，无法添加图片放大功能');
                return;
            }
            
            // 点击图片时显示模态框
            img.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 显示加载状态
                if (loader) loader.style.display = 'block';
                modal.style.display = 'block';
                
                // 设置图片源
                modalImg.src = this.src;
                
                // 重置变换
                scale = 1;
                translateX = 0;
                translateY = 0;
                modalImg.dataset.scale = 1;
                modalImg.dataset.translateX = 0;
                modalImg.dataset.translateY = 0;
                updateTransform(true);
                
                // 动画显示模态框
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
                
                // 图片加载完成后
                modalImg.onload = function() {
                    if (loader) loader.style.display = 'none';
                    setTimeout(() => {
                        modalImg.classList.add('show');
                        
                        // 显示操作提示
                        if (tooltip) {
                            tooltip.textContent = '滚轮缩放，拖拽移动，双击重置';
                            tooltip.style.left = `${window.innerWidth / 2 - 100}px`;
                            tooltip.style.top = `${window.innerHeight - 100}px`;
                            tooltip.style.opacity = '1';
                            
                            setTimeout(() => {
                                tooltip.style.opacity = '0';
                            }, 3000);
                        }
                    }, 50);
                };
                
                // 防止点击事件冒泡
                return false;
            });
            
            // 点击关闭按钮关闭模态框
            closeBtn.addEventListener('click', function() {
                closeModal();
            });
            
            // 点击模态框背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
            
            // 关闭模态框
            function closeModal() {
                modal.classList.remove('show');
                modalImg.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    translateX = 0;
                    translateY = 0;
                    scale = 1;
                }, 300);
            }
            
            // 变量用于跟踪拖动状态
            let isDragging = false;
            let startX = 0;
            let startY = 0;
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let lastTapTime = 0;
            
            // 更新图片变换
            function updateTransform(isInitial = false) {
                const transform = `
                    translate(-50%, -50%)
                    translate(${translateX}px, ${translateY}px)
                    scale(${scale})
                `;
                
                if (isInitial) {
                    modalImg.style.transform = transform;
                } else {
                    modalImg.style.transition = 'none';
                    modalImg.style.transform = transform;
                    // 强制重排
                    void modalImg.offsetHeight;
                    modalImg.style.transition = 'transform 0.05s ease';
                }
                
                // 更新数据属性
                modalImg.dataset.scale = scale;
                modalImg.dataset.translateX = translateX;
                modalImg.dataset.translateY = translateY;
            }
            
            // 计算动态缩放步长
            function calculateZoomStep(currentScale) {
                // 在不同缩放级别使用不同的步长
                if (currentScale < 0.5) return 0.1;
                if (currentScale < 1) return 0.15;
                if (currentScale < 2) return 0.25;
                if (currentScale < 5) return 0.5;
                return 1;
            }
            
            // 缩放功能
            function zoomImage(direction, fixedScale = null) {
                const zoomStep = fixedScale || calculateZoomStep(scale);
                const oldScale = scale;
                
                if (direction === 'in') {
                    scale = Math.min(10, scale + zoomStep);
                } else {
                    scale = Math.max(0.1, scale - zoomStep);
                }
                
                // 防止过小缩放
                if (scale < 0.1) scale = 0.1;
                
                // 显示当前缩放比例
                if (tooltip) {
                    tooltip.textContent = `缩放: ${Math.round(scale * 100)}%`;
                    tooltip.style.left = `${window.innerWidth / 2 - 50}px`;
                    tooltip.style.top = `${window.innerHeight / 2}px`;
                    tooltip.style.opacity = '1';
                    
                    setTimeout(() => {
                        tooltip.style.opacity = '0';
                    }, 1500);
                }
                
                updateTransform();
            }
            
            // 滚轮缩放
            modal.addEventListener('wheel', function(e) {
                e.preventDefault();
                zoomImage(e.deltaY < 0 ? 'in' : 'out');
            }, { passive: false });
            
            // 拖拽功能
            modalImg.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);
            
            function startDrag(e) {
                if (e.button === 0) { // 左键点击
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    modal.classList.add('grabbing');
                    e.preventDefault();
                    
                    // 检测双击
                    const now = new Date().getTime();
                    const timeDiff = now - lastTapTime;
                    if (timeDiff < 300 && timeDiff > 0) {
                        // 双击重置
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                        if (tooltip) {
                            tooltip.textContent = '视图已重置';
                            tooltip.style.left = `${e.clientX}px`;
                            tooltip.style.top = `${e.clientY}px`;
                            tooltip.style.opacity = '1';
                            
                            setTimeout(() => {
                                tooltip.style.opacity = '0';
                            }, 1500);
                        }
                    }
                    lastTapTime = now;
                }
            }
            
            function drag(e) {
                if (!isDragging) return;
                
                const dx = e.clientX - startX;
                const dy = e.clientY - startY;
                startX = e.clientX;
                startY = e.clientY;
                
                // 动态调整拖动灵敏度
                const speedFactor = scale > 1 ? 1 : Math.sqrt(scale);
                translateX += dx / speedFactor;
                translateY += dy / speedFactor;
                
                updateTransform();
            }
            
            function endDrag() {
                if (isDragging) {
                    isDragging = false;
                    modal.classList.remove('grabbing');
                }
            }
            
            // 键盘控制
            const keyDownHandler = function(e) {
                if (modal.style.display !== 'block') return;
                
                switch (e.key) {
                    case 'Escape':
                        closeModal();
                        break;
                    case 'ArrowLeft':
                        translateX += 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowRight':
                        translateX -= 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowUp':
                        translateY += 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowDown':
                        translateY -= 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case '+':
                    case '=':
                        zoomImage('in', 0.25);
                        e.preventDefault();
                        break;
                    case '-':
                    case '_':
                        zoomImage('out', 0.25);
                        e.preventDefault();
                        break;
                    case '0':
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                        if (tooltip) {
                            tooltip.textContent = '视图已重置';
                            tooltip.style.left = `${window.innerWidth / 2 - 50}px`;
                            tooltip.style.top = `${window.innerHeight / 2}px`;
                            tooltip.style.opacity = '1';
                            
                            setTimeout(() => {
                                tooltip.style.opacity = '0';
                            }, 1500);
                        }
                        e.preventDefault();
                        break;
                }
            };
            
            document.addEventListener('keydown', keyDownHandler);
            
            // 触摸设备支持
            let lastDistance = 0;
            let initialScale = 1;
            
            modalImg.addEventListener('touchstart', function(e) {
                if (e.touches.length === 1) {
                    // 单指拖动
                    isDragging = true;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    
                    // 检测双击
                    const now = new Date().getTime();
                    const timeDiff = now - lastTapTime;
                    if (timeDiff < 300 && timeDiff > 0) {
                        // 双击重置
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                    }
                    lastTapTime = now;
                } else if (e.touches.length === 2) {
                    // 双指缩放
                    isDragging = false;
                    const dx = e.touches[0].clientX - e.touches[1].clientX;
                    const dy = e.touches[0].clientY - e.touches[1].clientY;
                    lastDistance = Math.sqrt(dx * dx + dy * dy);
                    initialScale = scale;
                }
            });
            
            modalImg.addEventListener('touchmove', function(e) {
                e.preventDefault(); // 防止页面滚动
                
                if (e.touches.length === 1 && isDragging) {
                    // 单指拖动
                    const dx = e.touches[0].clientX - startX;
                    const dy = e.touches[0].clientY - startY;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    
                    // 根据缩放级别调整拖动灵敏度
                    const speedFactor = scale > 1 ? 1 : Math.sqrt(scale);
                    translateX += dx / speedFactor;
                    translateY += dy / speedFactor;
                    
                    updateTransform();
                } else if (e.touches.length === 2) {
                    // 双指缩放
                    const dx = e.touches[0].clientX - e.touches[1].clientX;
                    const dy = e.touches[0].clientY - e.touches[1].clientY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const newScale = initialScale * (distance / lastDistance);
                    
                    // 限制缩放范围
                    scale = Math.max(0.1, Math.min(10, newScale));
                    
                    updateTransform();
                    
                    // 显示缩放比例
                    if (tooltip) {
                        tooltip.textContent = `缩放: ${Math.round(scale * 100)}%`;
                        tooltip.style.left = `${window.innerWidth / 2 - 50}px`;
                        tooltip.style.top = `${window.innerHeight / 2}px`;
                        tooltip.style.opacity = '1';
                        
                        setTimeout(() => {
                            tooltip.style.opacity = '0';
                        }, 1500);
                    }
                }
            });
            
            modalImg.addEventListener('touchend', function(e) {
                if (e.touches.length < 1) {
                    isDragging = false;
                }
            });
            
            // 导出缩放控制函数到全局，供控制按钮使用
            window.zoomIn = function() {
                zoomImage('in', 0.25);
            };
            
            window.zoomOut = function() {
                zoomImage('out', 0.25);
            };
            
            window.resetZoom = function() {
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform();
                if (tooltip) {
                    tooltip.textContent = '视图已重置';
                    tooltip.style.left = `${window.innerWidth / 2 - 50}px`;
                    tooltip.style.top = `${window.innerHeight / 2}px`;
                    tooltip.style.opacity = '1';
                    
                    setTimeout(() => {
                        tooltip.style.opacity = '0';
                    }, 1500);
                }
            };
        }

        // 初始化图片上传框函数
        function initializeImageUploadBoxes(container) {
            const imageBoxes = container.querySelectorAll('.image-upload-box');
            imageBoxes.forEach(function(box) {
                // 鼠标移入事件
                box.addEventListener('mouseenter', function() {
                    window.lastHoveredBox = this;
                    this.classList.add('active-hover');
                });
                
                // 鼠标移出事件
                box.addEventListener('mouseleave', function() {
                    this.classList.remove('active-hover');
                    if (window.lastHoveredBox === this) {
                        window.lastHoveredBox = null;
                    }
                });
                
                // 点击事件 - 为手动触发文件选择做准备
                box.addEventListener('click', function(e) {
                    // 如果点击的是删除按钮或粘贴按钮或上传按钮，不触发文件选择
                    if (e.target.classList.contains('delete-btn') || 
                        e.target.classList.contains('paste-button') ||
                        e.target.classList.contains('upload-button') ||
                        e.target.classList.contains('file-input')) {
                        return;
                    }
                    
                    // 检查物料编码
                    if (!validateMaterialNumber()) {
                        return;
                    }
                    
                    // 检查是否有旧图片
                    if (box.classList.contains('has-image')) {
                        // 有旧图片，显示确认对话框
                        if (!confirm('确定要删除旧图片并上传新图片吗？')) {
                            return; // 用户取消操作
                        }
                    }
                    
                    // 更新最后点击的上传框
                    window.lastClickedBox = this;

                    // 这里可以添加打开文件选择框的代码，但需要结合后端接口
                });
                
                // 拖放功能
                box.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.add('drag-over');
                });
                
                box.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('drag-over');
                });
                
                box.addEventListener('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.classList.remove('drag-over');
                    
                    // 检查物料编码
                    if (!validateMaterialNumber()) {
                        return;
                    }
                    
                    // 检查是否有旧图片
                    if (box.classList.contains('has-image')) {
                        // 有旧图片，显示确认对话框
                        if (!confirm('确定要删除旧图片并上传新图片吗？')) {
                            return; // 用户取消操作
                        }
                    }
                    
                    const dt = e.dataTransfer;
                    const files = dt.files;
                    
                    if (files.length) {
                        // 处理拖放的文件
                        handleDroppedFile(this, files[0]);
                    }
                });

                // 创建文件输入框
                let fileInput = box.querySelector('.file-input');
                if (!fileInput) {
                    fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = 'image/*';
                    fileInput.className = 'file-input';
                    fileInput.style.display = 'none';
                    box.appendChild(fileInput);
                    
                    // 文件选择后的处理
                    fileInput.addEventListener('change', function(e) {
                        if (this.files && this.files.length > 0) {
                            handleDroppedFile(box, this.files[0]);
                            // 重置文件输入，允许重复选择同一文件
                            this.value = '';
                        }
                    });
                }
                
                // 添加上传按钮
                if (!box.querySelector('.upload-button')) {
                    const uploadButton = document.createElement('button');
                    uploadButton.className = 'upload-button';
                    uploadButton.textContent = '📂';
                    uploadButton.type = 'button';
                    
                    // 添加悬停效果
                    uploadButton.addEventListener('mouseover', function() {
                        this.textContent = '📂 上传';
                        this.style.width = '70px';
                    });
                    
                    uploadButton.addEventListener('mouseout', function() {
                        this.textContent = '📂';
                        this.style.width = '28px';
                    });
                    
                    uploadButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        if (!validateMaterialNumber()) {
                            return;
                        }
                        
                        // 检查是否有旧图片
                        if (box.classList.contains('has-image')) {
                            // 有旧图片，显示确认对话框
                            if (!confirm('确定要删除旧图片并上传新图片吗？')) {
                                return; // 用户取消操作
                            }
                        }
                        
                        window.lastClickedBox = box;
                        window.lastHoveredBox = box;
                        
                        // 点击时触发文件选择框
                        const fileInput = box.querySelector('.file-input');
                        if (fileInput) {
                            fileInput.click();
                        }
                    });
                    
                    box.appendChild(uploadButton);
                }

                // 添加复制按钮
                if (!box.querySelector('.copy-button')) {
                    const copyButton = document.createElement('button');
                    copyButton.className = 'copy-button';
                    copyButton.textContent = '📑';
                    copyButton.type = 'button';
                    
                    // 默认隐藏复制按钮，只在有图片时显示
                    copyButton.style.display = box.classList.contains('has-image') ? 'block' : 'none';
                    
                    // 添加悬停效果
                    copyButton.addEventListener('mouseover', function() {
                        this.textContent = '📑 复制';
                        this.style.width = '70px';
                    });
                    
                    copyButton.addEventListener('mouseout', function() {
                        this.textContent = '📑';
                        this.style.width = '28px';
                    });
                    
                    copyButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        // 获取预览图片
                        const previewImage = box.querySelector('.preview-image');
                        if (!previewImage) {
                            showFeedbackMessage('没有图片可复制', 'error');
                            return;
                        }
                        
                        // 复制图片到剪贴板
                        copyImageToClipboard(previewImage.src);
                    });
                    
                    box.appendChild(copyButton);
                } else {
                    // 更新现有复制按钮的显示状态
                    const copyButton = box.querySelector('.copy-button');
                    copyButton.style.display = box.classList.contains('has-image') ? 'block' : 'none';
                }

                // 添加粘贴按钮
                if (!box.querySelector('.paste-button')) {
                    const pasteButton = document.createElement('button');
                    pasteButton.className = 'paste-button';
                    pasteButton.textContent = '📋';
                    pasteButton.type = 'button';
                    pasteButton.title = '点击从剪贴板粘贴图片';
                    
                    // 添加悬停效果
                    pasteButton.addEventListener('mouseover', function() {
                        this.textContent = '📋 粘贴';
                        this.style.width = '70px';
                    });
                    
                    pasteButton.addEventListener('mouseout', function() {
                        this.textContent = '📋';
                        this.style.width = '28px';
                    });
                    
                    pasteButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        if (!validateMaterialNumber()) {
                            return;
                        }
                        
                        // 检查是否有旧图片
                        if (box.classList.contains('has-image')) {
                            // 有旧图片，显示确认对话框
                            if (!confirm('确定要删除旧图片并上传新图片吗？')) {
                                return; // 用户取消操作
                            }
                        }
                        
                        window.lastClickedBox = box;
                        window.lastHoveredBox = box;
                        
                        // 请求剪贴板权限并获取剪贴板内容
                        navigator.clipboard.read()
                            .then(clipboardItems => {
                                // 遍历剪贴板中的所有项目
                                for (const clipboardItem of clipboardItems) {
                                    // 检查是否有图片类型
                                    if (clipboardItem.types.includes('image/png') || 
                                        clipboardItem.types.includes('image/jpeg') || 
                                        clipboardItem.types.includes('image/gif')) {
                                        
                                        // 获取图片类型
                                        const imageType = clipboardItem.types.find(type => type.startsWith('image/'));
                                        
                                        // 获取图片数据
                                        clipboardItem.getType(imageType)
                                            .then(blob => {
                                                // 处理图片数据
                                                handleDroppedFile(box, blob);
                                                showFeedbackMessage('图片已从剪贴板粘贴并处理中...');
                                            })
                                            .catch(error => {
                                                console.error('获取剪贴板图片数据失败:', error);
                                                showFeedbackMessage('获取剪贴板图片失败', 'error');
                                            });
                                        
                                        return; // 找到图片后退出循环
                                    }
                                }
                                
                                // 如果没有找到图片
                                showFeedbackMessage('剪贴板中没有可用的图片', 'warning');
                            })
                            .catch(error => {
                                console.error('读取剪贴板失败:', error);
                                showFeedbackMessage('无法访问剪贴板，请确保已授予权限', 'error');
                            });
                    });
                    
                    box.appendChild(pasteButton);
                }
            });
        }

        // 处理拖放的文件
        function handleDroppedFile(box, file) {
            // 检查文件类型
            if (file && file.type && !file.type.startsWith('image/')) {
                showFeedbackMessage('只能上传图片文件', 'error');
                return;
            }
            
            // 获取问题编号
            const questionNumber = box.getAttribute('data-question-number');
            const reportCode = document.getElementById('report-code').textContent;
            
            // 先检查是否有旧图片，如果有先删除
            const hasExistingImage = box.classList.contains('has-image');
            
            // 定义deleteOldImage变量
            let deleteOldImage;
            
            if (hasExistingImage) {
                // 用户在外部事件中已经确认，直接删除旧图片
                deleteOldImage = fetch('/Material_Sample_Confirmation_Form_modify/delete_image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question_number: questionNumber,
                        report_code: reportCode
                    })
                }).then(res => res.json()).catch(err => {
                    console.warn('删除旧图片失败，但会继续上传新图片:', err);
                    return { success: false };
                });
            } else {
                // 没有旧图片，直接继续
                deleteOldImage = Promise.resolve({ success: true });
            }
            
            // 显示上传中状态
            box.classList.add('uploading');
            
            // 确保文件有一个名称，如果是粘贴的图片可能没有名称
            const fileName = file.name || `clipboard_image_${new Date().getTime()}.jpg`;
            
            // 在删除完成后再开始上传
            deleteOldImage.then(() => {
                // 压缩图片
                return compressImage(file).then(compressedBlob => {
                    // 创建FormData对象上传到服务器
                    const formData = new FormData();
                    formData.append('image', new File([compressedBlob], fileName, {type: 'image/jpeg'}));
                    formData.append('question_number', questionNumber);
                    formData.append('report_code', reportCode);
                    
                    // 发送到服务器
                    return fetch('/Material_Sample_Confirmation_Form_modify/upload_image', {
                        method: 'POST',
                        body: formData
                    });
                });
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`服务器返回状态码: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 更新预览图片
                    const previewImage = box.querySelector('.preview-image') || document.createElement('img');
                    previewImage.className = 'preview-image';
                    
                    // 使用服务器返回的图片路径
                    if (data.site_path) {
                        previewImage.src = data.site_path;
                    } else {
                        // 如果没有返回路径，使用本地对象URL（临时）
                        previewImage.src = URL.createObjectURL(file);
                    }
                    
                    // 如果预览图片不在DOM中，添加它
                    if (!box.contains(previewImage)) {
                        box.appendChild(previewImage);
                    }
                    
                    // 为新上传的图片添加放大功能
                    addZoomFunctionality(previewImage);
                    console.log('已为新上传图片添加放大功能');
                    
                    // 更新界面状态
                    box.classList.add('has-image');
                    box.classList.remove('uploading');
                    
                    // 隐藏上传提示，显示删除按钮
                    const uploadOverlay = box.querySelector('.upload-overlay');
                    if (uploadOverlay) {
                        uploadOverlay.style.display = 'none';
                    }
                    
                    // 显示复制按钮
                    const copyButton = box.querySelector('.copy-button');
                    if (copyButton) {
                        copyButton.style.display = 'block';
                    }
                    
                    // 如果没有删除按钮，添加一个
                    let deleteBtn = box.querySelector('.delete-btn');
                    if (!deleteBtn) {
                        deleteBtn = document.createElement('button');
                        deleteBtn.className = 'delete-btn';
                        deleteBtn.textContent = '×';
                        deleteBtn.type = 'button';
                        deleteBtn.onclick = null;
                        
                        // 添加事件监听器，明确阻止事件冒泡和默认行为
                        deleteBtn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            deleteImage(questionNumber);
                        });
                        
                        // 重新绑定事件处理程序
                        deleteBtn.onmouseover = null;
                        deleteBtn.onmouseout = null;
                        
                        // 使用addEventListener添加事件
                        deleteBtn.addEventListener('mouseover', function() {
                            setTimeout(() => {
                                this.textContent = '删除';
                            }, 100);
                        });
                        
                        deleteBtn.addEventListener('mouseout', function() {
                            this.textContent = '×';
                        });
                        
                        box.appendChild(deleteBtn);
                    }
                    deleteBtn.style.display = 'block';
                    // 确保显示 × 符号
                    deleteBtn.textContent = '×';
                    
                    // 重新绑定事件处理程序
                    deleteBtn.onmouseover = function() {
                        setTimeout(() => {
                            this.textContent = '删除';
                        }, 100);
                    };
                    deleteBtn.onmouseout = function() {
                        this.textContent = '×';
                    };
                    
                    showFeedbackMessage('图片上传成功');
                } else {
                    box.classList.remove('uploading');
                    showFeedbackMessage(data.message || '图片上传失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                box.classList.remove('uploading');
                showFeedbackMessage('上传过程中发生错误: ' + error.message, 'error');
            })
            .catch(err => {
                console.error('压缩图片失败:', err);
                box.classList.remove('uploading');
                showFeedbackMessage('图片处理失败', 'error');
            });
        }

        // 压缩图片函数
        function compressImage(file) {
            return new Promise((resolve) => {
                // 处理特殊情况：如果文件已经是Blob，但没有type属性（常见于粘贴的图片）
                const fileToProcess = file.type ? file : new Blob([file], { type: 'image/png' });
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        let width = img.width;
                        let height = img.height;

                        // 按比例缩小尺寸
                        if (width > 1600 || height > 1600) {
                            const ratio = Math.min(1600 / width, 1600 / height);
                            width = Math.floor(width * ratio);
                            height = Math.floor(height * ratio);
                        }

                        canvas.width = width;
                        canvas.height = height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);

                        // 动态调整质量
                        let quality = 0.8;
                        if (fileToProcess.size > 1024 * 1024) {
                            // 如果原图大于1MB，降低质量
                            quality = 0.7;
                        }
                        
                        canvas.toBlob((blob) => {
                            if (blob.size > 1024 * 1024) {
                                // 如果压缩后仍大于1MB，进一步降低质量
                                quality = Math.min(0.6, (1024 * 1024) / blob.size);
                                canvas.toBlob(resolve, 'image/jpeg', quality);
                            } else {
                                resolve(blob);
                            }
                        }, 'image/jpeg', quality);
                    };
                    img.onerror = function() {
                        console.error('图片加载失败');
                        // 如果加载失败，返回原始的blob
                        resolve(fileToProcess);
                    };
                    img.src = e.target.result;
                };
                reader.onerror = function() {
                    console.error('文件读取失败');
                    // 如果读取失败，返回原始的blob
                    resolve(fileToProcess);
                };
                reader.readAsDataURL(fileToProcess);
            });
        }

        // 复制图片到剪贴板函数
        function copyImageToClipboard(imageSrc) {
            // 创建一个canvas元素
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 创建一个新的图片对象
            const img = new Image();
            
            // 设置跨域属性，允许加载跨域图片
            img.crossOrigin = 'anonymous';
            
            // 图片加载完成后处理
            img.onload = function() {
                // 设置canvas尺寸与图片相同
                canvas.width = img.width;
                canvas.height = img.height;
                
                // 将图片绘制到canvas上
                ctx.drawImage(img, 0, 0);
                
                // 尝试从canvas获取图片数据并复制到剪贴板
                try {
                    canvas.toBlob(function(blob) {
                        // 创建ClipboardItem对象
                        const data = [new ClipboardItem({ 'image/png': blob })];
                        
                        // 写入剪贴板
                        navigator.clipboard.write(data)
                            .then(() => {
                                showFeedbackMessage('图片已复制到剪贴板');
                            })
                            .catch(err => {
                                console.error('复制到剪贴板失败:', err);
                                showFeedbackMessage('复制到剪贴板失败，请尝试其他方法', 'error');
                                
                                // 备用方法：打开图片在新窗口，让用户手动复制
                                const newWindow = window.open();
                                newWindow.document.write('<img src="' + canvas.toDataURL('image/png') + '" alt="复制此图片">');
                                newWindow.document.write('<p>请右键点击图片并选择"复制图片"</p>');
                            });
                    }, 'image/png');
                } catch (err) {
                    console.error('创建图片Blob失败:', err);
                    showFeedbackMessage('复制图片失败，浏览器可能不支持此功能', 'error');
                }
            };
            
            // 图片加载错误处理
            img.onerror = function() {
                console.error('加载图片失败:', imageSrc);
                showFeedbackMessage('加载图片失败，无法复制', 'error');
            };
            
            // 设置图片源
            img.src = imageSrc;
            
            // 如果图片已经被缓存，可能不会触发onload事件，手动触发
            if (img.complete) {
                img.onload();
            }
        }

        // 添加删除图片的函数
        function deleteImage(questionNumber) {
            // 阻止事件冒泡和默认行为
            event.preventDefault();
            event.stopPropagation();
            
            if (!confirm('确定要删除此图片吗？')) {
                return;
            }
            
            console.log(`删除问题点 ${questionNumber} 的图片`);
            
            const reportCode = document.getElementById('report-code').textContent;
            const box = document.querySelector(`.image-upload-box[data-question-number="${questionNumber}"]`);
            
            if (!box) {
                console.error('找不到对应的图片容器:', questionNumber);
                return;
            }
            
            // 获取关联的文本区域
            const textareaSelector = `textarea[name="question_${questionNumber}_text"]`;
            const textarea = document.querySelector(textareaSelector);
            
            // 获取并保存文本内容（如果有）
            const textContent = textarea ? textarea.value : '';
            const hasText = textContent && textContent.trim() !== '';
            
            console.log(`问题点 ${questionNumber} 的文本内容: "${textContent}"`);
            
            // 获取预览图片
            const previewImage = box.querySelector('.preview-image');
            if (!previewImage) {
                console.warn('没有找到预览图片，无需删除');
                return;
            }
            
            // 保存原始图片URL，用于服务器端删除
            const imageSrc = previewImage.src;
            
            // 清除预览图片
            previewImage.remove();
            
            // 移除has-image类
            box.classList.remove('has-image');
            
            // 显示上传提示
            const uploadOverlay = box.querySelector('.upload-overlay');
            if (uploadOverlay) {
                uploadOverlay.style.display = 'block';
                uploadOverlay.textContent = '无图片';
            }
            
            // 隐藏删除按钮
            const deleteBtn = box.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.style.display = 'none';
            }
            
            // 隐藏复制按钮
            const copyButton = box.querySelector('.copy-button');
            if (copyButton) {
                copyButton.style.display = 'none';
            }
            
            // 清除隐藏的图片路径输入框，但保留原始编号信息
            const imagePathInput = document.querySelector(`input[name="question_${questionNumber}_image"]`);
            if (imagePathInput) {
                imagePathInput.value = '';
            }
            
            // 确保原始编号信息被保留
            const originalNumberInput = document.querySelector(`input[name="question_${questionNumber}_original_number"]`);
            if (!originalNumberInput) {
                // 如果不存在原始编号输入，创建一个
                const newInput = document.createElement('input');
                newInput.type = 'hidden';
                newInput.name = `question_${questionNumber}_original_number`;
                newInput.value = questionNumber;
                document.getElementById('material-form').appendChild(newInput);
                console.log(`为问题点 ${questionNumber} 创建了原始编号隐藏字段`);
            }
            
            // 向服务器发送删除请求
            fetch('/Material_Sample_Confirmation_Form_modify/delete_image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    report_code: reportCode,
                    question_number: questionNumber
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('图片删除成功:', data.message);
                    
                    // 更新问题点数据，确保删除图片后的状态正确保存
                    const { questionData, questionMapping } = getQuestionPointsData();
                    
                    // 查找对应的问题点数据
                    const questionIndex = questionData.findIndex(data => 
                        data && parseInt(data.originalNumber) === parseInt(questionNumber));
                    
                    if (questionIndex !== -1) {
                        // 更新问题点数据，移除图片信息但保留文本内容
                        questionData[questionIndex].imageSrc = null;
                        questionData[questionIndex].hasImage = false;
                        questionData[questionIndex].imageName = '';
                        
                        // 确保文本内容不变
                        if (textarea) {
                            questionData[questionIndex].text = textarea.value || '';
                        }
                        
                        console.log(`已更新问题点 ${questionNumber} 的数据，移除图片信息，保留文本内容: "${questionData[questionIndex].text}"`);
                    } else {
                        // 如果在questionData中找不到对应的问题点，创建一个新的
                        if (textarea && hasText) {
                            // 创建一个新的问题点数据
                            const newData = {
                                originalNumber: questionNumber.toString(),
                                text: textContent,
                                imageSrc: null,
                                imageName: '',
                                hasImage: false,
                                hasText: true,
                                position: { row: 0, col: 0 }, // 默认位置
                                currentPosition: parseInt(questionNumber)
                            };
                            
                            // 将新数据添加到questionData
                            questionData.push(newData);
                            console.log(`为问题点 ${questionNumber} 创建了新的数据项，保留文本内容: "${textContent}"`);
                        }
                    }
                    
                    // 检查是否需要重排序问题点
                    const enableReordering = getImageReorderingSetting();
                    console.log(`删除问题点图片后检查图片重排序设置: ${enableReordering}`);
                    
                    // 创建一个隐藏字段来保存原始文本内容
                    const textContentField = document.querySelector(`input[name="question_${questionNumber}_original_text"]`);
                    if (!textContentField && hasText) {
                        const newInput = document.createElement('input');
                        newInput.type = 'hidden';
                        newInput.name = `question_${questionNumber}_original_text`;
                        newInput.value = textContent;
                        document.getElementById('material-form').appendChild(newInput);
                        console.log(`为问题点 ${questionNumber} 创建了原始文本隐藏字段: "${textContent.substring(0, 30)}..."`);
                    }
                    
                    // 确保文本内容保持不变
                    if (textarea) {
                        if (hasText) {
                            console.log(`确保问题点 ${questionNumber} 的文本内容保持不变: "${textContent}"`);
                            // 保存原有文本内容
                            textarea.value = textContent;
                            
                            // 添加一个数据属性来标记这个文本区域不应该被修改
                            textarea.setAttribute('data-preserve-text', 'true');
                        }
                        
                        // 添加一个事件监听器，确保文本内容不会被意外修改
                        textarea.addEventListener('change', function(e) {
                            const preserveText = this.getAttribute('data-preserve-text');
                            if (preserveText === 'true') {
                                console.log(`问题点 ${questionNumber} 的文本被修改，但由于设置了保护，将恢复原始内容`);
                                if (textContent) {
                                    this.value = textContent;
                                }
                            }
                        });
                    }
                    
                    if (enableReordering.toLowerCase() === 'yes') {
                        console.log('图片重排序设置为"是"，执行问题点重排序');
                        // 保存当前映射关系，但不重排序，不触发重命名
                        storeQuestionMapping(false);
                    } else {
                        console.log('图片重排序设置为"否"，跳过问题点重排序');
                        // 保存当前状态但不触发重排序
                        storeQuestionMapping(false);
                    }
                } else {
                    console.error('图片删除失败:', data.message);
                }
            })
            .catch(error => {
                console.error('删除图片时发生错误:', error);
            });
        }
        
        // 获取当前问题点行数
        function getCurrentQuestionRowCount() {
            return document.querySelectorAll('tr.question-row').length;
        }
        
        // 增加问题点行
        function addQuestionRow() {
            // 获取当前问题行数量
            const questionRows = document.querySelectorAll('tr.question-row');
            
            // 检查是否达到最大行数限制（6行）
            if (questionRows.length >= 6) {
                showFeedbackMessage('已达到最大问题点行数（6行），不能继续添加', 'warning');
                return;
            }
            
            const currentRowCount = getCurrentQuestionRowCount();
            const nextRowNumber = currentRowCount + 1;
            
            // 创建图片行
            const imageRow = document.createElement('tr');
            imageRow.className = 'question-row';
            
            // 创建文本行
            const textRow = document.createElement('tr');
            textRow.className = 'question-text-row';
            
            // 填充图片行
            for (let i = 0; i < 3; i++) {
                const questionNumber = `new-${nextRowNumber}-${i}`;
                const tdImage = document.createElement('td');
                tdImage.colSpan = i === 0 ? 4 : 5;
                tdImage.className = 'image-upload-cell';
                
                tdImage.innerHTML = `
                    <div class="image-upload-box" data-question-number="${questionNumber}">
                        <div class="upload-overlay">无图片</div>
                    </div>
                `;
                imageRow.appendChild(tdImage);
                
                // 填充文本行
                const tdText = document.createElement('td');
                tdText.colSpan = i === 0 ? 4 : 5;
                tdText.style.height = '10px';
                tdText.innerHTML = `<textarea name="question_${questionNumber}_text" placeholder="请输入问题描述"></textarea>`;
                textRow.appendChild(tdText);
            }
            
            // 查找问题点按钮行
            const buttonsRow = document.getElementById('question-buttons-row');
            
            // 在按钮行前插入新行
            buttonsRow.parentNode.insertBefore(imageRow, buttonsRow);
            buttonsRow.parentNode.insertBefore(textRow, buttonsRow);
            
            // 初始化新添加的图片上传框
            initializeImageUploadBoxes(imageRow);
            
            // 为新添加行的元素绑定事件
            imageRow.querySelectorAll('.preview-image').forEach(img => {
                addZoomFunctionality(img);
            });
            
            // 显示成功消息
            showFeedbackMessage(`已添加新的问题点行`);
        }
        
        // 删除最后一行问题点
        function deleteQuestionRow() {
            const questionRows = document.querySelectorAll('tr.question-row');
            const textRows = document.querySelectorAll('tr.question-text-row');
            
            // 至少保留2行问题点(共6个问题点)
            if (questionRows.length <= 2) {
                showFeedbackMessage('至少需要保留6个问题点', 'warning');
                return;
            }
            
            // 获取最后一行
            const lastQuestionRow = questionRows[questionRows.length - 1];
            const lastTextRow = textRows[textRows.length - 1];
            
            // 检查该行是否有图片或文本内容
            const hasImage = lastQuestionRow.querySelector('.image-upload-box.has-image');
            const textareas = lastTextRow.querySelectorAll('textarea');
            let hasContent = false;
            
            textareas.forEach(textarea => {
                if (textarea.value.trim() !== '') {
                    hasContent = true;
                }
            });
            
            if (hasImage || hasContent) {
                // 有数据，询问确认
                if (!confirm('最后一行问题点包含数据，确定要删除吗？')) {
                    return;
                }
            }
            
            // 删除行
            lastQuestionRow.remove();
            lastTextRow.remove();
            
            // 检查是否需要重排序问题点
            const enableReordering = getImageReorderingSetting();
            console.log(`删除问题点后检查图片重排序设置: ${enableReordering}`);
            if (enableReordering.toLowerCase() === 'yes') {
                console.log('图片重排序设置为"是"，执行问题点重排序');
                reorderQuestionPoints();
            } else {
                console.log('图片重排序设置为"否"，跳过问题点重排序');
                // 当重排序设置为"否"时，不执行重排序，但仍需更新数据
                const { questionData } = getQuestionPointsData();
                // 保存当前映射关系但不触发重排序
                storeQuestionMapping(false);
            }
            
            // 显示成功消息
            showFeedbackMessage('已删除最后一行问题点');
        }
        
        // 添加尺寸行
        function addSizeRow() {
            console.log('开始添加尺寸行...');
            
            // 获取当前所有尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            const visibleSizeRows = Array.from(allSizeRows).filter(row => !row.classList.contains('empty-size-row'));
            
            console.log(`当前共有 ${allSizeRows.length} 个尺寸行，其中 ${visibleSizeRows.length} 个可见`);
            
            // 检查是否达到最大行数（例如30行）
            if (visibleSizeRows.length >= 30) {
                showFeedbackMessage('已达到最大尺寸行数，不能继续添加', 'warning');
                return;
            }
            
            // 获取当前最大行号
            let maxRowNumber = 5; // 默认至少有5行
            allSizeRows.forEach(row => {
                const rowId = row.id;
                if (rowId && rowId.startsWith('size-row-')) {
                    const rowNum = parseInt(rowId.replace('size-row-', ''));
                    if (!isNaN(rowNum) && rowNum > maxRowNumber) {
                        maxRowNumber = rowNum;
                    }
                }
            });
            
            // 确定新行的编号 - 使用最大行号+1
            const newRowNumber = maxRowNumber + 1;
            console.log(`新行编号: ${newRowNumber}`);
            
            // 检查是否已存在该行，但被标记为empty-size-row
            const existingRow = document.getElementById(`size-row-${newRowNumber}`);
            if (existingRow) {
                // 如果行已存在但被隐藏，移除empty-size-row类使其显示
                existingRow.classList.remove('empty-size-row');
                console.log(`行 ${newRowNumber} 已存在，已移除empty-size-row类使其显示`);
                
                // 更新尺寸部分的rowspan
                updateSizeSectionRowspan();
                
                showFeedbackMessage(`已显示尺寸行 #${newRowNumber}`);
                return;
            }
            
            // 创建新行
            const newRow = document.createElement('tr');
            newRow.className = 'custom-row-height dynamic-size-row';
            newRow.id = `size-row-${newRowNumber}`;
            
            // 行内容模板
            newRow.innerHTML = `
                <td class="align-center">${newRowNumber}</td>
                <td data-id="size-${newRowNumber}" data-image="true" class="align-center">
                    <input type="text" name="size_${newRowNumber}_position" id="size-${newRowNumber}" value="">
                </td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_value" id="size-${newRowNumber}-value" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_min" id="size-${newRowNumber}-min" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_max" id="size-${newRowNumber}-max" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_1" id="size-${newRowNumber}-measure-1" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_2" id="size-${newRowNumber}-measure-2" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_3" id="size-${newRowNumber}-measure-3" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_4" id="size-${newRowNumber}-measure-4" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_5" id="size-${newRowNumber}-measure-5" value=""></td>
                <td>
                    <label><input type="radio" name="size_${newRowNumber}_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_${newRowNumber}_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_${newRowNumber}_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_${newRowNumber}_note"></textarea></td>
            `;
            
            // 找到按钮行并在它前面插入新行
            const buttonsRow = document.getElementById('size-buttons-row');
            if (buttonsRow) {
                buttonsRow.parentNode.insertBefore(newRow, buttonsRow);
                console.log('已在按钮行前插入新行');
            } else {
                // 如果找不到按钮行，尝试插入到最后一个现有尺寸行后面
                const lastSizeRow = allSizeRows[allSizeRows.length - 1];
                if (lastSizeRow && lastSizeRow.parentNode) {
                    lastSizeRow.parentNode.insertBefore(newRow, lastSizeRow.nextSibling);
                    console.log('已在最后一行后插入新行');
                } else {
                    console.error('无法找到合适位置插入新行');
                }
            }
            
            // 更新尺寸部分的rowspan
            updateSizeSectionRowspan();
            
            console.log(`新行 #${newRowNumber} 已添加`);
            
            showFeedbackMessage(`已添加尺寸行 #${newRowNumber}`);
            
            // 确保新行可见
            newRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        
        // 删除最后一个尺寸行
        function deleteSizeRow() {
            // 获取所有非空的尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            const visibleSizeRows = Array.from(allSizeRows).filter(row => !row.classList.contains('empty-size-row'));
            
            // 保留至少5行尺寸行
            if (visibleSizeRows.length <= 5) {
                showFeedbackMessage('至少需要保留5个尺寸行', 'warning');
                return;
            }
            
            // 获取最后一个可见行（跳过前5行）
            let lastVisibleRow = null;
            for (let i = visibleSizeRows.length - 1; i >= 0; i--) {
                const row = visibleSizeRows[i];
                const rowNum = parseInt(row.id.replace('size-row-', ''));
                if (rowNum > 5) {
                    lastVisibleRow = row;
                    break;
                }
            }
            
            if (!lastVisibleRow) {
                showFeedbackMessage('找不到可删除的尺寸行', 'warning');
                return;
            }
            
            // 检查该行是否有数据
            const hasData = !checkRowIsEmpty(lastVisibleRow);
            
            if (hasData) {
                // 有数据，询问确认
                if (!confirm('最后一行尺寸包含数据，确定要删除吗？')) {
                    return;
                }
            }
            
            // 清空行内容而不是删除或隐藏
            const inputs = lastVisibleRow.querySelectorAll('input[type="text"], textarea');
            inputs.forEach(input => {
                input.value = '';
            });
            
            // 清空单选按钮选择
            const radios = lastVisibleRow.querySelectorAll('input[type="radio"]');
            radios.forEach(radio => {
                radio.checked = false;
            });
            
            // 标记为空行
            lastVisibleRow.classList.add('empty-size-row');
            
            // 更新尺寸部分的rowspan
            updateSizeSectionRowspan();
            
            showFeedbackMessage('已删除最后一行尺寸');
        }
        
        // 更新尺寸部分的rowspan
        function updateSizeSectionRowspan() {
            const sizeSectionCell = document.getElementById('size-section-cell');
            if (!sizeSectionCell) return;
            
            // 获取所有可见的尺寸行数量
            const visibleSizeRows = Array.from(document.querySelectorAll('tr[id^="size-row-"]'))
                .filter(row => !row.classList.contains('empty-size-row'));
            
            // 设置rowspan，加2是因为要包含标题行和子标题行
            sizeSectionCell.rowSpan = visibleSizeRows.length + 2;
            console.log(`更新尺寸部分rowspan为 ${visibleSizeRows.length + 2}`);
        }

        // 用于检查尺寸行是否为空的函数
        function checkRowIsEmpty(row) {
            // 使用增强版的检查函数
            return checkRowIsReallyEmpty(row);
        }

        // 检查物料编码是否填写
        function validateMaterialNumber() {
            const materialNumber = document.getElementById('material-number');
            if (!materialNumber || !materialNumber.value || materialNumber.value.trim() === '') {
                showFeedbackMessage('请先填写物料料号', 'warning');
                materialNumber.focus();
                return false;
            }
            return true;
        }

        // 初始化尺寸行显示
        function initializeSizeRows() {
            console.log('初始化尺寸行显示...');
            
            // 获取所有尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            console.log(`找到 ${allSizeRows.length} 个尺寸行`);

            // 清理所有行的样式类，确保重置到初始状态
            allSizeRows.forEach(row => {
                row.classList.remove('confirmed-empty-row');
            });
            
            // 检查每一行，根据规则决定是否显示
            allSizeRows.forEach(row => {
                if (row.id && row.id.startsWith('size-row-')) {
                    // 获取行号
                    const rowNumber = parseInt(row.id.replace('size-row-', ''));
                    
                    // 检查是否真正为空
                    const isEmpty = checkRowIsReallyEmpty(row);
                    
                    // 处理行状态
                    if (isEmpty) {
                        // 前3行始终显示，无论是否为空
                        if (rowNumber <= 3) {
                            row.classList.remove('confirmed-empty-row');
                            console.log(`行 ${row.id} 是空的，但是是前3行，确保显示`);
                        } else {
                            // 第4行及以后的空行隐藏
                            row.classList.add('confirmed-empty-row');
                            console.log(`行 ${row.id} 是空的，且不是前3行，标记为隐藏`);
                        }
                    } else {
                        // 如果行有任何数据，确保显示
                        row.classList.remove('confirmed-empty-row');
                        console.log(`行 ${row.id} 有数据，确保显示`);
                        
                        // 打印行内的数据，用于调试
                        logRowData(row);
                    }
                }
            });
            
            // 更新尺寸部分的rowspan
            updateSizeSectionRowspan();
        }

        // 增强版的行检查函数，更严格地判断行是否真正为空
        function checkRowIsReallyEmpty(row) {
            if (!row) return true;
            
            // 获取行中的所有输入框和文本区域
            const inputs = row.querySelectorAll('input[type="text"], textarea');
            
            // 获取行号用于日志
            const rowId = row.id || 'unknown';
            
            if (inputs.length === 0) {
                console.log(`行 ${rowId} 没有输入元素，视为空行`);
                return true;
            }

            // 检查是否所有输入框都为空
            let hasValue = false;
            
            // 检查所有文本框是否真的为空
            inputs.forEach(input => {
                if (input && input.value) {
                    const value = input.value.trim();
                    if (value && value !== '' && value !== 'NULL') {
                        // 如果是数值字段，确认是真正的数值
                        if (input.name && (input.name.includes('_measure_') || 
                           input.name.includes('_min') || 
                           input.name.includes('_max') || 
                           input.name.includes('_value'))) {
                            // 数值字段，如果是真正的数字且不为0，则认为有值
                            if (!isNaN(parseFloat(value)) && parseFloat(value) !== 0) {
                                console.log(`行 ${rowId} 中字段 ${input.name} 有有效数值: "${value}"`);
                                hasValue = true;
                            }
                        } else {
                            // 非数值字段，任何非空值都认为有效
                            console.log(`行 ${rowId} 中字段 ${input.name} 有非空值: "${value}"`);
                            hasValue = true;
                        }
                    }
                }
            });
            
            // 检查单选按钮
            const checkedRadios = row.querySelectorAll('input[type="radio"]:checked');
            if (checkedRadios.length > 0) {
                // 如果有选中的单选按钮，且值不是"/"
                if (checkedRadios[0].value && checkedRadios[0].value !== '/') {
                    console.log(`行 ${rowId} 中有选中的单选按钮: ${checkedRadios[0].value}`);
                    hasValue = true;
                }
            }
            
            // 返回是否为空
            return !hasValue;
        }

        // 辅助函数：记录行内数据
        function logRowData(row) {
            if (!row || !row.id) return;
            
            const inputs = row.querySelectorAll('input[type="text"]');
            inputs.forEach(input => {
                if (input && input.name && input.value && input.value.trim() !== '') {
                    console.log(`- ${input.name}: "${input.value}"`);
                }
            });
        }

        // 自动判断尺寸合格性的函数
        function checkSizeCompliance(rowNumber) {
            console.log(`检查第${rowNumber}行尺寸的合格性`);
            
            // 获取标准值、最小值和最大值
            const standardValue = document.querySelector(`input[name="size_${rowNumber}_value"]`);
            const minValue = document.querySelector(`input[name="size_${rowNumber}_min"]`);
            const maxValue = document.querySelector(`input[name="size_${rowNumber}_max"]`);
            
            // 获取实测值
            const measure1 = document.querySelector(`input[name="size_${rowNumber}_measure_1"]`);
            const measure2 = document.querySelector(`input[name="size_${rowNumber}_measure_2"]`);
            const measure3 = document.querySelector(`input[name="size_${rowNumber}_measure_3"]`);
            const measure4 = document.querySelector(`input[name="size_${rowNumber}_measure_4"]`);
            const measure5 = document.querySelector(`input[name="size_${rowNumber}_measure_5"]`);
            
            // 获取单选按钮
            const radioPass = document.querySelector(`input[name="size_${rowNumber}_check"][value="合格"]`);
            const radioFail = document.querySelector(`input[name="size_${rowNumber}_check"][value="不合格"]`);
            const radioAOD = document.querySelector(`input[name="size_${rowNumber}_check"][value="AOD"]`);
            
            // 如果没有找到必要的元素，直接返回
            if (!standardValue || !minValue || !maxValue || !radioPass || !radioFail || !radioAOD) {
                console.warn(`第${rowNumber}行缺少必要的输入元素，无法判断合格性`);
                return;
            }
            
            // 检查当前是否已经有选中的单选按钮（数据库加载的值）
            const hasSelectedRadio = radioPass.checked || radioFail.checked || radioAOD.checked;
            
            // 检查是否有足够的数据来判断
            let hasStandardData = false;
            let hasMinMaxData = false;
            let hasMeasureData = false;
            
            // 判断是否有标准值
            if (standardValue.value && standardValue.value.trim() !== '' && !isNaN(parseFloat(standardValue.value))) {
                hasStandardData = true;
            }
            
            // 判断是否有最小最大值
            if (minValue.value && minValue.value.trim() !== '' && !isNaN(parseFloat(minValue.value)) &&
                maxValue.value && maxValue.value.trim() !== '' && !isNaN(parseFloat(maxValue.value))) {
                hasMinMaxData = true;
            }
            
            // 判断是否有测量值
            const measures = [];
            // 只添加存在的测量值，确保变量已定义
            if (typeof measure1 !== 'undefined' && measure1) measures.push(measure1);
            if (typeof measure2 !== 'undefined' && measure2) measures.push(measure2);
            if (typeof measure3 !== 'undefined' && measure3) measures.push(measure3);
            if (typeof measure4 !== 'undefined' && measure4) measures.push(measure4);
            if (typeof measure5 !== 'undefined' && measure5) measures.push(measure5);
            
            const validMeasures = measures.filter(m => 
                m && m.value && m.value.trim() !== '' && !isNaN(parseFloat(m.value))
            );
            hasMeasureData = validMeasures.length > 0;
            
            // 如果缺少必要数据，则不做判断
            if (!hasMeasureData || (!hasStandardData && !hasMinMaxData)) {
                console.log(`第${rowNumber}行缺少判断所需的数据`);
                return;
            }
            
            // 判断是否合格
            let isCompliant = true;
            let detailedReason = '';
            
            // 对每个有效的测量值进行判断
            validMeasures.forEach((measure, index) => {
                const measureValue = parseFloat(measure.value);
                
                // 如果有最小最大值，优先使用最小最大值判断
                if (hasMinMaxData) {
                    const min = parseFloat(minValue.value);
                    const max = parseFloat(maxValue.value);
                    
                    if (measureValue < min || measureValue > max) {
                        isCompliant = false;
                        detailedReason += `测量值${index+1}(${measureValue})超出范围[${min}, ${max}]; `;
                    }
                }
                // 如果只有标准值，则判断是否等于标准值
                else if (hasStandardData) {
                    const standard = parseFloat(standardValue.value);
                    
                    // 允许0.01的误差
                    const tolerance = 0.01;
                    if (Math.abs(measureValue - standard) > tolerance) {
                        isCompliant = false;
                        detailedReason += `测量值${index+1}(${measureValue})不等于标准值(${standard}); `;
                    }
                }
            });
            
            // 只有在没有已选择的单选按钮时，才根据判断结果选择对应的单选框
            // 或者当前为用户手动触发的判断（由userTriggered标志指示）
            if (!hasSelectedRadio || rowNumber.toString() in userModifiedRows) {
                if (hasMeasureData && (hasStandardData || hasMinMaxData)) {
                    if (isCompliant) {
                        radioPass.checked = true;
                        console.log(`第${rowNumber}行判定为合格`);
                    } else {
                        radioFail.checked = true;
                        console.log(`第${rowNumber}行判定为不合格: ${detailedReason}`);
                    }
                    
                    // 添加提示，表明这是系统自动判断的结果
                    showFeedbackMessage(`第${rowNumber}行已自动判断为${isCompliant ? '合格' : '不合格'}，您可以手动修改`, 'info');
                }
            } else {
                console.log(`第${rowNumber}行已有选中的单选按钮，保留原选择`);
            }
        }
        
        // 全局变量，记录用户修改过的行
        const userModifiedRows = {};
        
        // 为尺寸行的输入字段添加事件监听器
        function addSizeRowListeners(rowNumber) {
            const inputs = [
                document.querySelector(`input[name="size_${rowNumber}_value"]`),
                document.querySelector(`input[name="size_${rowNumber}_min"]`),
                document.querySelector(`input[name="size_${rowNumber}_max"]`),
                document.querySelector(`input[name="size_${rowNumber}_measure_1"]`),
                document.querySelector(`input[name="size_${rowNumber}_measure_2"]`),
                document.querySelector(`input[name="size_${rowNumber}_measure_3"]`),
                document.querySelector(`input[name="size_${rowNumber}_measure_4"]`),
                document.querySelector(`input[name="size_${rowNumber}_measure_5"]`)
            ];
            
            // 为每个输入字段添加change和input事件
            inputs.forEach(input => {
                if (input) {
                    // 移除现有的事件监听器（如果有）
                    const oldChangeHandler = input.onchange;
                    const oldInputHandler = input.oninput;
                    
                    input.onchange = null;
                    input.oninput = null;
                    
                    // 添加新的事件处理程序
                    input.addEventListener('change', function() {
                        // 标记该行为用户修改过
                        userModifiedRows[rowNumber] = true;
                        // 触发自动判断
                        checkSizeCompliance(rowNumber);
                        // 调用原有的事件处理程序（如果有）
                        if (oldChangeHandler) oldChangeHandler.call(this);
                    });
                    
                    input.addEventListener('input', function() {
                        // 使用延迟以避免频繁触发
                        clearTimeout(input.timer);
                        input.timer = setTimeout(() => {
                            // 标记该行为用户修改过
                            userModifiedRows[rowNumber] = true;
                            // 触发自动判断
                            checkSizeCompliance(rowNumber);
                        }, 500);
                        // 调用原有的事件处理程序（如果有）
                        if (oldInputHandler) oldInputHandler.call(this);
                    });
                }
            });
            
            // 为单选按钮添加事件监听器，使用户可以手动修改自动判断结果
            const radioButtons = document.querySelectorAll(`input[name="size_${rowNumber}_check"]`);
            radioButtons.forEach(radio => {
                radio.addEventListener('change', function() {
                    // 当用户手动修改选择时，取消该行的自动判断标记
                    delete userModifiedRows[rowNumber];
                    console.log(`用户手动选择了第${rowNumber}行的${this.value}选项`);
                });
            });
            
            console.log(`已为第${rowNumber}行添加自动判断事件监听器`);
        }

        // 为所有现有的尺寸行添加监听器
        function initializeSizeRowListeners() {
            // 获取所有尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            
            allSizeRows.forEach(row => {
                if (row.id && row.id.startsWith('size-row-')) {
                    const rowNumber = parseInt(row.id.replace('size-row-', ''));
                    addSizeRowListeners(rowNumber);
                    
                    // 检查是否有已选中的单选按钮（从数据库加载的选择）
                    const checkedRadio = row.querySelector('input[type="radio"]:checked');
                    if (!checkedRadio) {
                        // 如果没有已选择的单选按钮，说明是新行或没有从数据库加载选择
                        // 此时可以进行初始自动判断
                        console.log(`第${rowNumber}行无已选择的单选按钮，执行初始自动判断`);
                        userModifiedRows[rowNumber] = true; // 标记为需要自动判断
                        checkSizeCompliance(rowNumber);
                    } else {
                        console.log(`第${rowNumber}行已有选中的单选按钮: ${checkedRadio.value}，保留原选择`);
                    }
                }
            });
            
            console.log('已初始化所有尺寸行的自动判断功能');
        }

        // 添加数据库加载后的强制显示处理
        function enforceSizeRowsDisplay() {
            console.log('数据库加载后处理尺寸行显示，使用新的通用逻辑...');
            
            // 从头初始化所有行，确保显示正确
            initializeSizeRows();
        }

        // 添加尺寸行
        function addSizeRow() {
            console.log('添加新的尺寸行...');
            
            // 获取所有尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            const visibleRows = Array.from(allSizeRows).filter(row => !row.classList.contains('confirmed-empty-row'));
            
            console.log(`当前有 ${allSizeRows.length} 个尺寸行，其中 ${visibleRows.length} 个可见`);
            
            // 检查是否达到最大行数（例如30行）
            if (allSizeRows.length >= 30) {
                showFeedbackMessage('已达到最大尺寸行数，不能继续添加', 'warning');
                return;
            }
            
            // 查找被标记为空的行
            const hiddenRows = Array.from(allSizeRows).filter(row => row.classList.contains('confirmed-empty-row'));
            if (hiddenRows.length > 0) {
                // 如果有隐藏的行，先重用它们
                const rowToReuse = hiddenRows[0];
                rowToReuse.classList.remove('confirmed-empty-row');
                
                // 获取行号
                const rowNumber = parseInt(rowToReuse.id.replace('size-row-', ''));
                console.log(`重用隐藏的行 #${rowNumber}`);
                
                // 显示消息
                showFeedbackMessage(`显示尺寸行 #${rowNumber}`);
                
                // 确保行可见并滚动到视图
                rowToReuse.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // 更新rowspan
                updateSizeSectionRowspan();
                
                // 添加自动判断监听器
                addSizeRowListeners(rowNumber);
                
                return;
            }
            
            // 获取当前最大行号
            let maxRowNumber = 0;
            allSizeRows.forEach(row => {
                if (row.id && row.id.startsWith('size-row-')) {
                    const rowNum = parseInt(row.id.replace('size-row-', ''));
                    if (!isNaN(rowNum) && rowNum > maxRowNumber) {
                        maxRowNumber = rowNum;
                    }
                }
            });
            
            // 新行号是当前最大行号+1
            const newRowNumber = maxRowNumber + 1;
            console.log(`创建新行 #${newRowNumber}`);
            
            // 创建新行
            const newRow = document.createElement('tr');
            newRow.className = 'custom-row-height dynamic-size-row';
            newRow.id = `size-row-${newRowNumber}`;
            
            // 行内容
            newRow.innerHTML = `
                <td class="align-center">${newRowNumber}</td>
                <td data-id="size-${newRowNumber}" data-image="true" class="align-center">
                    <input type="text" name="size_${newRowNumber}_position" id="size-${newRowNumber}" value="">
                </td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_value" id="size-${newRowNumber}-value" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_min" id="size-${newRowNumber}-min" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_max" id="size-${newRowNumber}-max" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_1" id="size-${newRowNumber}-measure-1" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_2" id="size-${newRowNumber}-measure-2" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_3" id="size-${newRowNumber}-measure-3" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_4" id="size-${newRowNumber}-measure-4" value=""></td>
                <td class="align-center"><input type="text" name="size_${newRowNumber}_measure_5" id="size-${newRowNumber}-measure-5" value=""></td>
                <td>
                    <label><input type="radio" name="size_${newRowNumber}_check" value="合格"> 合格</label>
                    <label><input type="radio" name="size_${newRowNumber}_check" value="不合格"> 不合格</label>
                    <label><input type="radio" name="size_${newRowNumber}_check" value="AOD"> AOD</label>
                </td>
                <td colspan="2"><textarea name="size_${newRowNumber}_note"></textarea></td>
            `;
            
            // 插入新行
            const buttonsRow = document.getElementById('size-buttons-row');
            if (buttonsRow) {
                buttonsRow.parentNode.insertBefore(newRow, buttonsRow);
            } else {
                // 如果找不到按钮行，尝试插入到最后一个现有行后面
                const lastRow = allSizeRows[allSizeRows.length - 1];
                if (lastRow && lastRow.parentNode) {
                    lastRow.parentNode.insertBefore(newRow, lastRow.nextSibling);
                }
            }
            
            // 更新rowspan
            updateSizeSectionRowspan();
            
            // 添加自动判断监听器
            addSizeRowListeners(newRowNumber);
            
            // 显示消息并滚动到新行
            showFeedbackMessage(`已添加尺寸行 #${newRowNumber}`);
            newRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // 删除尺寸行
        function deleteSizeRow() {
            // 获取所有非空的尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            const visibleRows = Array.from(allSizeRows).filter(row => !row.classList.contains('empty-size-row'));
            
            // 至少保留3行可见
            if (visibleRows.length <= 3) {
                showFeedbackMessage('至少需要保留3个尺寸行', 'warning');
                return;
            }
            
            // 获取最后一个可见行
            const lastVisibleRow = visibleRows[visibleRows.length - 1];
            if (!lastVisibleRow) {
                showFeedbackMessage('找不到可删除的行', 'warning');
                return;
            }
            
            // 获取行号
            const rowId = lastVisibleRow.id;
            const rowNumber = rowId ? parseInt(rowId.replace('size-row-', '')) : 0;
            
            // 确认删除
            if (!checkRowIsReallyEmpty(lastVisibleRow)) {
                if (!confirm(`尺寸行 #${rowNumber} 有数据，确定要删除吗？`)) {
                    return;
                }
            }
            
            // 清空行数据
            const inputs = lastVisibleRow.querySelectorAll('input[type="text"], textarea');
            inputs.forEach(input => {
                input.value = '';
            });
            
            // 清空单选按钮
            const radios = lastVisibleRow.querySelectorAll('input[type="radio"]');
            radios.forEach(radio => {
                radio.checked = false;
            });
            
            // 标记为空行
            lastVisibleRow.classList.add('confirmed-empty-row');
            
            // 更新rowspan
            updateSizeSectionRowspan();
            
            // 显示消息
            showFeedbackMessage(`已删除尺寸行 #${rowNumber}`);
        }

        // 更新尺寸部分的rowspan
        function updateSizeSectionRowspan() {
            const sizeSectionCell = document.getElementById('size-section-cell');
            if (!sizeSectionCell) return;
            
            // 获取所有可见的尺寸行数量
            const visibleRows = Array.from(document.querySelectorAll('tr[id^="size-row-"]'))
                .filter(row => !row.classList.contains('empty-size-row'));
            
            // 设置rowspan，加2是因为要包含标题行和子标题行
            sizeSectionCell.rowSpan = visibleRows.length + 2;
            console.log(`更新尺寸部分rowspan为 ${visibleRows.length + 2}`);
        }

        // 表单提交前处理
        function prepareFormSubmit() {
            console.log('准备提交表单，确保所有行数据正确处理...');
            
            // 确保所有行都可见以便正确提交
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            console.log(`表单提交前处理 ${allSizeRows.length} 个尺寸行`);
            
            // 临时移除所有行的隐藏类，确保数据能被提交
            allSizeRows.forEach(row => {
                // 记录原始状态
                const wasHidden = row.classList.contains('confirmed-empty-row');
                
                if (wasHidden) {
                    console.log(`临时显示行 ${row.id} 以确保数据提交`);
                    row.classList.remove('confirmed-empty-row');
                    
                    // 添加一个标记，表示这是临时显示的行
                    row.dataset.tempVisible = 'true';
                }
            });
            
            // 保存问题点映射关系，确保图片和文本正确对应
            console.log('保存问题点映射关系...');
            
            // 获取所有问题点数据和映射关系
            const { questionData, questionMapping, detailedMapping } = getQuestionPointsData();
            
            // 创建一个隐藏字段，用于提交问题点映射关系
            let mappingInput = document.querySelector('input[name="question_mapping"]');
            if (!mappingInput) {
                mappingInput = document.createElement('input');
                mappingInput.type = 'hidden';
                mappingInput.name = 'question_mapping';
                document.getElementById('main-form').appendChild(mappingInput);
            }
            
            // 将映射关系转换为JSON字符串
            mappingInput.value = JSON.stringify(questionMapping);
            
            // 添加一个更详细的映射关系字段
            let detailedMappingInput = document.querySelector('input[name="detailed_question_mapping"]');
            if (!detailedMappingInput) {
                detailedMappingInput = document.createElement('input');
                detailedMappingInput.type = 'hidden';
                detailedMappingInput.name = 'detailed_question_mapping';
                document.getElementById('main-form').appendChild(detailedMappingInput);
            }
            
            // 将详细映射关系转换为JSON字符串
            detailedMappingInput.value = JSON.stringify(detailedMapping);
            
            // 确保所有问题点的图片路径和文本正确对应
            console.log('确保问题点图片和文本正确对应...');
            
                            // 遍历所有问题点数据
                questionData.forEach((data, index) => {
                    if (!data) return;
                    
                    const questionNumber = index + 1;
                    const originalNumber = data.originalNumber;
                    
                    // 更新隐藏字段的值
                    const imagePathInput = document.querySelector(`input[name="question_${questionNumber}_image"]`);
                    if (imagePathInput && data.imageSrc) {
                        imagePathInput.value = data.imageSrc;
                    }
                    
                    // 更新原始编号隐藏字段
                    const originalNumberInput = document.querySelector(`input[name="question_${questionNumber}_original_number"]`);
                    if (!originalNumberInput && originalNumber) {
                        const newInput = document.createElement('input');
                        newInput.type = 'hidden';
                        newInput.name = `question_${questionNumber}_original_number`;
                        newInput.value = originalNumber;
                        document.getElementById('material-form').appendChild(newInput);
                        console.log(`为问题点 ${questionNumber} 创建了原始编号隐藏字段，值为 ${originalNumber}`);
                    } else if (originalNumberInput) {
                        originalNumberInput.value = originalNumber;
                        console.log(`更新问题点 ${questionNumber} 的原始编号隐藏字段，值为 ${originalNumber}`);
                    }
                    
                    // 确保文本区域名称与原始编号对应
                    const textarea = document.querySelector(`textarea[name="question_${questionNumber}_text"]`);
                    if (textarea && data.text) {
                        // 保存文本内容，确保不会丢失
                        const textContent = textarea.value;
                        console.log(`确保问题点 ${questionNumber} (原始编号: ${originalNumber}) 的文本内容保持不变: "${textContent}"`);
                    }
                });
            
            // 将映射关系也保存到服务器临时存储
            storeQuestionMapping(false); // 使用false参数，不触发重命名
            
            return true;
        }
        
        // 在表单提交前调用准备函数
        document.getElementById('main-form').addEventListener('submit', function(event) {
            // 先执行表单准备
            prepareFormSubmit();
        });

        // 保存问题点映射关系的函数
        function storeQuestionMapping(confirmFlag = false) {
            console.log('保存问题点映射关系... confirmFlag =', confirmFlag);
            
            // 获取报告编码
            const reportCode = document.getElementById('report-code').textContent;
            if (!reportCode) {
                console.error('无法获取报告编码，无法保存映射关系');
                return;
            }
            
            // 首先尝试从后端获取报告对应的重排序设置
            fetch(`/Material_Sample_Confirmation_Form_modify/get_question_mapping/${reportCode}`)
            .then(response => response.json())
            .then(data => {
                let enableReordering = 'no';
                
                if (data.success) {
                    // 使用后端存储的重排序设置
                    enableReordering = data.enable_reordering || 'no';
                    console.log(`从后端获取到报告 ${reportCode} 的重排序设置: ${enableReordering}`);
                } else {
                    // 如果后端没有存储，使用默认方法获取
                    enableReordering = getImageReorderingSetting();
                    console.log('使用默认方法获取图片重排序设置:', enableReordering);
                }
                
                // 显示当前的重排序状态
                showImageReorderingStatus(enableReordering);
                
                // 获取所有问题点数据和映射关系
                const { questionData, questionMapping, detailedMapping } = getQuestionPointsData();
                
                // 如果没有问题点数据，直接返回
                if (!questionData || questionData.length === 0) {
                    console.log('没有问题点数据，无需保存映射关系');
                    return;
                }
                
                // 检查图片文件名与问题点编号是否一致
                let fileNameMismatch = [];
                
                if (enableReordering.toLowerCase() === 'yes') {
                    console.log('重排序模式：检查图片文件名是否需要更新');
                    
                    // 遍历所有问题点数据，检查图片文件名是否与问题点编号一致
                    questionData.forEach((data, index) => {
                        if (data && data.hasImage && data.imageName) {
                            // 计算新的问题点编号（从1开始）
                            const newNumber = index + 1;
                            
                            // 获取原始问题点编号
                            const originalNumber = data.originalNumber;
                            
                            // 从图片文件名中提取问题点编号
                            let fileNumber = '';
                            if (data.imageName) {
                                const nameParts = data.imageName.split('_');
                                if (nameParts.length > 0) {
                                    fileNumber = nameParts[0];
                                }
                            }
                            
                            // 如果文件名中的编号与新编号不一致，记录需要更新
                            if (fileNumber && fileNumber !== String(newNumber)) {
                                console.log(`发现图片文件名不匹配: 原始编号=${originalNumber}, 新编号=${newNumber}, 文件编号=${fileNumber}, 文件名=${data.imageName}`);
                                
                            fileNameMismatch.push({
                                    originalNumber: originalNumber,
                                newNumber: newNumber,
                                    fileNumber: fileNumber,
                                    imageName: data.imageName
                            });
                        }
                    }
                    });
                    
                    console.log(`检测到 ${fileNameMismatch.length} 个图片文件名需要更新`);
                }
                
                // 发送数据到服务器
                fetch('/Material_Sample_Confirmation_Form_modify/store_question_mapping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_code: reportCode,
                        question_data: questionData,
                        question_mapping: questionMapping,
                        detailed_mapping: detailedMapping,
                        file_name_mismatch: fileNameMismatch,  // 添加文件名不匹配信息
                        enable_reordering: enableReordering  // 传递重排序设置
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('问题点映射关系保存成功:', data.message);
                    } else {
                        console.error('问题点映射关系保存失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('保存问题点映射关系时发生错误:', error);
                });
            })
            .catch(error => {
                console.error('获取报告重排序设置出错:', error);
                // 使用默认方法获取并继续处理
                const enableReordering = getImageReorderingSetting();
                console.log('使用默认方法获取图片重排序设置:', enableReordering);
                
                // 这里重复上面的处理逻辑...
                // 获取所有问题点数据和映射关系
                const { questionData, questionMapping, detailedMapping } = getQuestionPointsData();
                
                // 如果没有问题点数据，直接返回
                if (!questionData || questionData.length === 0) {
                    console.log('没有问题点数据，无需保存映射关系');
                    return;
                }
                
                // 发送数据到服务器
                fetch('/Material_Sample_Confirmation_Form_modify/store_question_mapping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_code: reportCode,
                        question_data: questionData,
                        question_mapping: questionMapping,
                        detailed_mapping: detailedMapping,
                        file_name_mismatch: [],
                        enable_reordering: enableReordering  // 传递重排序设置
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('问题点映射关系保存成功:', data.message);
                    } else {
                        console.error('问题点映射关系保存失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('保存问题点映射关系时发生错误:', error);
                });
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 初始化所有现有图片的放大功能
            document.querySelectorAll('.preview-image').forEach(img => {
                addZoomFunctionality(img);
                console.log('已为图片添加放大功能:', img.src);
            });
            
            // 移除强制显示样式 - 但保留前3行的强制显示
            document.querySelectorAll('tr[id^="size-row-"]').forEach(row => {
                // 获取行号
                const rowNumber = parseInt(row.id.replace('size-row-', ''));
                
                // 只移除非前3行的内联样式
                if (rowNumber > 3) {
                    row.removeAttribute('style');
                }
            });
            
            // 移除CSS样式规则 - 但保留前3行的强制显示
            const styleSheets = document.styleSheets;
            for (let i = 0; i < styleSheets.length; i++) {
                try {
                    const cssRules = styleSheets[i].cssRules || styleSheets[i].rules;
                    for (let j = 0; j < cssRules.length; j++) {
                        const rule = cssRules[j];
                        // 只移除包含第4行的规则
                        if (rule.selectorText && rule.selectorText === '#size-row-1, #size-row-2, #size-row-3, #size-row-4') {
                            styleSheets[i].deleteRule(j);
                            console.log('已删除包含第4行的CSS规则');
                            break;
                        }
                    }
                } catch (e) {
                    console.warn('无法访问样式表规则:', e);
                }
            }
            
            // 初始化尺寸行显示
            initializeSizeRows();
            
            // 初始化尺寸行合格性判断功能
            initializeSizeRowListeners();
            
            // 重新排序尺寸行数据，确保第4-30行中有数据的行连续排列
            reorderSizeRows();
            
            // 绑定添加和删除尺寸行的事件
            document.getElementById('add-size-row').addEventListener('click', addSizeRow);
            document.getElementById('delete-size-row').addEventListener('click', deleteSizeRow);
            
            // 检查表单提交
            debugFormSubmission();
            
            // 绑定图片上传相关事件
            setupImageUpload();
            
            // 在表单提交前处理
            document.querySelector('form').addEventListener('submit', function(e) {
                // 不阻止默认提交
                return prepareFormSubmit();
            });
            
            // 确保第4行及以后的空行正确隐藏
            setTimeout(function() {
                console.log('延迟执行尺寸行显示逻辑，确保所有行正确初始化');
                
                // 获取所有尺寸行
                const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
                
                // 检查第4行及以后的行
                allSizeRows.forEach(row => {
                    if (row.id && row.id.startsWith('size-row-')) {
                        const rowNumber = parseInt(row.id.replace('size-row-', ''));
                        
                        // 只处理第4行及以后的行
                        if (rowNumber >= 4) {
                            const isEmpty = checkRowIsReallyEmpty(row);
                            if (isEmpty) {
                                console.log(`行 #${rowNumber} 确认为空，添加隐藏类`);
                                row.classList.add('confirmed-empty-row');
                            } else {
                                console.log(`行 #${rowNumber} 有数据，确保显示`);
                                row.classList.remove('confirmed-empty-row');
                            }
                        }
                    }
                });
                
                // 更新尺寸部分的rowspan
                updateSizeSectionRowspan();
            }, 500);
        });

        // 重新排序尺寸行数据
        function reorderSizeRows() {
            console.log('开始重新排序尺寸行数据...');
            
            // 获取所有尺寸行
            const allSizeRows = document.querySelectorAll('tr[id^="size-row-"]');
            
            // 获取所有行的数据状态
            const rowDataStatus = {};
            allSizeRows.forEach(row => {
                const rowNumber = parseInt(row.id.replace('size-row-', ''));
                rowDataStatus[rowNumber] = !checkRowIsReallyEmpty(row);
            });
            
            console.log('行数据状态:', rowDataStatus);
            
            // 第1行始终保持原位置
            // 第2-3行如果为空，且后面有数据的行，则将后面的数据前移
            let targetRowNumber = 2; // 从第2行开始检查
            
            // 创建映射表，记录原始行号和新行号的对应关系
            const rowMapping = {};
            
            // 先处理第2-3行
            for (let i = 2; i <= 3; i++) {
                if (!rowDataStatus[i]) {
                    // 当前行为空，查找后续有数据的行
                    let foundDataRow = false;
                    for (let j = i + 1; j <= 30; j++) {
                        if (rowDataStatus[j] && !rowMapping[j]) {
                            // 找到有数据的行，映射到当前空行
                            rowMapping[j] = i;
                            foundDataRow = true;
                            console.log(`映射: 原始行 ${j} -> 空行 ${i}`);
                            break;
                        }
                    }
                    if (!foundDataRow) {
                        // 没有找到有数据的行，保持当前行为空
                        console.log(`第${i}行为空，且后续没有有数据的行可移动`);
                    }
                } else {
                    // 当前行有数据，保持不变
                    console.log(`第${i}行有数据，保持不变`);
                }
            }
            
            // 处理第4行及以后的行
            let nextAvailableRow = 4;
            for (let i = 4; i <= 30; i++) {
                if (rowDataStatus[i] && !rowMapping[i]) {
                    // 有数据且未被映射过的行
                    while (rowMapping[nextAvailableRow] || nextAvailableRow in rowMapping) {
                        nextAvailableRow++;
                    }
                    if (i !== nextAvailableRow) {
                        rowMapping[i] = nextAvailableRow;
                        console.log(`映射: 原始行 ${i} -> 新行 ${nextAvailableRow}`);
                    }
                    nextAvailableRow++;
                }
            }
            
            // 检查是否需要重排序
            let needsReordering = false;
            for (const originalNumber in rowMapping) {
                if (parseInt(originalNumber) !== rowMapping[originalNumber]) {
                    needsReordering = true;
                    console.log(`需要重排序: 行 ${originalNumber} 将移动到 ${rowMapping[originalNumber]}`);
                    break;
                }
            }
            
            if (!needsReordering) {
                console.log('所有行已经按顺序排列，无需重排序');
                return;
            }
            
            // 首先清空所有目标行
            for (const originalNumber in rowMapping) {
                const newNumber = rowMapping[originalNumber];
                const targetRow = document.getElementById(`size-row-${newNumber}`);
                if (targetRow) {
                    // 清空所有输入字段
                    const inputs = targetRow.querySelectorAll('input[type="text"]');
                    inputs.forEach(input => {
                        input.value = '';
                    });
                    
                    // 清空单选按钮
                    const radios = targetRow.querySelectorAll('input[type="radio"]');
                    radios.forEach(radio => {
                        radio.checked = false;
                    });
                    
                    // 清空文本区域
                    const textareas = targetRow.querySelectorAll('textarea');
                    textareas.forEach(textarea => {
                        textarea.value = '';
                    });
                }
            }
            
            // 然后按照映射移动数据
            for (const originalNumber in rowMapping) {
                const newNumber = rowMapping[originalNumber];
                if (parseInt(originalNumber) === newNumber) {
                    // 如果新旧行号相同，无需移动
                    continue;
                }
                
                // 获取原始行和目标行
                const originalRow = document.getElementById(`size-row-${originalNumber}`);
                const targetRow = document.getElementById(`size-row-${newNumber}`);
                
                if (!originalRow || !targetRow) {
                    console.warn(`无法找到行 ${originalNumber} 或 ${newNumber}`);
                    continue;
                }
                
                console.log(`移动数据: 从行 ${originalNumber} 到行 ${newNumber}`);
                
                // 移动所有输入字段的值
                const sourceInputs = originalRow.querySelectorAll('input[type="text"]');
                const targetInputs = targetRow.querySelectorAll('input[type="text"]');
                
                for (let i = 0; i < Math.min(sourceInputs.length, targetInputs.length); i++) {
                    targetInputs[i].value = sourceInputs[i].value;
                    sourceInputs[i].value = '';
                }
                
                // 移动单选按钮选择
                const sourceRadios = originalRow.querySelectorAll('input[type="radio"]:checked');
                if (sourceRadios.length > 0) {
                    const checkedValue = sourceRadios[0].value;
                    const targetRadio = targetRow.querySelector(`input[type="radio"][value="${checkedValue}"]`);
                    if (targetRadio) {
                        targetRadio.checked = true;
                    }
                    sourceRadios[0].checked = false;
                }
                
                // 移动文本区域内容
                const sourceTextareas = originalRow.querySelectorAll('textarea');
                const targetTextareas = targetRow.querySelectorAll('textarea');
                
                for (let i = 0; i < Math.min(sourceTextareas.length, targetTextareas.length); i++) {
                    targetTextareas[i].value = sourceTextareas[i].value;
                    sourceTextareas[i].value = '';
                }
                
                // 标记原始行为空行
                originalRow.classList.add('confirmed-empty-row');
                
                // 确保目标行可见
                targetRow.classList.remove('confirmed-empty-row');
            }
            
            
            // 更新尺寸部分的rowspan
            updateSizeSectionRowspan();
            
            console.log('尺寸行重排序完成');
            showFeedbackMessage('尺寸数据已重新排序', 'info');
        }

        // 获取问题点数据的函数
        function getQuestionPointsData() {
            console.log('获取所有问题点数据...');
            
            // 获取重排序设置
            const enableReordering = getImageReorderingSetting();
            console.log(`获取问题点数据时的重排序设置: ${enableReordering}`);
            
            // 获取所有问题点行
            const questionRows = document.querySelectorAll('tr.question-row');
            const textRows = document.querySelectorAll('tr.question-text-row');
            
            // 第一阶段：收集所有问题点数据到一个统一的数据结构
            // 创建一个对象来存储所有问题点数据，使用原始编号作为键
            const allQuestionData = {};
            
            console.log('第一阶段：收集所有问题点数据');
            
            // 处理每一行问题点
            for (let i = 0; i < questionRows.length; i++) {
                const questionRow = questionRows[i];
                const textRow = textRows[i];
                
                // 每行有3个问题点
                for (let j = 0; j < 3; j++) {
                    // 获取问题点单元格
                    const imageCell = questionRow.children[j];
                    const textCell = textRow.children[j];
                    
                    if (!imageCell || !textCell) continue;
                    
                    // 获取图片上传框
                    const uploadBox = imageCell.querySelector('.image-upload-box');
                    if (!uploadBox) continue;
                    
                    // 获取问题点编号
                    const questionNumber = uploadBox.getAttribute('data-question-number');
                    if (!questionNumber) continue;
                    
                    // 获取原始问题点编号
                    const originalNumber = uploadBox.getAttribute('data-original-number') || questionNumber;
                    
                    // 获取图片元素
                    const previewImage = uploadBox.querySelector('.preview-image');
                    
                    // 获取文本区域
                    const textarea = textCell.querySelector('textarea');
                    if (!textarea) continue;
                    
                    // 获取原始文本区域名称
                    const originalName = textarea.getAttribute('data-original-name');
                    
                    // 检查是否有内容（图片或文本）
                    const hasImage = previewImage && previewImage.src && previewImage.src.indexOf('data:image') !== -1;
                    const hasText = textarea.value && textarea.value.trim() !== '';
                    
                    // 计算当前位置（从1开始）
                    const currentPosition = i * 3 + j + 1;
                    
                    // 提取图片文件名（如果有）
                    let imageName = '';
                    let imageUrl = '';
                    if (hasImage) {
                        imageUrl = previewImage.src;
                        // 从URL中提取文件名
                        const urlParts = imageUrl.split('/');
                        imageName = urlParts[urlParts.length - 1];
                    }
                    
                    // 将数据收集到字典中，确保即使存在重复问题编号也能正确处理
                    if (!allQuestionData[originalNumber]) {
                        allQuestionData[originalNumber] = {
                            originalNumber: originalNumber,
                            text: textarea.value || '',
                            imageSrc: hasImage ? previewImage.src : null,
                            imageName: imageName,
                            imageUrl: imageUrl,
                            hasImage: hasImage,
                            hasText: hasText,
                            isEmpty: !(hasImage || hasText),
                            position: {row: i, col: j},
                            originalName: originalName || '',
                            currentPosition: currentPosition
                        };
                        console.log(`收集问题点数据：原始编号 ${originalNumber}, 位置: ${currentPosition}, 有图片: ${hasImage}, 有文本: ${hasText}`);
                    } else {
                        // 对于重复的问题编号，合并数据（保留图片和文本）
                        const existingData = allQuestionData[originalNumber];
                        if (hasImage && !existingData.hasImage) {
                            existingData.imageSrc = previewImage.src;
                            existingData.imageName = imageName;
                            existingData.imageUrl = imageUrl;
                            existingData.hasImage = true;
                            console.log(`为已存在的问题点 ${originalNumber} 添加图片数据`);
                        }
                        if (hasText && !existingData.text) {
                            existingData.text = textarea.value;
                            existingData.hasText = true;
                            console.log(`为已存在的问题点 ${originalNumber} 添加文本数据`);
                        }
                        existingData.isEmpty = !(existingData.hasImage || existingData.hasText);
                    }
                }
            }
            
            // 第二阶段：处理收集到的数据，创建结果数组和映射关系
            console.log('第二阶段：处理收集到的问题点数据');
            
            // 创建结果数组
            let result = [];
            
            // 创建映射关系对象，记录原始编号到新编号的映射
            const mapping = {};
            
            // 将收集到的数据转换为数组
            for (const [originalNumber, data] of Object.entries(allQuestionData)) {
                result.push(data);
            }
            
            console.log(`从字典收集到 ${result.length} 个问题点数据`);
            
            // 创建详细的映射关系
            const detailedMapping = {};
            
            // 如果禁用重排序，保持问题点在原始位置
            if (enableReordering.toLowerCase() === 'no') {
                console.log('重排序设置为"否"，保持问题点原始位置');
                
                // 使用currentPosition作为实际位置，并按currentPosition排序
                result.sort((a, b) => a.currentPosition - b.currentPosition);
                
                // 使用currentPosition作为实际位置
                result.forEach((item) => {
                    if (item && item.originalNumber) {
                        mapping[item.originalNumber] = item.currentPosition;
                        
                        detailedMapping[item.originalNumber] = {
                            newNumber: item.currentPosition,
                            hasImage: item.hasImage,
                            hasText: item.hasText,
                            isEmpty: item.isEmpty || false,  // 添加空状态标记
                            imageName: item.imageName,
                            originalName: item.originalName
                        };
                    }
                });
                
                // 不改变result的顺序，保持原始位置
            } else {
                console.log('重排序设置为"是"，使用连续编号');
                
                // 在启用重排序模式下，先把非空问题点排在前面
                result = result.filter(item => !item.isEmpty);
                
                // 使用连续编号并建立详细映射关系
                result.forEach((item, index) => {
                    if (item && item.originalNumber) {
                        const newNumber = index + 1;
                        // 记录原始编号到新编号的映射关系
                        mapping[item.originalNumber] = newNumber;
                        
                        detailedMapping[item.originalNumber] = {
                            newNumber: newNumber,
                            hasImage: item.hasImage,
                            hasText: item.hasText,
                            isEmpty: item.isEmpty || false,
                            imageName: item.imageName,
                            originalName: item.originalName,
                            // 添加图片重命名的标识信息
                            needRename: item.hasImage && item.imageName ? true : false
                        };
                        
                        // 更新当前项中的位置信息
                        item.newPosition = newNumber;
                    }
                });
                
                // 确保问题点数据中的排序编号是连续的
                for (let i = 0; i < result.length; i++) {
                    if (result[i]) {
                        result[i].continuousNumber = i + 1;
                    }
                }
                
                console.log('重排序后的映射关系:', mapping);
                console.log('重排序后的详细映射关系:', detailedMapping);
            }
            
            return { 
                questionData: result,
                questionMapping: mapping,
                detailedMapping: detailedMapping
            };
        }

        // 重新排序问题点的函数
        function reorderQuestionPoints() {
            console.log('开始重新排序问题点...');
            
            // 获取问题点数据
            const { questionData, questionMapping } = getQuestionPointsData();
            
            // 获取报告编码
            const reportCode = document.getElementById('report-code').textContent;
            if (!reportCode) {
                console.error('无法获取报告编码，无法获取重排序设置');
                // 使用默认方法获取重排序设置
                const enableReordering = getImageReorderingSetting();
                if (enableReordering.toLowerCase() === 'no') {
                    console.log('图片重排序设置为"否"，跳过问题点重排序');
                    
                    // 仍然存储问题点映射关系，但不进行重排序
                    storeQuestionMapping(false);
                    
                    return { questionData, questionMapping };
                }
            }
            
            // 从后端获取重排序设置
            return new Promise((resolve) => {
                fetch(`/Material_Sample_Confirmation_Form_modify/get_question_mapping/${reportCode}`)
                .then(response => response.json())
                .then(data => {
                    let enableReordering = 'no';
                    
                    if (data.success) {
                        // 使用后端存储的重排序设置
                        enableReordering = data.enable_reordering || 'no';
                        console.log(`从后端获取到报告 ${reportCode} 的重排序设置: ${enableReordering}`);
                    } else {
                        // 如果后端没有存储，使用默认方法获取
                        enableReordering = getImageReorderingSetting();
                        console.log('使用默认方法获取图片重排序设置:', enableReordering);
                    }
                    
                    // 显示当前的重排序状态
                    showImageReorderingStatus(enableReordering);
                    
                    if (enableReordering.toLowerCase() === 'no') {
                        console.log('图片重排序设置为"否"，跳过问题点重排序');
                        
                        // 仍然存储问题点映射关系，但不进行重排序
                        storeQuestionMapping(false);
                        
                        // 直接返回当前问题点数据，不进行重排序
                        resolve({ questionData, questionMapping });
                        return;
                    }
                    
                    // 以下为重排序逻辑，仅在启用重排序时执行
                    console.log('进行问题点重排序操作...');
                    
                    // 如果没有问题点数据，直接返回
                    if (!questionData || questionData.length === 0) {
                        console.log('没有问题点数据，无需重排序');
                        resolve({ questionData, questionMapping });
                        return;
                    }
                    
                    // 获取所有问题点行和文本行
                    const questionRows = document.querySelectorAll('tr.question-row');
                    const textRows = document.querySelectorAll('tr.question-text-row');
                    
                    // 备份所有问题点数据，以便在清空后恢复
                    const questionBackup = [];
                    
                    // 收集所有问题点的当前状态
                    for (let i = 0; i < questionData.length; i++) {
                        if (questionData[i]) {
                            questionBackup.push({...questionData[i]});
                        } else {
                            questionBackup.push(null);
                        }
                    }
                    
                    // 过滤出有内容的问题点（有文本或图片）
                    const validQuestions = questionBackup.filter(data => 
                        data && (data.text || data.imageSrc)
                    );
                    
                    // 清空所有问题点
                    for (let i = 0; i < questionData.length; i++) {
                        questionData[i] = null;
                    }
                    
                    // 按顺序重新填充问题点数据
                    for (let i = 0; i < validQuestions.length; i++) {
                        questionData[i] = validQuestions[i];
                    }
                    
                    // 更新问题点显示
                    updateQuestionPointsDisplay();
                    
                    // 保存重排序后的数据到临时存储
                    storeQuestionMapping(false); // 使用false参数，不触发重命名
                    
                    console.log('问题点重排序完成，映射关系:', questionMapping);
                    resolve({ questionData, questionMapping });
                })
                .catch(error => {
                    console.error('获取报告重排序设置出错:', error);
                    // 使用默认方法获取并继续处理
                    const enableReordering = getImageReorderingSetting();
                    console.log('使用默认方法获取图片重排序设置:', enableReordering);
                    
                    if (enableReordering.toLowerCase() === 'no') {
                        console.log('图片重排序设置为"否"，跳过问题点重排序');
                        
                        // 仍然存储问题点映射关系，但不进行重排序
                        storeQuestionMapping(false);
                        
                        // 直接返回当前问题点数据，不进行重排序
                        resolve({ questionData, questionMapping });
                        return;
                    }
                    
                    // 以下为重排序逻辑，仅在启用重排序时执行
                    console.log('进行问题点重排序操作...');
                    
                    // 如果没有问题点数据，直接返回
                    if (!questionData || questionData.length === 0) {
                        console.log('没有问题点数据，无需重排序');
                        resolve({ questionData, questionMapping });
                        return;
                    }
                    
                    // 获取所有问题点行和文本行
                    const questionRows = document.querySelectorAll('tr.question-row');
                    const textRows = document.querySelectorAll('tr.question-text-row');
                    
                    // 备份所有问题点数据，以便在清空后恢复
                    const questionBackup = [];
                    
                    // 收集所有问题点的当前状态
                    for (let i = 0; i < questionData.length; i++) {
                        if (questionData[i]) {
                            questionBackup.push({...questionData[i]});
                        } else {
                            questionBackup.push(null);
                        }
                    }
                    
                    // 过滤出有内容的问题点（有文本或图片）
                    const validQuestions = questionBackup.filter(data => 
                        data && (data.text || data.imageSrc)
                    );
                    
                    // 清空所有问题点
                    for (let i = 0; i < questionData.length; i++) {
                        questionData[i] = null;
                    }
                    
                    // 按顺序重新填充问题点数据
                    for (let i = 0; i < validQuestions.length; i++) {
                        questionData[i] = validQuestions[i];
                    }
                    
                    // 更新问题点显示
                    updateQuestionPointsDisplay();
                    
                    // 保存重排序后的数据到临时存储
                    storeQuestionMapping(false); // 使用false参数，不触发重命名
                    
                    console.log('问题点重排序完成，映射关系:', questionMapping);
                    resolve({ questionData, questionMapping });
                });
            });
        }

        // 放大图片
        function zoomIn() {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const scale = parseFloat(modalImg.dataset.scale) || 1;
            modalImg.style.transform = `translate(-50%, -50%) scale(${scale + 0.1})`;
            modalImg.dataset.scale = scale + 0.1;
        }

        // 缩小图片
        function zoomOut() {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            const scale = parseFloat(modalImg.dataset.scale) || 1;
            modalImg.style.transform = `translate(-50%, -50%) scale(${scale - 0.1})`;
            modalImg.dataset.scale = scale - 0.1;
        }

        // 重置图片大小
        function resetZoom() {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modalImg.style.transform = 'translate(-50%, -50%) scale(1)';
            modalImg.dataset.scale = 1;
        }

        // 在合适位置添加下面的获取设置的代码
        function getImageReorderingSetting() {
            // 默认不启用重排序
            let enableReordering = 'no';
            
            // 清除localStorage中的设置，强制从服务器重新获取
            localStorage.removeItem('enable_image_reordering');
            
            // 从服务器获取最新设置
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', '/settings/init_config', false); // 同步请求
                xhr.send();
                
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.status === 'success' && response.config) {
                        enableReordering = response.config.enable_image_reordering || 'no';
                        console.log('从服务器获取图片重排序设置:', enableReordering);
                    }
                }
            } catch (error) {
                console.error('获取图片重排序设置出错:', error);
            }
            
            // 显示当前的重排序状态
            showImageReorderingStatus(enableReordering);
            
            return enableReordering;
        }

        // 显示当前的重排序状态
        function showImageReorderingStatus(status) {
            const statusText = status.toLowerCase() === 'yes' ? '已启用' : '已禁用';
            const statusClass = status.toLowerCase() === 'yes' ? 'status-enabled' : 'status-disabled';
            
            // 检查是否已存在状态显示元素
            let statusElement = document.getElementById('image-reordering-status');
            if (!statusElement) {
                // 创建一个状态显示元素
                statusElement = document.createElement('div');
                statusElement.id = 'image-reordering-status';
                statusElement.style.position = 'fixed';
                statusElement.style.bottom = '10px';
                statusElement.style.right = '10px';
                statusElement.style.padding = '8px 12px';
                statusElement.style.borderRadius = '4px';
                statusElement.style.fontSize = '12px';
                statusElement.style.fontWeight = 'bold';
                statusElement.style.zIndex = '9999';
                document.body.appendChild(statusElement);
            }
            
            // 更新状态显示
            statusElement.textContent = `问题点图片重排序: ${statusText}`;
            statusElement.className = statusClass;
            
            // 根据状态设置不同的样式
            if (status.toLowerCase() === 'yes') {
                statusElement.style.backgroundColor = '#28a745';
                statusElement.style.color = 'white';
            } else {
                statusElement.style.backgroundColor = '#dc3545';
                statusElement.style.color = 'white';
            }
        }

        // 在页面加载时初始化图片重排序设置
        document.addEventListener('DOMContentLoaded', function() {
            // ... 现有代码 ...
            
            // 初始化图片重排序设置
            const reportCode = document.getElementById('report-code').textContent;
            
            // 首先尝试从后端获取报告对应的重排序设置
            if (reportCode) {
                fetch(`/Material_Sample_Confirmation_Form_modify/get_question_mapping/${reportCode}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 使用后端存储的重排序设置
                        const enableReordering = data.enable_reordering || 'no';
                        console.log(`从后端获取到报告 ${reportCode} 的重排序设置: ${enableReordering}`);
                        
                        // 显示当前的重排序状态
                        showImageReorderingStatus(enableReordering);
                        
                        // 如果在重排序模式下，给页面添加重排序模式的提示
                        if (enableReordering.toLowerCase() === 'yes') {
                            console.log('图片重排序模式已启用，问题点将连续编号');
                        } else {
                            console.log('图片重排序模式已禁用，问题点将保持原始编号');
                        }
                    } else {
                        // 如果后端没有存储，使用默认方法获取
                        const enableReordering = getImageReorderingSetting();
                        console.log('页面加载时获取到的图片重排序设置:', enableReordering);
                        
                        // 如果在重排序模式下，给页面添加重排序模式的提示
                        if (enableReordering.toLowerCase() === 'yes') {
                            console.log('图片重排序模式已启用，问题点将连续编号');
                        } else {
                            console.log('图片重排序模式已禁用，问题点将保持原始编号');
                        }
                    }
                })
                .catch(error => {
                    console.error('获取报告重排序设置出错:', error);
                    // 使用默认方法获取
                    const enableReordering = getImageReorderingSetting();
                    console.log('页面加载时获取到的图片重排序设置:', enableReordering);
                });
            } else {
                // 如果没有报告编码，使用默认方法获取
                const enableReordering = getImageReorderingSetting();
                console.log('页面加载时获取到的图片重排序设置:', enableReordering);
            }
            
            // ... 现有代码 ...
        });

        // 更新问题点显示的函数
        function updateQuestionPointsDisplay(inputQuestionData) {
            console.log('开始更新问题点显示...');
            
            // 获取重排序设置
            const enableReordering = getImageReorderingSetting();
            console.log(`更新问题点显示时的重排序设置: ${enableReordering}`);
            
            // 获取所有问题点行
            const questionRows = document.querySelectorAll('tr.question-row');
            const textRows = document.querySelectorAll('tr.question-text-row');
            
            // 使用传入的问题点数据或获取当前数据
            const questionData = inputQuestionData || [];
            
            // 如果没有问题点数据，直接返回
            if (!questionData || questionData.length === 0) {
                console.log('没有问题点数据，无需更新显示');
                return;
            }
            
            console.log('执行问题点显示更新...');
            
            // 计数器，用于跟踪处理的问题点数量
            let processedCount = 0;
            
            // 根据重排序设置决定如何更新问题点显示
            if (enableReordering.toLowerCase() === 'yes') {
                // 重排序模式：按顺序显示问题点
                console.log('重排序模式：按连续顺序排列问题点');
                
                // 遍历并更新问题点显示
                questionData.forEach((data, index) => {
                    if (!data) return;
                    
                    // 确定该问题点应该显示在哪一行
                    const rowIndex = Math.floor(index / 3);
                    const colIndex = index % 3;
                    
                    // 查找对应的行和单元格
                    if (rowIndex < questionRows.length && colIndex < 3) {
                        updateQuestionPointCell(rowIndex, colIndex, data, index + 1, questionRows, textRows);
                        processedCount++;
                    }
                });
            } else {
                // 不重排序模式：按原始位置显示问题点
                console.log('非重排序模式：按原始位置排列问题点');
                
                // 创建一个临时数组来存储所有问题点数据和其位置信息
                const positionedData = [];
                
                // 遍历问题点数据，确定每个问题点的显示位置
                questionData.forEach((data, index) => {
                    if (!data) return;
                    
                    // 确定该问题点应显示的位置
                    let position;
                    
                    // 优先使用currentPosition属性
                    if (data.currentPosition && Number(data.currentPosition) >= 1) {
                        position = Number(data.currentPosition);
                        console.log(`问题点使用currentPosition: ${position}`);
                    }
                    // 其次尝试使用originalNumber（如果是数字）
                    else if (data.originalNumber && !isNaN(Number(data.originalNumber))) {
                        position = Number(data.originalNumber);
                        console.log(`问题点使用originalNumber: ${position}`);
                    }
                    // 如果data.position包含row和col，计算位置
                    else if (data.position && typeof data.position === 'object' && 
                             'row' in data.position && 'col' in data.position) {
                        position = data.position.row * 3 + data.position.col + 1;
                        console.log(`问题点使用position对象计算位置: ${position}`);
                    }
                    // 最后默认使用当前索引+1
                    else {
                        position = index + 1;
                        console.log(`问题点使用默认索引: ${position}`);
                    }
                    
                    // 将问题点数据和位置信息存储到临时数组
                    positionedData.push({
                        data: data,
                        position: position,
                        originalIndex: index
                    });
                });
                
                // 先对positionedData按position排序，确保问题点按原始位置正确排列
                positionedData.sort((a, b) => a.position - b.position);
                
                console.log('排序后的问题点位置数据:', positionedData.map(item => ({
                    position: item.position,
                    originalNumber: item.data.originalNumber,
                    text: item.data.text ? item.data.text.substring(0, 20) + '...' : ''
                })));
                
                // 更新问题点显示
                positionedData.forEach(item => {
                    const position = item.position - 1; // 转换为0-based索引
                    const rowIndex = Math.floor(position / 3);
                    const colIndex = position % 3;
                    
                    // 查找对应的行和单元格
                    if (rowIndex < questionRows.length && colIndex < 3) {
                        // 在非重排序模式下，使用原始编号作为displayNumber，这样保持原始编号关联
                        const displayNumber = item.data.originalNumber || item.position;
                        
                        console.log(`放置问题点到位置 ${item.position}(${rowIndex},${colIndex})，显示编号: ${displayNumber}, 原始编号: ${item.data.originalNumber}, 文本: ${item.data.text ? item.data.text.substring(0, 20) + '...' : ''}`);
                        
                        updateQuestionPointCell(rowIndex, colIndex, item.data, displayNumber, questionRows, textRows);
                        processedCount++;
                    } else {
                        console.log(`警告：问题点位置(${item.position})超出可显示范围，行=${rowIndex}, 列=${colIndex}`);
                    }
                });
            }
            
            console.log(`问题点显示更新完成，共处理 ${processedCount} 个问题点`);
        }
        
        // 更新单个问题点单元格的辅助函数
        function updateQuestionPointCell(rowIndex, colIndex, data, displayNumber, questionRows, textRows) {
            // 获取图片和文本单元格
            const imageCell = questionRows[rowIndex].children[colIndex];
            const textCell = textRows[rowIndex].children[colIndex];
            
            if (!imageCell || !textCell) {
                console.log(`警告：找不到问题点单元格，行=${rowIndex}, 列=${colIndex}`);
                return;
            }
            
            // 获取上传框和文本区域
            const uploadBox = imageCell.querySelector('.image-upload-box');
            const textarea = textCell.querySelector('textarea');
            
            // 更新上传框数据
            if (uploadBox) {
                // 设置问题点编号
                uploadBox.setAttribute('data-question-number', displayNumber);
                
                // 保存原始编号
                if (data.originalNumber) {
                    uploadBox.setAttribute('data-original-number', data.originalNumber);
                }
                
                // 更新图片显示
                if (data.imageSrc) {
                    // 清除旧内容
                    uploadBox.innerHTML = '';
                    
                    // 创建图片元素
                    const img = document.createElement('img');
                    img.src = data.imageSrc;
                    img.className = 'preview-image';
                    img.onclick = function() {
                        showEnlargedImage(data.imageSrc);
                    };
                    uploadBox.appendChild(img);
                    
                    // 添加删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'delete-image-btn';
                    deleteBtn.innerHTML = '×';
                    deleteBtn.onclick = function(e) {
                        e.stopPropagation();
                        deleteImage(displayNumber);
                    };
                    uploadBox.appendChild(deleteBtn);
                    
                    // 标记为有图片
                    uploadBox.setAttribute('data-has-image', 'true');
                    
                    // 保存图片名称
                    if (data.imageName) {
                        uploadBox.setAttribute('data-image-name', data.imageName);
                    }
                } else {
                    // 没有图片，显示默认内容
                    uploadBox.innerHTML = '<div class="upload-overlay">无图片</div>';
                    uploadBox.setAttribute('data-has-image', 'false');
                }
                
                // 添加上传事件
                uploadBox.onclick = function() {
                    if (!this.querySelector('.preview-image')) {
                        openImageUploadDialog(displayNumber);
                    }
                };
            }
            
            // 更新文本区域
            if (textarea) {
                // 设置文本区域的name属性
                textarea.name = `question_${displayNumber}_text`;
                
                // 保存原始名称
                if (data.originalName) {
                    textarea.setAttribute('data-original-name', data.originalName);
                } else if (data.originalNumber) {
                    // 如果没有原始名称但有原始编号，也保存为data-original-name
                    textarea.setAttribute('data-original-name', `question_${data.originalNumber}_text`);
                }
                
                // 保存原始编号
                if (data.originalNumber) {
                    textarea.setAttribute('data-original-number', data.originalNumber);
                }
                
                // 更新文本内容
                if (data.text) {
                    // 获取重排序设置
                    const enableReordering = getImageReorderingSetting();
                    
                    // 修复：在重排序设置为"是"时，正确处理文本内容
                    if (enableReordering.toLowerCase() === 'yes') {
                        // 重排序模式：设置文本内容
                        textarea.value = data.text;
                        
                        // 添加隐藏字段保存原始文本，确保表单提交时使用正确的文本
                        const originalFieldName = data.originalNumber ? `question_${data.originalNumber}_original_text` : `question_${displayNumber}_original_text`;
                        let hiddenInput = document.querySelector(`input[name="${originalFieldName}"]`);
                        if (!hiddenInput) {
                            hiddenInput = document.createElement('input');
                            hiddenInput.type = 'hidden';
                            hiddenInput.name = originalFieldName;
                            textCell.appendChild(hiddenInput);
                        }
                        hiddenInput.value = data.text;
                        
                        console.log(`重排序模式 - 为问题点 ${displayNumber} 设置文本: "${data.text.substring(0, 30)}..."`);
                    } 
                    // 非重排序模式或问题点本身有文本
                    else if (data.hasText) {
                        textarea.value = data.text;
                        
                        // 添加隐藏字段保存原始文本
                        const originalFieldName = data.originalNumber ? `question_${data.originalNumber}_original_text` : `question_${displayNumber}_original_text`;
                        let hiddenInput = document.querySelector(`input[name="${originalFieldName}"]`);
                        if (!hiddenInput) {
                            hiddenInput = document.createElement('input');
                            hiddenInput.type = 'hidden';
                            hiddenInput.name = originalFieldName;
                            textCell.appendChild(hiddenInput);
                        }
                        hiddenInput.value = data.text;
                        
                        console.log(`非重排序模式 - 为问题点 ${displayNumber} 设置文本: "${data.text.substring(0, 30)}..."`);
                    }
                    else {
                        // 非重排序模式且问题点本身没有文本，清空文本区域
                        textarea.value = '';
                        console.log(`非重排序模式 - 问题点 ${displayNumber} 无文本，清空文本区域`);
                    }
                } else {
                    // 没有文本内容，清空文本区域
                    textarea.value = '';
                }
            }
        }
    </script>
</body>
</html>


