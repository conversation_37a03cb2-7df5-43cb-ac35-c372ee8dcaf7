<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检验记录录入</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script>
        function getMaterialInfo() {
            const materialCode = document.getElementById('material_code').value;
            const materialNameInput = document.getElementById('material_name');
            const normInput = document.getElementById('norm');
            const materialInput = document.getElementById('material');
            const colourInput = document.getElementById('colour');
            const versionInput = document.getElementById('version');
            const remarkInput = document.getElementById('remark');
            const supplierInput = document.getElementById('supplier');

            if (materialCode) {
                fetch(`/get_material_name?material_code=${encodeURIComponent(materialCode)}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        materialNameInput.value = data.material_name || '';
                        // 获取完整物料信息
                        fetch(`/get_material_info?material_code=${encodeURIComponent(materialCode)}`)
                            .then(response => response.json())
                            .then(materialInfo => {
                                normInput.value = materialInfo.norm || '';
                                materialInput.value = materialInfo.material || '';
                                colourInput.value = materialInfo.colour || '';
                                versionInput.value = materialInfo.version || '';
                                remarkInput.value = materialInfo.remark || '';
                            });
                        // 获取完整供应商列表
                        fetch(`/get_suppliers?material_code=${encodeURIComponent(materialCode)}`)
                            .then(response => response.json())
                            .then(suppliers => {
                                const supplierSelect = document.getElementById('supplier');
                                supplierSelect.innerHTML = ''; // 清空现有选项
                                suppliers.forEach(supplier => {
                                    const option = document.createElement('option');
                                    option.value = supplier;
                                    option.textContent = supplier;
                                    supplierSelect.appendChild(option);
                                });
                                supplierSelect.innerHTML += '<option value="_new_">+ 新增供应商</option>';
                                // 设置默认供应商
                                if (data.supplier) {
                                    supplierSelect.value = data.supplier;
                                } else {
                                    supplierSelect.value = '_new_';
                                }
                            });
                        supplierInput.value = data.supplier || '';

                        if (data.exists) {
                            fetchCTQData(materialCode);
                        } else {
                            if (confirm('没有此物料，是否进行新物料录入？')) {
                                window.location.href = "{{ url_for('new_material') }}";
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching material info:', error);
                        alert('获取物料信息失败，请稍后再试。');
                    });
            }
        }

        function fetchCTQData(materialCode) {
            fetch(`/get_ctq_data?material_code=${encodeURIComponent(materialCode)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    const ctqHeader = document.getElementById('ctq-header');
                    const ctqTable = document.getElementById('ctq-table');
                    const table = ctqTable.getElementsByTagName('tbody')[0] || ctqTable;

                    // 清空现有内容（保留表头）
                    while (table.rows.length > 1) {
                        table.deleteRow(1);
                    }

                    if (data.length > 0) {
                        // 显示表头和表格
                        ctqHeader.style.display = 'block';
                        ctqTable.style.display = 'block';

                        data.forEach((ctq, index) => {
                            const newRow = table.insertRow();
                            ctq.base_lower_value = ctq.base_value - ctq.lower_tolerance;
                            ctq.base_upper_value = ctq.base_value + ctq.upper_tolerance;

                            // 使用数字索引 1, 2, 3...
                            const numericIndex = index + 1;

                            newRow.innerHTML = `
                                <td>${numericIndex}</td>
                                <td>${ctq.position}</td>
                                <td>${ctq.base_lower_value}~${ctq.base_upper_value}</td>
                                <td><input type="number" id="ctq_${numericIndex}_size1" name="ctq_${numericIndex}_size1" step="0.1" required oninput="validateCTQ('${numericIndex}')" data-base-lower-value="${ctq.base_lower_value}" data-base-upper-value="${ctq.base_upper_value}"></td>
                                <td><input type="number" id="ctq_${numericIndex}_size2" name="ctq_${numericIndex}_size2" step="0.1" required oninput="validateCTQ('${numericIndex}')" data-base-lower-value="${ctq.base_lower_value}" data-base-upper-value="${ctq.base_upper_value}"></td>
                                <td><input type="number" id="ctq_${numericIndex}_size3" name="ctq_${numericIndex}_size3" step="0.1" required oninput="validateCTQ('${numericIndex}')" data-base-lower-value="${ctq.base_lower_value}" data-base-upper-value="${ctq.base_upper_value}"></td>
                                <td id="ctq_${numericIndex}_result">未检查</td> <!-- 新增列 -->
                                <!-- <td><input type="hidden" id="ctq_${numericIndex}_result_hidden" name="ctq_${numericIndex}_result_hidden" value="未检查"></td> -->
                            `;
                        });
                    } else {
                        // 隐藏表头和表格
                        ctqHeader.style.display = 'none';
                        ctqTable.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error fetching CTQ data:', error);
                    document.getElementById('ctq-header').style.display = 'none'; // 默认隐藏表头
                    document.getElementById('ctq-table').style.display = 'none'; // 默认隐藏表格
                });
        }

        function validateCTQ(numericIndex) {
            const size1 = parseFloat(document.getElementById(`ctq_${numericIndex}_size1`).value);
            const size2 = parseFloat(document.getElementById(`ctq_${numericIndex}_size2`).value);
            const size3 = parseFloat(document.getElementById(`ctq_${numericIndex}_size3`).value);

            const baseLowerValue = parseFloat(document.getElementById(`ctq_${numericIndex}_size1`).getAttribute('data-base-lower-value'));
            const baseUpperValue = parseFloat(document.getElementById(`ctq_${numericIndex}_size1`).getAttribute('data-base-upper-value'));

            const resultCell = document.getElementById(`ctq_${numericIndex}_result`);
            const hiddenFieldsDiv = document.getElementById('hidden-fields');
            let resultHiddenField = document.getElementById(`ctq_${numericIndex}_result_hidden`);

            if (!resultHiddenField) {
                resultHiddenField = document.createElement('input');
                resultHiddenField.type = 'hidden';
                resultHiddenField.id = `ctq_${numericIndex}_result_hidden`;
                resultHiddenField.name = `ctq_${numericIndex}_result_hidden`;
                hiddenFieldsDiv.appendChild(resultHiddenField);
            }

            if (isNaN(size1) || isNaN(size2) || isNaN(size3)) {
                resultCell.textContent = '未检查';
                resultCell.style.color = 'brown'; // 设置字体颜色为黄色
                resultHiddenField.value = '未检查';
                return;
            }

            if (size3 >= baseLowerValue && size3 <= baseUpperValue) {
                resultCell.textContent = '合格';
                resultCell.style.color = 'green'; // 设置字体颜色为绿色
                resultHiddenField.value = '合格';
            } else {
                resultCell.textContent = '不合格';
                resultCell.style.color = 'red'; // 设置字体颜色为红色
                resultHiddenField.value = '不合格';
            }
        }

        function calculateDefectRates() {
            const samplingQuantity = parseInt(document.getElementById('sampling_quantity').value);
            if (isNaN(samplingQuantity) || samplingQuantity === 0) {
                return;
            }
            for (let i = 1; i <= 5; i++) {
                const quantityElement = document.getElementById(`problem_${i}_quantity`);
                if (quantityElement) {
                    const quantity = parseInt(quantityElement.value);
                    if (!isNaN(quantity)) {
                        const rate = (quantity / samplingQuantity) * 100;
                        document.getElementById(`problem_${i}_rate`).value = rate.toFixed(2) + '%';
                    }
                }
            }
            let overallRate = 0;
            for (let i = 1; i <= 5; i++) {
                const rateStrElement = document.getElementById(`problem_${i}_rate`);
                if (rateStrElement && rateStrElement.value) {
                    const rate = parseFloat(rateStrElement.value.replace('%', ''));
                    overallRate += rate;
                }
            }
            document.getElementById('overall_defect_rate').value = overallRate.toFixed(2) + '%';
        }

        function addProblemPoint() {
            const table = document.getElementById('problem-points-table');
            const rowCount = table.rows.length;
            // 检查行数是否已经达到 6（包括表头）
            if (rowCount >= 6) {
                alert('最多只能录入 5 个问题点');
                return;
            }
            const newRow = table.insertRow(rowCount);

            const cell1 = newRow.insertCell(0);
            cell1.innerHTML = `<td>${rowCount}</td>`;

            const cell2 = newRow.insertCell(1);
            cell2.innerHTML = `<input type="text" id="problem_${rowCount}" name="problem_${rowCount}">`;

            const cell3 = newRow.insertCell(2);
            cell3.innerHTML = `<input type="number" id="problem_${rowCount}_quantity" name="problem_${rowCount}_quantity" oninput="calculateDefectRates()">`;

            const cell4 = newRow.insertCell(3);
            cell4.innerHTML = `<input type="text" id="problem_${rowCount}_rate" name="problem_${rowCount}_rate" readonly>`;
        }
    
        //将CTQ测量结果选项列的值包含在表单数据中。
        // 首先，在表单中添加一个隐藏字段，用于存储CTQ测量结果选项列的值。
        // 在validateCTQ函数中，当用户输入CTQ测量结果时，将该值存储在隐藏字段中。
        function prepareForm() {
            console.log('prepareForm is called');
            const ctqTable = document.getElementById('ctq-table');
            const rows = ctqTable.getElementsByTagName('tbody')[0].rows;

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const numericIndex = i + 1; // 使用数字索引 1, 2, 3...

                const size1 = row.cells[3].querySelector('input').value;
                const size2 = row.cells[4].querySelector('input').value;
                const size3 = row.cells[5].querySelector('input').value;
                const result = row.cells[6].textContent;

                const size1Input = document.createElement('input');
                size1Input.type = 'hidden';
                size1Input.name = `ctq_${numericIndex}_size1`;
                size1Input.value = size1;

                const size2Input = document.createElement('input');
                size2Input.type = 'hidden';
                size2Input.name = `ctq_${numericIndex}_size2`;
                size2Input.value = size2;

                const size3Input = document.createElement('input');
                size3Input.type = 'hidden';
                size3Input.name = `ctq_${numericIndex}_size3`;
                size3Input.value = size3;

                const resultInput = document.createElement('input');
                resultInput.type = 'hidden';
                resultInput.name = `ctq_${numericIndex}_result_hidden`;
                resultInput.value = result;

                document.forms[0].appendChild(size1Input);
                document.forms[0].appendChild(size2Input);
                document.forms[0].appendChild(size3Input);
                document.forms[0].appendChild(resultInput);

                // 调试信息：打印每个字段的值
                console.log(`ctq_${numericIndex}_size1: ${size1}`);
                console.log(`ctq_${numericIndex}_size2: ${size2}`);
                console.log(`ctq_${numericIndex}_size3: ${size3}`);
                console.log(`ctq_${numericIndex}_result_hidden: ${result}`);
            }
        }

        // 供应商编辑内容
        window.onload = function() {
            document.getElementById('supplier').addEventListener('change', function() {
                if (this.value === '_new_') {
                    document.getElementById('new-supplier').style.display = 'block';
                } else {
                    document.getElementById('new-supplier').style.display = 'none';
                }
            });

            // 设置默认供应商
            const supplierSelect = document.getElementById('supplier');
            const defaultSupplier = supplierSelect.getAttribute('data-default-supplier');
            if (defaultSupplier) {
                supplierSelect.value = defaultSupplier;
            } else {
                document.getElementById('new-supplier').style.display = 'block';
            }
        };

        function addNewSupplier() {
            const newSupplierInput = document.getElementById('new_supplier').value;
            if (newSupplierInput) {
                const supplierSelect = document.getElementById('supplier');
                const newOption = document.createElement('option');
                newOption.value = newSupplierInput;
                newOption.textContent = newSupplierInput;
                supplierSelect.appendChild(newOption);
                supplierSelect.value = newSupplierInput;
                document.getElementById('new-supplier').style.display = 'none';

                // 设置隐藏字段的值
                document.getElementById('hidden_new_supplier').value = newSupplierInput;
            } else {
                alert('请输入供应商名称');
            }
        }
    </script>
</head>
<body>
    <h1>检验记录录入</h1>
    <form method="post" onsubmit="prepareForm(); return true;">
        <!-- 其他表单字段 -->
        <label for="material_code">物料料号:</label>
        <input type="text" id="material_code" name="material_code" onblur="getMaterialInfo()" required><br>
        <label for="OrderID">订单号:</label>
        <input type="text" id="OrderID" name="OrderID" ><br>
        <label for="batch">批次号:</label>
        <input type="text" id="batch" name="batch" ><br>
        <label for="material_name">物料名称:</label>
        <input type="text" id="material_name" name="material_name" readonly><br>
        <label for="norm">规格:</label>
        <input type="text" id="norm" name="norm" readonly><br>
        <label for="material">材质:</label>
        <input type="text" id="material" name="material" required><br>
        <label for="colour">颜色:</label>
        <input type="text" id="colour" name="colour"  required><br>
        <label for="version">版本:</label>
        <input type="text" id="version" name="version" readonly><br>
        <label for="remark">备注:</label>
        <input type="text" id="remark" name="remark" readonly><br>
      

        <label for="supplier">供应商:</label>
        <select id="supplier" name="supplier" required data-default-supplier="{{ default_supplier }}">
            {% if suppliers %}
                {% for supplier in suppliers %}
                <option value="{{ supplier }}">{{ supplier }}</option>
                {% endfor %}
                <option value="_new_">+ 新增供应商</option>
            {% else %}
                <option value="_new_">+ 新增供应商</option>
            {% endif %}
        </select>
        <div id="new-supplier" style="display:none;">
            <input type="text" id="new_supplier" placeholder="请输入新的供应商名称">
            <button type="button" onclick="addNewSupplier()">添加</button>
        </div>
        <input type="hidden" id="hidden_new_supplier" name="new_supplier">
        <br>
        


        <label for="inspection_date">检验日期:</label>
        <input type="date" id="inspection_date" name="inspection_date"><br>
        <label for="inspection_result">检验结果:</label>
        <input type="text" id="inspection_result" name="inspection_result" required><br>
        <label for="incoming_quantity">来料数量:</label>
        <input type="number" id="incoming_quantity" name="incoming_quantity" required><br>
        <label for="sampling_quantity">抽检数量:</label>
        <input type="number" id="sampling_quantity" name="sampling_quantity" required oninput="calculateDefectRates()"><br>
    
        <label for="overall_defect_rate">总体不良率:</label>
        <input type="text" id="overall_defect_rate" name="overall_defect_rate" readonly><br>
    
        <h3 id="ctq-header" style="display: none;">CTQ尺寸测量要求</h3>
        <table id="ctq-table"  style="display: none;">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>位置</th>
                    <th>尺寸要求</th>
                    <th>尺寸1</th>
                    <th>尺寸2</th>
                    <th>尺寸3</th>
                    <th>结果</th>
                </tr>    
            </thead>
            
        </table>
    
        <h3>主要问题点</h3>
        <table id="problem-points-table" border="1">
            <tr>
                <th>序号</th>
                <th>问题点</th>
                <th>问题点数量</th>
                <th>问题点不良率</th>
            </tr>
            {% for i in range(1, 2) %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="text" id="problem_{{ i }}" name="problem_{{ i }}"></td>
                <td><input type="number" id="problem_{{ i }}_quantity" name="problem_{{ i }}_quantity" oninput="calculateDefectRates()"></td>
                <td><input type="text" id="problem_{{ i }}_rate" name="problem_{{ i }}_rate" readonly></td>
            </tr>
            {% endfor %}
        </table>
        <button type="button" onclick="addProblemPoint()">添加问题点</button>
        <input type="submit" value="提交">

        <!-- 统一生成隐藏字段 -->
        <div id="hidden-fields"></div>
    </form>
    <a href="{{ url_for('index') }}">返回首页</a>
</body>
</html>