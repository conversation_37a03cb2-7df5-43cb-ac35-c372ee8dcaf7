<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检验记录查询</title>
</head>
<body>
    <h1>检验记录查询</h1>
    <form method="post">
        <!-- 修改为物料编码搜索 -->
        <label for="material_code">物料编码搜索:</label>
        <input type="text" id="material_code" name="material_code" value="{{ request.form.get('material_code', '') }}">
        <label for="inspection_date">检验日期搜索:</label>
        <input type="date" id="inspection_date" name="inspection_date" value="{{ request.form.get('inspection_date', '') }}">
        <input type="submit" value="搜索">
    </form>
    {% if records %}
    <table border="1">
        <tr>
            <!-- 增加物料编码和供应商的表头 -->
            <th>物料编码</th>
            <th>物料名称</th>
            <th>供应商</th>
            <th>检验日期</th>
            <th>订单号</th>
            <th>批次号</th>
            <th>检验结果</th>
            <th>问题点 1</th>
            <th>问题点 1 数量</th>
            <th>问题点 1 不良率</th>
            <th>问题点 2</th>
            <th>问题点 2 数量</th>
            <th>问题点 2 不良率</th>
            <th>问题点 3</th>
            <th>问题点 3 数量</th>
            <th>问题点 3 不良率</th>
            <th>问题点 4</th>
            <th>问题点 4 数量</th>
            <th>问题点 4 不良率</th>
            <th>问题点 5</th>
            <th>问题点 5 数量</th>
            <th>问题点 5 不良率</th>
            <th>总体不良率</th>
        </tr>
        {% for record in records %}
        <tr>
            <!-- 显示物料编码和供应商 -->
            <td>{{ record[0] }}</td>
            <td>{{ record[1] }}</td>
            <td>{{ record[2] }}</td>
            <td>{{ record[3] }}</td>
            <td>{{ record[4] }}</td>
            <td>{{ record[5] }}</td>
            <td>{{ record[6] }}</td>
            <td>{{ record[7] }}</td>
            <td>{{ record[8] }}</td>
            <td>{{ record[9] }}</td>
            <td>{{ record[10] }}</td>
            <td>{{ record[11] }}</td>
            <td>{{ record[12] }}</td>
            <td>{{ record[13] }}</td>
            <td>{{ record[14] }}</td>
            <td>{{ record[15] }}</td>
            <td>{{ record[16] }}</td>
            <td>{{ record[17] }}</td>
            <td>{{ record[18] }}</td>
            <td>{{ record[19] }}</td>
            <td>{{ record[20] }}</td>
            <td>{{ record[21] }}</td>
            <td>{{ record[22] }}</td>
            <td>{{ record[23] }}</td>
        </tr>
        {% endfor %}
    </table>
    {% else %}
    <p>未查询到相关记录。</p>
    {% endif %}
    <a href="{{ url_for('index') }}">返回首页</a>
</body>
</html>