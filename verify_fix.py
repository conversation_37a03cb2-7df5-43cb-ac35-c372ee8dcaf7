#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复是否正确
"""

def verify_load_data_handler():
    """验证load_data_handler.py的修复"""
    print("验证load_data_handler.py修复...")
    
    try:
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        checks = [
            ('size_row_count = len(size_data)', 'size_row_count计算修复'),
            ('size_row_count=size_row_count', 'size_row_count参数传递'),
            ('size_1_position=size_1.get', 'size_1变量'),
            ('function_1_check=function_1.get', 'function_1变量'),
            ('function_5_tests=function_5.get', 'function_5_tests变量'),
        ]
        
        all_good = True
        for check_str, desc in checks:
            if check_str in content:
                print(f"✅ {desc}: 正确")
            else:
                print(f"❌ {desc}: 缺失")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def create_minimal_test():
    """创建最小测试应用"""
    print("\n创建最小测试应用...")
    
    test_app_content = '''#!/usr/bin/env python3
from flask import Flask
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

app = Flask(__name__)

@app.route('/test-load-data/<sample_id>')
def test_load_data(sample_id):
    """测试load-data功能"""
    try:
        from blueprints.material_confirmation.load_data_handler import load_sample_data
        return load_sample_data(sample_id)
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        return f"<h1>错误详情</h1><pre>{error_details}</pre>", 500

if __name__ == '__main__':
    print("启动测试应用...")
    print("测试URL: http://127.0.0.1:5002/test-load-data/MSC20250801001")
    app.run(debug=True, port=5002, use_reloader=False)
'''
    
    try:
        with open('test_app_minimal.py', 'w', encoding='utf-8') as f:
            f.write(test_app_content)
        print("✅ 创建测试应用成功: test_app_minimal.py")
        return True
    except Exception as e:
        print(f"❌ 创建测试应用失败: {e}")
        return False

def main():
    print("=" * 60)
    print("验证修复并创建测试应用")
    print("=" * 60)
    
    # 验证修复
    if verify_load_data_handler():
        print("\n🎉 修复验证通过！")
    else:
        print("\n❌ 修复验证失败！")
        return
    
    # 创建测试应用
    if create_minimal_test():
        print("\n📋 下一步操作:")
        print("1. 运行测试应用: python test_app_minimal.py")
        print("2. 在浏览器中访问: http://127.0.0.1:5002/test-load-data/MSC20250801001")
        print("3. 如果测试应用正常工作，说明修复成功")
        print("4. 然后停止测试应用，重新启动主应用: python app.py")
        print("\n🔧 如果仍有问题，可能需要:")
        print("- 检查数据库连接")
        print("- 确保数据库中有测试数据")
        print("- 检查模板文件路径")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
