import datetime
import random
from flask import Blueprint, render_template, jsonify, request
from db_config import get_db_connection
import mysql.connector
from flask import Flask
app = Flask(__name__, static_folder='d:\\360极速浏览器X下载\\检验系统\\static', static_url_path='/static')

Material_Sample_Confirmation_Form_load_data_bp = Blueprint('Material_Sample_Confirmation_Form_load_data_bp', __name__)

# 动态数据接口（返回JSON）
@Material_Sample_Confirmation_Form_load_data_bp.route('/<sample_id>')
def load_sample_data(sample_id):
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询基本信息
        cursor.execute("""
            SELECT report_code, supplier, inspection_date, sample_count, inspector,
                   material_number, graph_number, material_name, drawing_version,
                   material_texture, surface_processing, sample_status, other_textbox,
                   final_judgment, opinion, review
            FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (sample_id,))
        base_info = cursor.fetchone()
        
        if not base_info:
            return jsonify({"error": "未找到对应报告数据"}), 404
        
        # 查询尺寸数据
        cursor.execute("""
            SELECT 
                size_number, 
                COALESCE(position, '') as position, 
                COALESCE(value, '') as value, 
                COALESCE(min_value, '') as min_value, 
                COALESCE(max_value, '') as max_value,
                COALESCE(measure_1, '') as measure_1, 
                COALESCE(measure_2, '') as measure_2, 
                COALESCE(measure_3, '') as measure_3, 
                COALESCE(measure_4, '') as measure_4, 
                COALESCE(measure_5, '') as measure_5,
                COALESCE(NULLIF(check_result, ''), '/') as check_result, 
                COALESCE(note, '') as note
            FROM material_sample_size_data
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY size_number
        """, (sample_id,))
        
        # 获取所有尺寸数据行
        all_size_data = cursor.fetchall()
        
        # 过滤出有实际内容的尺寸数据行
        size_data = []
        for row in all_size_data:
            has_content = False
            for field in ['position', 'value', 'min_value', 'max_value', 'measure_1', 
                          'measure_2', 'measure_3', 'measure_4', 'measure_5', 'check_result', 'note']:
                if row[field] and str(row[field]).strip() and str(row[field]).lower() != 'none':
                    has_content = True
                    break
            if has_content:
                size_data.append(row)
        
        # 统计非空尺寸数据行数
        size_row_count = len(size_data)
        
        # 提取尺寸1-3的字段
        size_1 = next((item for item in size_data if item['size_number'] == 1), {})
        size_2 = next((item for item in size_data if item['size_number'] == 2), {})
        size_3 = next((item for item in size_data if item['size_number'] == 3), {})
        
        # 查询外观检查数据
        cursor.execute("""
            SELECT 
                check_number, 
                COALESCE(NULLIF(check_result, ''), '/') as check_result, 
                COALESCE(note, '') as note,
                COALESCE(other_info, '') as other_info
            FROM material_sample_appearance
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (sample_id,))
        appearance_data = cursor.fetchall()
        
        # 查询功能检查数据
        cursor.execute("""
            SELECT check_number, check_result, note, burnin_info, electrical_info,
                   tests_info, other_test, other_info
            FROM material_sample_function
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (sample_id,))
        function_data = cursor.fetchall()
        
        # 查询问题记录和图片路径
        cursor.execute("""
            SELECT q.question_number, q.question_text, q.image_path
            FROM material_sample_questions q
            WHERE q.form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY q.question_number
        """, (sample_id,))
        
        questions = []
        question_images = {}
        valid_question_numbers = []
        
        for row in cursor.fetchall():
            # 检查问题文本是否存在
            if row['question_text']:
                questions.append({'question_number': row['question_number'], 'question_text': row['question_text']})
                valid_question_numbers.append(row['question_number'])
                
                # 如果有图片路径则处理图片
                if row['image_path']:
                    image_path = row['image_path'].replace('/', '\\')
            if row['image_path']:
                import os
                # 确保路径格式正确
                image_path = row['image_path'].replace('/', '\\')
                
                # 检查路径是否存在，如果不存在尝试多种方式查找
                if not os.path.exists(image_path):
                    # 尝试从static目录开始查找
                    static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', image_path.lstrip('\\'))
                    if os.path.exists(static_path):
                        image_path = static_path
                    else:
                        # 尝试从绝对路径查找
                        abs_path = os.path.abspath(image_path)
                        if os.path.exists(abs_path):
                            image_path = abs_path
                        else:
                            # 尝试从图片存储基础路径查找
                            base_path = os.path.join('D:\\检验系统图片\\', image_path.lstrip('\\'))
                            if os.path.exists(base_path):
                                image_path = base_path
                
                dir_path = os.path.dirname(image_path)
                file_name = os.path.basename(image_path)
                
                # 检查文件是否存在
                if os.path.exists(image_path):
                    # 使用相对路径返回给前端
                    # 直接使用绝对路径映射到静态资源
                    static_base = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
                    if not os.path.exists(static_base):
                        os.makedirs(static_base)
                    
                    # 复制图片到static目录
                    dest_path = os.path.join(static_base, os.path.basename(image_path))
                    if not os.path.exists(dest_path):
                        import shutil
                        shutil.copy2(image_path, dest_path)
                    
                    question_images[row['question_number']] = {
                        'dir_path': dir_path,
                        'file_name': file_name,
                        'full_path': '/static/' + os.path.basename(image_path)
                    }

                    # 添加调试信息
                    print(f"Original image path: {row['image_path']}")
                    print(f"Final image path: {image_path}")
        
        return render_template(
            'Material_Sample_Confirmation_Form_load_data.html',
            report_code=base_info['report_code'],
            supplier=base_info['supplier'],
            inspection_date=base_info['inspection_date'],
            sample_count=base_info['sample_count'] if base_info['sample_count'] is not None else '',
            inspector=base_info['inspector'],
            material_number=base_info['material_number'],
            graph_number=base_info['graph_number'],
            material_name=base_info['material_name'],
            drawing_version=base_info['drawing_version'],
            material_texture=base_info['material_texture'],
            surface_processing=base_info['surface_processing'],
            sample_status_list = base_info['sample_status'].split(',') if base_info['sample_status'] else [],
            other_textbox_value = base_info['other_textbox'],
            final_judgment=base_info['final_judgment'],
            opinion=base_info['opinion'],
            review=base_info['review'],
            size_row_count=size_row_count,
            question_images=question_images,
            valid_question_numbers=valid_question_numbers,

            # 尺寸1-3的字段
            size_1_position=size_1.get('position', ''),
            size_1_value=size_1.get('value', ''),
            size_1_min=size_1.get('min_value', ''),
            size_1_max=size_1.get('max_value', ''),
            size_1_measure_1=size_1.get('measure_1', ''),
            size_1_measure_2=size_1.get('measure_2', ''),
            size_1_measure_3=size_1.get('measure_3', ''),
            size_1_measure_4=size_1.get('measure_4', ''),
            size_1_measure_5=size_1.get('measure_5', ''),
            size_1_check=size_1.get('check_result', ''),
            size_1_note=size_1.get('note', ''),
            
            size_2_position=size_2.get('position', ''),
            size_2_value=size_2.get('value', ''),
            size_2_min=size_2.get('min_value', ''),
            size_2_max=size_2.get('max_value', ''),
            size_2_measure_1=size_2.get('measure_1', ''),
            size_2_measure_2=size_2.get('measure_2', ''),
            size_2_measure_3=size_2.get('measure_3', ''),
            size_2_measure_4=size_2.get('measure_4', ''),
            size_2_measure_5=size_2.get('measure_5', ''),
            size_2_check=size_2.get('check_result', ''),
            size_2_note=size_2.get('note', ''),
            
            size_3_position=size_3.get('position', ''),
            size_3_value=size_3.get('value', ''),
            size_3_min=size_3.get('min_value', ''),
            size_3_max=size_3.get('max_value', ''),
            size_3_measure_1=size_3.get('measure_1', ''),
            size_3_measure_2=size_3.get('measure_2', ''),
            size_3_measure_3=size_3.get('measure_3', ''),
            size_3_measure_4=size_3.get('measure_4', ''),
            size_3_measure_5=size_3.get('measure_5', ''),
            size_3_check=size_3.get('check_result', ''),
            size_3_note=size_3.get('note', ''),
          
            # 传递所有尺寸数据
            all_size_data=all_size_data,  # 所有尺寸数据
            size_data=size_data,  # 有数据的尺寸行
            appearance_data=appearance_data,
            function_data=function_data,
            questions=questions,
            
            # 功能检查数据
            function_1_check=next((item['check_result'] for item in function_data if item['check_number'] == 1), '/'),
            function_1_note=next((item['note'] for item in function_data if item['check_number'] == 1), ''),
            function_2_check=next((item['check_result'] for item in function_data if item['check_number'] == 2), '/'),
            function_2_note=next((item['note'] for item in function_data if item['check_number'] == 2), ''),
            function_3_check=next((item['check_result'] for item in function_data if item['check_number'] == 3), '/'),
            function_3_note=next((item['note'] for item in function_data if item['check_number'] == 3), ''),
            function_4_check=next((item['check_result'] for item in function_data if item['check_number'] == 4), '/'),
            function_4_note=next((item['note'] for item in function_data if item['check_number'] == 4), ''),
            function_4_burnin=next((item['burnin_info'] for item in function_data if item['check_number'] == 4), ''),
            function_4_electrical=next((item['electrical_info'] for item in function_data if item['check_number'] == 4), ''),
            function_5_check=next((item['check_result'] for item in function_data if item['check_number'] == 5), '/'),
            function_5_note=next((item['note'] for item in function_data if item['check_number'] == 5), ''),
            function_5_tests=next(([test.strip('" ') for test in item['tests_info'].split(',')] if item['tests_info'] else [] for item in function_data if item['check_number'] == 5), []),
            function_5_other_test=next((item['other_test'] for item in function_data if item['check_number'] == 5), ''),
            function_6_check=next((item['check_result'] for item in function_data if item['check_number'] == 6), '/'),
            function_6_note=next((item['note'] for item in function_data if item['check_number'] == 6), ''),
            function_6_other=next((item['other_info'] for item in function_data if item['check_number'] == 6), '')
        )
        
    except mysql.connector.Error as err:
        return jsonify({"error": f"数据库错误: {str(err)}"}), 500
    except Exception as e:
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()



@Material_Sample_Confirmation_Form_load_data_bp.route('/submit', methods=['POST'])
def submit_form():
    try:
        report_code = f"SC{datetime.datetime.now().strftime('%Y%m%d')}-{random.randint(100,999)}"
        sample_status = request.form.getlist('sample_status[]')  # 获取所有选中的复选框值
        other_textbox = request.form.get('other_textbox', '')    # 获取其他文本框的内容

        # 将复选框值转为字符串存储
        sample_status_str = ','.join(sample_status) if sample_status else ''
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""INSERT INTO material_sample_confirmation_form 
            (report_code, supplier, inspection_date, sample_count, inspector, 
             material_number, graph_number, material_name, drawing_version, 
             sample_status, other_textbox,
             material_texture, surface_processing, sample_status, other_textbox, 
             final_judgment, opinion, review)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            report_code,
            request.form['supplier'],
            request.form['inspection_date'],
            request.form['sample_count'],
            request.form['inspector'],
            request.form['material_number'],
            request.form['graph_number'],
            request.form['material_name'],
            request.form['drawing_version'],
            request.form['material_texture'],
            request.form['surface_processing'],
            request.form['sample_status'],
            request.form.get('other_textbox', ''),
            request.form.get('final_judgment', ''),
            request.form.get('opinion', ''),
            request.form.get('review', '')
        ))
        form_id = cursor.lastrowid
        
        # 保存尺寸数据
        for i in range(1, 31):
            size_number = request.form.get(f'size_{i}_position', '')
            position = request.form.get(f'size_{i}_position', '')
            value = request.form.get(f'size_{i}_value', '')
            min_val = request.form.get(f'size_{i}_min', '')
            max_val = request.form.get(f'size_{i}_max', '')
            measures = [
                request.form.get(f'size_{i}_measure_{j}', '') for j in range(1,6)
            ]
            check_result = request.form.get(f'size_{i}_check', '')
            note = request.form.get(f'size_{i}_note', '')
            
            cursor.execute("""
                INSERT INTO material_sample_size_data 
                (form_id, size_number, position, value, min_value, max_value, 
                 measure_1, measure_2, measure_3, measure_4, measure_5, 
                 check_result, note)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                form_id, size_number, position, value, min_val, max_val,
                *measures, check_result, note
            ))
        
        # 保存外观检查
        for i in range(1,5):
            check_number = i
            check_result = request.form.get(f'appearance_{i}_check', '')
            note = request.form.get(f'appearance_{i}_note', '')
            other_info = request.form.get(f'appearance_{i}_other', '')
            cursor.execute("""
                INSERT INTO material_sample_appearance 
                (form_id, check_number, check_result, note, other_info)
                VALUES (%s, %s, %s, %s, %s)
            """, (form_id, check_number, check_result, note, other_info))
        
        # 保存功能检查
        for i in range(1,7):
            check_number = i
            check_result = request.form.get(f'function_{i}_check', '')
            note = request.form.get(f'function_{i}_note', '')
            burnin = request.form.get(f'function_{i}_burnin', '')
            electrical = request.form.get(f'function_{i}_electrical', '')
            tests_list = request.form.getlist(f'function_{i}_tests[]')
            print(f"Tests list for function_{i}: {tests_list}")  # 调试输出
            tests = ','.join(tests_list)
            other_test = request.form.get(f'function_{i}_other_test', '')
            cursor.execute("""
                INSERT INTO material_sample_function 
                (form_id, check_number, check_result, note, burnin_info, 
                electrical_info, tests_info, other_test)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (form_id, check_number, check_result, note, burnin, electrical, tests, other_test))
        
        # 保存问题记录
        for i in range(1,19):
            question_text = request.form.get(f'question_{i}', '')
            cursor.execute("""
                INSERT INTO material_sample_questions 
                (form_id, question_number, question_text)
                VALUES (%s, %s, %s)
            """, (form_id, i, question_text))
            
            # 保存问题图片
            image_path = request.form.get(f'question_{i}_image', '')
            if image_path:
                cursor.execute("""
                    INSERT INTO material_sample_questions
                    (form_id, question_number, image_path)
                    VALUES (%s, %s, %s)
                """, (form_id, i, image_path))
        
        conn.commit()
        return jsonify({"report_code": report_code}), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()