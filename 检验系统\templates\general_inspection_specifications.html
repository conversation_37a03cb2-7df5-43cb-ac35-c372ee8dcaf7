<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>通用检验规范</title>
    <style>
        /* 固定列的样式 */
        th.col-checkbox,
        th.col-id,
        th.col-unique-id,
        th.col-is-standard,
        th.col-material-type,
        th.col-inspection-item,
        th.col-inspection-method,
        th.col-inspection-description,
        th.col-sampling-level,
        th.col-cr,
        th.col-maj,
        th.col-min {
            z-index: 12 !important; /* 数据列低于表头固定列 */
            background: rgb(187, 211, 224) !important;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        th.col-checkbox,
        td.col-checkbox,
        th.col-id,
        td.col-id{
            width: 45px !important; /* 固定宽度 */
            min-width: 45px !important; /* 最小宽度 */
            max-width: 45px !important; /* 最大宽度 */
            flex: 0 0 45px !important; /* 锁定 Flex 布局 */
            position: sticky !important;
            left: 0;
        }

        th.col-unique-id,
        td.col-unique-id{
            width: 110px !important; /* 固定宽度 */
            min-width: 110px !important; /* 最小宽度 */
            max-width: 110px !important; /* 最大宽度 */
            flex: 0 0 110px !important; /* 锁定 Flex 布局 */
            position: sticky !important;
            left: 0;
        }

        th.col-material-type,
        td.col-material-type{
            flex: 0 0 auto !important; /* 锁定 Flex 布局 */
            position: sticky !important;
            left: 0;
        }

        th.col-is-standard,
        td.col-is-standard,
        th.col-sampling-level,
        td.col-sampling-level{
            width: 70px !important; /* 固定宽度 */
            min-width: 70px !important; /* 最小宽度 */
            max-width: 70px !important; /* 最大宽度 */
            flex: 0 0 70px !important; /* 锁定 Flex 布局 */
            position: sticky !important;
            left: 0;            
        }
        th.col-cr,
        td.col-cr,
        th.col-maj,
        td.col-maj,
        th.col-min,
        td.col-min {
            width: 60px !important; /* 固定宽度 */
            min-width: 60px !important; /* 最小宽度 */
            max-width: 60px !important; /* 最大宽度 */
            flex: 0 0 60px !important; /* 锁定 Flex 布局 */
            position: sticky !important;
            left: 0;
        }

        /* 修改表格容器设置 */
  
        /* 表头列宽设置 */
        th.col-material-type { width: 80px; } /* 固定宽度 */
        th.col-inspection-item { width: 80px;
        } /* 固定宽度 */
        th.col-inspection-method { width: 120px; 
            text-align: center !important; 
        } /* 固定宽度，居中对齐 */
        th.col-inspection-description { 
            width: 380px; 
            text-align: center !important; 
        } /* 固定宽度，居中对齐 */
        
       

        /* 合并后的CSS文件内容 */
        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed; /* 固定表格布局 */
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center !important; /* 默认居中对齐 */
            white-space: nowrap; /* 禁止文本换行 */
            overflow: hidden; /* 隐藏溢出内容 */
        }

        /* 拖动手柄样式 */
        .drag-handle {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 5px;
            cursor: col-resize;
            background-color: #ccc;
            z-index: 3;
        }

        .resize-line {
            display: none;
            position: absolute;
            width: 2px;
            background: #007bff;
            height: 100vh;
            top: 0;
            z-index: 1000;
            pointer-events: none;
        }

        .resize-handle {
            position: absolute;
            right: -4px;
            top: 0;
            bottom: 0;
            width: 8px;
            cursor: col-resize;
            z-index: 3;
            background-color: transparent;
            opacity: 1 !important;
        }

        .resize-handle:hover,
        .resize-handle:active {
            background-color: #007bff;
            opacity: 1 !important;
        }

        th:hover .resize-handle {
            opacity: 1 !important;
        }

        /* 在原有table样式基础上添加 */
        #specificationsTable {
            table-layout: fixed;
            width: 100%;
            overflow-x: auto;
            min-width: calc(
                45px * 2 +      /* 复选框和序号列 */
                110px +         /* 唯一码列 */
                70px * 2 +      /* 通用规范和抽样水准列 */
                60px * 3 +      /* CR/MAJ/MIN 列 */
                80px +          /* 物料类型列（改为可变） */
                80px +          /* 检验项目列 */
                120px +         /* 检验方法列 */
                380px           /* 检验说明列 */
            );
        }
        .resize-handle:hover {
            background-color: #007bff; /* 更明显的悬停反馈 */
            transition: background-color 0.2s;
        }
        th {
            position: relative;
            user-select: none; /* 防止拖动时选中文本 */
            top: 0; /* 表头固定 */
            z-index: 1;
            background-color: #f2f2f2;
        }
        .sticky-col {
            position: sticky;
            right: 0;
            background: white;
            z-index: 1;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }
        input[type="checkbox"] {
            transform: scale(1.5);
        }
        

        .breadcrumb {
            padding: 5px 0;
            margin-bottom: 5px;
            font-size: 16px;
            z-index: 1; /* 确保其层级低于表格容器 */
        }

/* 对于其他可能遮挡的元素也进行类似调整 */
        
        .search-container {
            background-color: white;
            padding: 5px;
            margin-top: 2px;
            margin-bottom: 2px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .search-field {
            display: flex;
            align-items: center;
            gap: 5px;
            margin: 2px 0;
        }
        .base-search .remove-search-btn {
            display: none !important;
        }
        .add-search-btn, .remove-search-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 16px;
            line-height: 1;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        .add-search-btn {
            background-color: #28a745;
        }
        .remove-search-btn {
            background-color: #dc3545;
        }
        .search-btn {
            background-color: #007bff;
            color: white;
            padding: 4px 15px;
            border-radius: 4px;
        }
        select, input[type="text"] {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px 8px;
        }
        button {
            padding: 5px 10px;
            cursor: pointer;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .aql-select,
        .custom-aql {
            width: 100%;
            padding: 3px;
            box-sizing: border-box;
            font-size: 12px; /* 减小字体大小 */
        }

        .custom-aql {
            width: 100%;
            padding: 3px;
            box-sizing: border-box;
        }

        td input[type="number"] {
            width: 70px;
        }

        /* 确保自定义输入框和下拉框在同一行 */
        td {
            white-space: nowrap;
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            min-height: 32px; /* 确保单元格有足够的高度 */
        }
        th[data-sort] {
            /* 禁用表头默认的鼠标手势 */
            cursor: default !important;
        }

        th[data-sort]::after {
            content: '';
            position: absolute;
            right: 6px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
        }

        th[data-sort].sort-asc::after {
            border-bottom: 5px solid #333;
            border-top: none;
        }

        th[data-sort].sort-desc::after {
            border-top: 5px solid #333;
            border-bottom: none;
        }

        /* 表格容器样式 */
        .table-container {
            width: 100%; /* 默认宽度为 100% */
            overflow-x: auto;
            height: calc(100vh - 150px);
            border: 2px solid #ddd;
            margin-top: 2px !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            padding-top: 0;
        }

        /* 表格基础样式 */
        table {
            width: 100%;
            table-layout: fixed; /* 固定表格布局 */
            border-collapse: collapse;
        }

        /* 单元格基础样式 */
        th, td {
            white-space: nowrap;
            overflow: hidden;
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center !important; /* 默认居中对齐 */
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
        }
 
        
        /* 确保其他列宽不受影响 */
        

        /* 表头样式 */
        thead {
            position: sticky;
            top: -1px; /* 略微向上偏移以覆盖边框 */
            z-index: 2;
            background-color: #f2f2f2;
            margin-top: 0;
            border-top: 2px solid #ddd; /* 添加上边框以匹配容器边框 */
        }

        thead tr {
            height: auto;
            background-color: #f2f2f2;
        }

        /* 检验说明列左对齐 */
        td:nth-child(8), /* 检验说明列 */
        th:nth-child(8) {
            text-align: left; /* 左对齐 */
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
        }

        /* 检验方法列左对齐 */
        td:nth-child(7), /* 检验方法列 */
        th:nth-child(7) {
            text-align: left; /* 左对齐 */
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
        }

        /* 输入框样式 */
        td input[type="text"],
        td input[type="number"],
        td select {
            width: 100%;
            padding: 4px;
            box-sizing: border-box;
            text-align: center; /* 输入框内文字居中 */
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
        }

        /* 检验说明列的输入框左对齐 */
        td:nth-child(8) input[type="text"] {
            text-align: left; /* 左对齐 */
            white-space: normal; /* 允许文本换行 */
            word-wrap: break-word; /* 确保长单词或URL地址可以换行 */
            overflow-wrap: break-word; /* 确保长单词或URL地址可以换行 */
        }

        /* 复选框容器居中 */
        td:first-child {
            text-align: center;
        }

        /* 序号列居中 */
        td:nth-child(2) {
            text-align: center;
        }

        /* AQL 相关列的样式 */
        .aql-select,
        .custom-aql {
            width: 100%;
            padding: 3px;
            box-sizing: border-box;
            font-size: 12px; /* 减小字体大小 */
        }

        /* AQL 单元格样式 */
        td:nth-child(10),  /* CR 列 */
        td:nth-child(11),  /* MAJ 列 */
        td:nth-child(12) { /* MIN 列 */
            padding: 4px;
            min-width: 80px;
            white-space: normal;
        }

        /* AQL 下拉框选项样式 */
        .aql-select option {
            font-size: 12px;
            padding: 2px;
        }

        /* 表格内的按钮样式 */
        td button {
            margin: 2px;
            padding: 4px 8px;
        }

        /* 表格内的复选框样式 */
        td input[type="checkbox"] {
            transform: scale(1.5);
            margin: 0 auto;
            display: block;
        }

        /* 添加到现有的 style 标签中 */
        td input[type="text"],
        td input[type="number"],
        td select {
            width: 100%;
            box-sizing: border-box;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            cursor: text;
            pointer-events: auto;
        }

        td input[type="text"]:focus,
        td input[type="number"]:focus,
        td select:focus {
            outline: 2px solid #007bff;
            border-color: #007bff;
        }

        /* 确保表格单元格可以容纳输入框 */
        td {
            padding: 4px;
            position: relative;
        }

        /* 自定义 AQL 输入框样式 */
        .custom-aql {
            margin-top: 2px;
        }

        /* 确保下拉框和输入框在单元格内正确显示 */
        td select,
        td input {
            margin: 0;
            min-width: 60px;
        }

        /* 添加悬停效果 */
        td input[type="text"]:hover,
        td input[type="number"]:hover,
        td select:hover {
            border-color: #007bff;
        }

        /* 增强弹窗样式 */
        .duplicate-details {
            border: 1px solid #eee;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }

        .duplicate-details p {
            margin: 5px 0;
            font-size: 0.9em;
        }

        /* 面包屑导航样式 */
        .breadcrumb {
            padding: 5px 0;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb span {
            color: #6c757d;
            margin: 0 8px;
        }
        
        .breadcrumb .current {
            color: #333;
            font-weight: 500;
        }
        

        /* 固定列数据单元格 */
        td:nth-child(1),
        td:nth-child(2),
        td:nth-child(3),
        td:nth-child(4),
        td:nth-child(5) {
        position: sticky !important;
        left: 0;
        z-index: 12 !important; /* 数据列低于表头固定列 */
        background: white !important;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        /* 数据左对齐 */
        td.col-inspection-method {
            text-align: center !important; /* 数据左对齐 */
        }
        td.col-inspection-description {
            text-align: left !important; /* 数据左对齐 */
        }
    </style>
</head>
<body>
    <div class="breadcrumb">
        <a href="{{ url_for('index') }}">首页</a>
        <span>></span>
        <span class="current">通用检验规范</span>
    </div>
    
    <!-- 修改搜索表单 -->
    <form id="searchForm" onsubmit="event.preventDefault(); performSearch();">
        <div id="search-container" class="search-container" style="gap: 0.1px;">
            <!-- 基础搜索行 -->
            <div class="search-field base-search">
                <select name="search_field" style="width: 120px;">
                    <option value="material_type">物料类型</option>                   
                    <option value="inspection_item">检验项目</option>
                    <option value="inspection_method">检验方法</option>
                    <option value="inspection_description">检验说明</option>
                    <option value="is_standard">通用规范</option>
                    <option value="cr">CR</option>
                    <option value="maj">MAJ</option>
                    <option value="min">MIN</option>
                </select>
                <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容..." 
                       onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
                <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                <button type="submit" class="search-btn">搜索</button>
            </div>
        </div>
    </form>

    <!-- 操作按钮容器 -->
    <div class="operation-buttons">
        <button onclick="showAddRow()" id="addBtn">添加新规范</button>
        <button onclick="startEdit()" id="manageBtn" disabled>管理</button>
        <button onclick="deleteSelected()" id="deleteBtn" disabled>删除</button>
        <button onclick="saveEdit()" id="saveBtn" style="display: none;">保存</button>
        <button onclick="saveNewRow()" id="saveNewBtn" style="display: none;">保存新规范</button>
        <button onclick="cancelEdit()" id="cancelBtn" style="display: none;">取消</button>
    </div>

    <div class="table-container">
        <table id="specificationsTable" style="table-layout: fixed;">
            <thead>
                <tr>
                    <th class="col-checkbox"><input type="checkbox" onchange="toggleAllRows(this)"></th>
                    <th class="col-id">序号</th>
                    <th class="col-unique-id" data-sort="unique_id">唯一码</th>
                    <th class="col-is-standard" data-sort="is_standard">通用规范</th>
                    <th class="col-material-type" data-sort="material_type">物料类型</th>
                    <th class="col-inspection-item" data-sort="inspection_item">检验项目</th>
                    <th class="col-inspection-method" data-sort="inspection_method">检验方法</th>
                    <th class="col-inspection-description" data-sort="inspection_description">检验说明</th>
                    <th class="col-sampling-level" data-sort="sampling_level">抽样水准</th>
                    <th class="col-cr" data-sort="cr">CR</th>
                    <th class="col-maj" data-sort="maj">MAJ</th>
                    <th class="col-min" data-sort="min">MIN</th>
                </tr>
            </thead>
            <tbody>
                {% for specification in specifications %}
                <tr data-id="{{ specification.id }}">
                    <td><input type="checkbox" onchange="toggleRowActions(this)"></td>
                    <td>{{ specification.id }}</td>
                    <td>{{ specification.unique_id }}</td>
                    <td>{{ 'Y' if specification.is_standard else 'N' }}</td>
                    <td>{{ specification.material_type }}</td>
                    <td>{{ specification.inspection_item }}</td>
                    <td class="col-inspection-method">{{ specification.inspection_method }}</td>
                    <td class="col-inspection-description">{{ specification.inspection_description }}</td>
                    <td>{{ specification.sampling_level }}</td>
                    <td>
                        <span class="display-value">{{ specification.cr or '' }}</span>
                        <select name="cr" class="aql-select" onchange="toggleCustomAQL(this)">
                            {{ generateAQLOptions('cr', specification.cr) }}
                        </select>
                        <input type="number" class="custom-aql" style="display:none" 
                            step="0.001" min="0" max="100" value="{{ specification.cr or '' }}">
                    </td>
                    <td>
                        <span class="display-value">{{ specification.maj or '' }}</span>
                        <select name="maj" class="aql-select" onchange="toggleCustomAQL(this)">
                            {{ generateAQLOptions('maj', specification.maj) }}
                        </select>
                        <input type="number" class="custom-aql" style="display:none" 
                            step="0.001" min="0" max="100" value="{{ specification.maj or '' }}">
                    </td>
                    <td>
                        <span class="display-value">{{ specification.min or '' }}</span>
                        <select name="min" class="aql-select" onchange="toggleCustomAQL(this)">
                            {{ generateAQLOptions('min', specification.min) }}
                        </select>
                        <input type="number" class="custom-aql" style="display:none" 
                            step="0.001" min="0" max="100" value="{{ specification.min or '' }}">
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div id="pagination"></div>

    <script>
        // 添加分页和每页显示数量控制
        let currentPage = 1;
        let pageSize = 20;
        let sortField = '';
        let sortOrder = 'asc';
        let isDragging = false; // 新增标志位

        function updatePageSize(size) {
            pageSize = parseInt(size);
            currentPage = 1;
            loadSpecifications();
            
            // 更新选择框的显示
            const pageSizeSelect = document.querySelector('select[name="page_size"]');
            if (pageSizeSelect) {
                pageSizeSelect.value = size;
            }
        }

        async function loadSpecifications(searchParams = '') {
            try {
                let url = '/api/general_inspection_specifications';
                const params = new URLSearchParams(searchParams);
                
                // 添加分页参数
                params.append('page', currentPage);
                params.append('page_size', pageSize);
                
                // 添加排序参数
                if (sortField) {
                    params.append('sort_field', sortField);
                    params.append('sort_order', sortOrder);
                }
                
                if (params.toString()) {
                    url += '?' + params.toString();
                }
                
                console.log('加载数据，URL：', url);

                const response = await fetch(url);
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }

                const tbody = document.querySelector('#specificationsTable tbody');
                tbody.innerHTML = ''; // 清空现有内容
                
                if (!data.specifications || data.specifications.length === 0) {
                    // 添加无数据提示行
                    const noDataRow = document.createElement('tr');
                    noDataRow.innerHTML = `
                        <td colspan="12" style="text-align: center;">没有找到匹配的数据</td>
                    `;
                    tbody.appendChild(noDataRow);
                    return;
                }
                
                // 使用 data.specifications 更新表格
                data.specifications.forEach((spec, index) => {
                    const row = document.createElement('tr');
                    row.dataset.id = spec.id;
                    row.innerHTML = `
                        <td><input type="checkbox" onchange="toggleRowActions(this)"></td>
                        <td>${(currentPage - 1) * pageSize + index + 1}</td>
                        <td>${spec.unique_id}</td>
                        <td>${spec.is_standard === 'Y' ? 'Y' : 'N'}</td>
                        <td>${spec.material_type || ''}</td>
                        <td>${spec.inspection_item || ''}</td>
                        <td class="col-inspection-method">${spec.inspection_method || ''}</td>
                        <td class="col-inspection-description">${spec.inspection_description || ''}</td>
                        <td>${spec.sampling_level || ''}</td>
                        <td>${spec.cr || ''}</td>
                        <td>${spec.maj || ''}</td>
                        <td>${spec.min || ''}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                // 更新分页信息
                if (data.total_pages) {
                    updatePagination(data.total_pages);
                }
                
                // 更新排序指示器
                updateSortIndicators();

             
                
                // 重置按钮状态
                resetButtons();
                
            
                // 数据加载完成后，强制设置固定列宽度
                setTimeout(() => {
                    const fixedCols = document.querySelectorAll(
                        'th.col-id, th.col-unique-id, th.col-is-standard, th.col-material-type, ' +
                        'th.col-sampling-level, th.col-cr, th.col-maj, th.col-min'
                    );
                    fixedCols.forEach(col => {
                        col.style.width = '80px';
                        col.style.minWidth = '80px';
                        col.style.maxWidth = '80px';
                    });

                    const fixedDataCols = document.querySelectorAll(
                        'td.col-id, td.col-unique-id, td.col-is-standard, td.col-material-type, ' +
                        'td.col-sampling-level, td.col-cr, td.col-maj, td.col-min'
                    );
                    fixedDataCols.forEach(td => {
                        td.style.width = '80px';
                        td.style.minWidth = '80px';
                        td.style.maxWidth = '80px';
                    });
                }, 50);

            } catch (error) {
                console.error('Error loading specifications:', error);
                alert('加载数据失败: ' + error.message);
            }
        }

        function updateTable(specifications) {
            const tableBody = document.querySelector('#specificationsTable tbody');
            tableBody.innerHTML = '';
            
            if (!specifications || specifications.length === 0) {
                const noDataRow = document.createElement('tr');
                noDataRow.innerHTML = `
                    <td colspan="12" style="text-align: center;">没有找到匹配的数据</td>
                `;
                tableBody.appendChild(noDataRow);
                return;
            }
            
            specifications.forEach((spec, index) => {
                const row = document.createElement('tr');
                row.dataset.id = spec.id;
                row.innerHTML = `
                    <td><input type="checkbox" onchange="toggleRowActions(this)"></td>
                    <td>${index + 1}</td>
                    <td>${spec.unique_id}</td>
                    <td>${spec.is_standard === 'Y' ? 'Y' : 'N'}</td>
                    <td>${spec.material_type || ''}</td>
                    <td>${spec.inspection_item || ''}</td>
                    <td class="col-inspection-method">${spec.inspection_method || ''}</td>
                    <td class="col-inspection-description">${spec.inspection_description || ''}</td>
                    <td>${spec.sampling_level || ''}</td>
                    <td>${spec.cr || ''}</td>
                    <td>${spec.maj || ''}</td>
                    <td>${spec.min || ''}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        function updatePagination(totalPages) {
            const paginationContainer = document.getElementById('pagination');
            if (!paginationContainer) {
                const container = document.createElement('div');
                container.id = 'pagination';
                container.style.margin = '20px 0';
                container.style.textAlign = 'center';
                document.querySelector('#specificationsTable').after(container);
            }
            
            const paginationHtml = `
                <button onclick="changePage(${currentPage - 1})" 
                        ${currentPage === 1 ? 'disabled' : ''}>上一页</button>
                <span style="margin: 0 10px;">第 ${currentPage} 页，共 ${totalPages} 页</span>
                <button onclick="changePage(${currentPage + 1})"
                        ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>
                <select name="page_size" onchange="updatePageSize(this.value)" style="margin-left: 10px;">
                    <option value="20" ${pageSize === 20 ? 'selected' : ''}>每页20行</option>
                    <option value="50" ${pageSize === 50 ? 'selected' : ''}>每页50行</option>
                    <option value="100" ${pageSize === 100 ? 'selected' : ''}>每页100行</option>
                    <option value="200" ${pageSize === 200 ? 'selected' : ''}>每页200行</option>
                </select>
            `;
            document.getElementById('pagination').innerHTML = paginationHtml;
        }

        // 添加页面切换函数
        function changePage(newPage) {
            if (newPage < 1) return;
            currentPage = newPage;
            loadSpecifications();
        }

        // 添加排序指示器更新函数
        function updateSortIndicators() {
            // 清除所有排序指示器
            document.querySelectorAll('th[data-sort]').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });
            
            // 添加当前排序指示器
            if (sortField) {
                const currentHeader = document.querySelector(`th[data-sort="${sortField}"]`);
                if (currentHeader) {
                    currentHeader.classList.add(sortOrder.toLowerCase() === 'asc' ? 'sort-asc' : 'sort-desc');
                }
            }
        }

        // 修改表头初始化函数
        function initSortableHeaders() {
            const headers = document.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    const field = header.dataset.sort;
                    if (sortField === field) {
                        sortOrder = sortOrder === 'ASC' ? 'DESC' : 'ASC';
                    } else {
                        sortField = field;
                        sortOrder = 'ASC';
                    }
                    
                    // 重新加载数据
                    const searchParams = new URLSearchParams(window.location.search);
                    loadSpecifications(searchParams.toString());
                });
            });
        }

        // 修改 generateUniqueId 函数
        async function generateUniqueId() {
            try {
                const date = new Date();
                const year = date.getFullYear().toString().slice(-2);
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                
                const prefix = `GIS${year}${month}${day}`;
                
                const response = await fetch(`/api/general_inspection_specifications/max_id?prefix=${prefix}`);
                if (!response.ok) {
                    throw new Error(`获取序号失败 (${response.status})`);
                }
                
                const data = await response.json();
                if (data.error) {
                    throw new Error(data.error);
                }
                
                const nextNumber = (data.max_id || 0) + 1;
                return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
            } catch (error) {
                console.error('Error generating unique ID:', error);
                throw new Error(`生成唯一码失败: ${error.message}`);
            }
        }

        function generateAQLOptions(type, currentValue) {
            const options = {
                cr: ['', '0.01', '0.015', '0.025', '0.04', '0.065', '0.1', '0.15', '0.25', '0.4', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0', 'custom'],
                maj: ['', '0.01', '0.015', '0.025', '0.04', '0.065', '0.1', '0.15', '0.25', '0.4', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0', 'custom'],
                min: ['', '0.01', '0.015', '0.025', '0.04', '0.065', '0.1', '0.15', '0.25', '0.4', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0', 'custom']
            };
            
            return options[type].map(option => {
                let displayText = option;
                if (option === '') displayText = '不启用';
                if (option === 'custom') displayText = '自定义';
                
                // 新增判断逻辑：当当前值存在且不在预定义列表中时，标记为自定义
                const isCustom = currentValue && !options[type].includes(currentValue) && currentValue !== '';
                const selected = (option === currentValue) || (isCustom && option === 'custom') ? 'selected' : '';
                
                return `<option value="${option}" ${selected}>${displayText}</option>`;
            }).join('');
        }

        function generateSamplingLevelOptions(currentLevel) {
            const levels = ['S-1', 'S-2', 'S-3', 'S-4', 'G-I', 'G-II', 'G-III'];
            return levels.map(level => 
                `<option value="${level}" ${level === currentLevel ? 'selected' : ''}>${level}</option>`
            ).join('');
        }

        // 修改 setupAQLAutoFill 函数
        function setupAQLAutoFill(row) {
            const materialTypeInput = row.querySelector('input[data-field="material_type"]');
            const inspectionItemInput = row.querySelector('input[data-field="inspection_item"]');
            
            if (materialTypeInput && inspectionItemInput) {
                const checkAndFill = async () => {
                    const materialType = materialTypeInput.value.trim();
                    const inspectionItem = inspectionItemInput.value.trim();
                    
                    if (materialType && inspectionItem) {
                        try {
                            row.isAQLChecking = true;
                            
                            const response = await fetch(`/api/aql_settings/match?material_type=${encodeURIComponent(materialType)}&inspection_item=${encodeURIComponent(inspectionItem)}`);
                            const data = await response.json();

                            if (data.found) {
                                const samplingSelect = row.querySelector('select[name="sampling_level"]');
                                if (samplingSelect) {
                                    samplingSelect.value = data.sampling_level;
                                }
                                
                                ['cr', 'maj', 'min'].forEach(field => {
                                    const select = row.querySelector(`select[name="${field}"]`);
                                    const input = row.querySelector(`input[name="${field}"]`);
                                    if (select && data[field]) {
                                        if (data[field] === 'custom') {
                                            select.value = 'custom';
                                            if (input) input.value = data[field + '_value'];
                                        } else {
                                            select.value = data[field];
                                        }
                                    }
                                });
                            }
                        } finally {
                            setTimeout(() => {
                                row.isAQLChecking = false;
                            }, 300);
                        }
                    }
                };
                
                materialTypeInput.addEventListener('change', checkAndFill);
                inspectionItemInput.addEventListener('change', checkAndFill);
            }
        }

        // 修改 checkDuplicate 函数
        async function checkDuplicate(input) {
            // 如果正在保存，跳过重复检查
            if (window.isSaving) {
                return false;
            }

            const row = input.closest('tr');
            if (!row) return false;

            // 如果正在进行 AQL 匹配检查，跳过重复检查
            if (row.isAQLChecking) {
                return false;
            }

            const currentValues = {
                material_type: row.querySelector('input[data-field="material_type"]')?.value.trim() || '',
                inspection_item: row.querySelector('input[data-field="inspection_item"]')?.value.trim() || '',
                inspection_method: row.querySelector('input[data-field="inspection_method"]')?.value.trim() || '',
                inspection_description: row.querySelector('input[data-field="inspection_description"]')?.value.trim() || ''
            };

            // 如果是编辑模式且数据没有变化，不进行检查
            if (row.originalData && 
                currentValues.material_type === row.originalData.material_type &&
                currentValues.inspection_item === row.originalData.inspection_item &&
                currentValues.inspection_method === row.originalData.inspection_method &&
                currentValues.inspection_description === row.originalData.inspection_description) {
                return false;
            }

            // 如果必填字段为空，不进行检查
            if (!currentValues.material_type || !currentValues.inspection_item || 
                !currentValues.inspection_method || !currentValues.inspection_description) {
                return false;
            }

            try {
                const params = new URLSearchParams();
                // 使用 encodeURIComponent 确保特殊字符被正确编码
                Object.entries(currentValues).forEach(([key, value]) => {
                    params.append(key, encodeURIComponent(value));
                });
                
                // 添加当前记录ID（如果存在）
                const currentId = row.dataset.editingId || row.dataset.id;
                if (currentId) {
                    params.append('current_id', currentId);
                }

                const response = await fetch(`/api/general_inspection_specifications/check_duplicate?${params}`);
                const data = await response.json();
                
                if (data.status === 'duplicate') {
                    alert(`已存在相同的记录：
物料类型: ${data.duplicate_info.material_type}
检验项目: ${data.duplicate_info.inspection_item}
检验方法: ${data.duplicate_info.inspection_method}
检验说明: ${data.duplicate_info.inspection_description}
唯一码: ${data.duplicate_info.unique_id}`);
                    return true;
                }

                return false;
            } catch (error) {
                console.error('检查重复失败:', error);
                return false;
            }
        }

        // 修改 setupEditFieldValidation 函数中的重复检查部分
        function setupEditFieldValidation(row) {
            const fields = ['material_type', 'inspection_item', 'inspection_method', 'inspection_description'];
            const originalData = row.originalData;
            
            fields.forEach(field => {
                const input = row.querySelector(`input[data-field="${field}"]`);
                if (input) {
                    let timeout;
                    const checkDuplicateHandler = () => {
                        // 如果正在保存，跳过重复检查
                        if (window.isSaving) {
                            return;
                        }

                        clearTimeout(timeout);
                        timeout = setTimeout(async () => {
                            try {
                                const isDuplicate = await checkDuplicate(input);
                                document.getElementById('saveBtn').disabled = isDuplicate;
                            } catch (error) {
                                console.error('Error checking duplicate:', error);
                                document.getElementById('saveBtn').disabled = false;
                            }
                        }, 300);
                    };
                    
                    // 移除之前的事件监听器（如果存在）
                    if (input.checkDuplicateHandler) {
                        input.removeEventListener('blur', input.checkDuplicateHandler);
                    }
                    
                    // 添加新的事件监听器
                    input.addEventListener('blur', checkDuplicateHandler);
                    input.checkDuplicateHandler = checkDuplicateHandler;
                }
            });
        }

        // 修改 saveEdit 函数中的表单数据构建部分
        async function saveEdit() {
            // 查找带有 editing 类的行
            const editingRow = document.querySelector('tbody tr.editing');
            if (!editingRow) {
                alert('请选择一行进行编辑');
                return;
            }

            const id = editingRow.dataset.id;

            window.isSaving = true;

            try {
                const formData = {
                    is_standard: editingRow.querySelector('input[name="is_standard"]').checked ? 'Y' : 'N',
                    material_type: editingRow.querySelector('input[data-field="material_type"]').value.trim(),
                    inspection_item: editingRow.querySelector('input[data-field="inspection_item"]').value.trim(),
                    inspection_method: editingRow.querySelector('input[data-field="inspection_method"]').value.trim(),
                    inspection_description: editingRow.querySelector('input[data-field="inspection_description"]').value.trim(),
                    sampling_level: editingRow.querySelector('select[name="sampling_level"]').value,
                    cr: getAQLValue(editingRow, 'cr'),  // 确保获取自定义AQL值
                    maj: getAQLValue(editingRow, 'maj'), // 确保获取自定义AQL值
                    min: getAQLValue(editingRow, 'min')  // 确保获取自定义AQL值
                };

                const hasChanged = Object.keys(formData).some(key => 
                    formData[key] !== editingRow.originalData[key]
                );

                if (hasChanged) {
                    const params = new URLSearchParams({
                        ...formData,
                        current_id: id
                    });

                    const response = await fetch(`/api/general_inspection_specifications/check_duplicate?${params}`);
                    const data = await response.json();
                    
                    if (data.status === 'duplicate') {
                        alert(`已存在相同的记录：
        物料类型: ${data.duplicate_info.material_type}
        检验项目: ${data.duplicate_info.inspection_item}
        检验方法: ${data.duplicate_info.inspection_method}
        检验说明: ${data.duplicate_info.inspection_description}
        唯一码: ${data.duplicate_info.unique_id}`);
                        return;
                    }
                }

                const response = await fetch(`/api/general_inspection_specifications/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                if (data.message === '更新成功') {
                    await loadSpecifications(); // 强制重新加载数据
                    resetEditState();
                } else {
                    alert('更新失败: ' + data.message);
                }
            } catch (error) {
                console.error('Error updating specification:', error);
                alert('更新失败');
            } finally {
                window.isSaving = false;
            }
        }

        function resetEditState() {
            document.getElementById('addBtn').style.display = 'inline';
            document.getElementById('manageBtn').style.display = 'inline';
            document.getElementById('deleteBtn').style.display = 'inline';
            document.getElementById('saveBtn').style.display = 'none';
            document.getElementById('cancelBtn').style.display = 'none';
            
            // 启用所有复选框
            document.querySelectorAll('tbody input[type="checkbox"]').forEach(cb => {
                cb.disabled = false;
                cb.checked = false;
            });
            
            document.getElementById('manageBtn').disabled = true;
            document.getElementById('deleteBtn').disabled = true;
        }

        function addSearchField() {
            const container = document.getElementById('search-container');
            const searchFields = container.querySelectorAll('.search-field');
            
            // 检查是否已达到最大搜索行数（5行）
            if (searchFields.length >= 5) {
                alert('最多只能添加5个搜索条件');
                return;
            }

            const newField = document.createElement('div');
            newField.className = 'search-field';
            newField.innerHTML = `
                <select name="search_field" style="width: 120px;">
                    <option value="material_type">物料类型</option>
                    <option value="is_standard">通用规范</option>
                    <option value="inspection_item">检验项目</option>
                    <option value="inspection_method">检验方法</option>
                    <option value="inspection_description">检验说明</option>
                    <option value="cr">CR</option>
                    <option value="maj">MAJ</option>
                    <option value="min">MIN</option>
                </select>
                <input type="text" name="search_value" style="width: 200px;" 
                       placeholder="请输入搜索内容..." 
                       onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
                ${searchFields.length < 4 ? '<button type="button" class="add-search-btn" onclick="addSearchField()">+</button>' : ''}
                ${searchFields.length > 0 ? '<button type="button" class="remove-search-btn" onclick="removeSearchField(this)">-</button>' : ''}
            `;
            
            container.appendChild(newField);
            
            // 确保第一行始终显示 [+] [搜索]
            const firstField = container.firstElementChild;
            if (firstField) {
                firstField.innerHTML = `
                    <select name="search_field" style="width: 120px;">
                        <option value="material_type">物料类型</option>
                        <option value="is_standard">通用规范</option>
                        <option value="inspection_item">检验项目</option>
                        <option value="inspection_method">检验方法</option>
                        <option value="inspection_description">检验说明</option>
                        <option value="cr">CR</option>
                        <option value="maj">MAJ</option>
                        <option value="min">MIN</option>
                    </select>
                    <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容..." 
                           onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
                    <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                    <button type="button" class="search-btn" onclick="performSearch()">搜索</button>
                `;
            }
        }

        function removeSearchField(button) {
            const container = document.getElementById('search-container');
            const searchFields = container.querySelectorAll('.search-field');
            const fieldToRemove = button.closest('.search-field');
            
            fieldToRemove.remove();
            
            // 确保第一行始终显示 [+] [搜索]
            const firstField = container.firstElementChild;
            if (firstField) {
                firstField.innerHTML = `
                    <select name="search_field" style="width: 120px;">
                        <option value="material_type">物料类型</option>
                        <option value="is_standard">通用规范</option>
                        <option value="inspection_item">检验项目</option>
                        <option value="inspection_method">检验方法</option>
                        <option value="inspection_description">检验说明</option>
                        <option value="cr">CR</option>
                        <option value="maj">MAJ</option>
                        <option value="min">MIN</option>
                    </select>
                    <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容..." 
                           onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
                    <button type="button" class="add-search-btn" onclick="addSearchField()">+</button>
                    <button type="button" class="search-btn" onclick="performSearch()">搜索</button>
                `;
            }
            
            // 更新后续行的按钮显示
            searchFields.forEach((field, index) => {
                if (index === 0) return; // 跳过第一行
                field.innerHTML = `
                    <select name="search_field" style="width: 120px;">
                        <option value="material_type">物料类型</option>
                        <option value="is_standard">通用规范</option>
                        <option value="inspection_item">检验项目</option>
                        <option value="inspection_method">检验方法</option>
                        <option value="inspection_description">检验说明</option>
                        <option value="cr">CR</option>
                        <option value="maj">MAJ</option>
                        <option value="min">MIN</option>
                    </select>
                    <input type="text" name="search_value" style="width: 200px;" placeholder="请输入搜索内容..." 
                           onkeydown="if(event.keyCode === 13) { event.preventDefault(); performSearch(); }">
                    ${index < 4 ? '<button type="button" class="add-search-btn" onclick="addSearchField()">+</button>' : ''}
                    <button type="button" class="remove-search-btn" onclick="removeSearchField(this)">-</button>
                `;
            });
        }

        function performSearch() {
            const searchFields = document.querySelectorAll('.search-field select[name="search_field"]');
            const searchValues = document.querySelectorAll('.search-field input[name="search_value"]');
            const searchParams = new URLSearchParams();
            
            let hasValidSearch = false;
            
            for (let i = 0; i < searchFields.length; i++) {
                const field = searchFields[i].value;
                const value = searchValues[i].value.trim();
                if (value) {
                    hasValidSearch = true;
                    searchParams.append('search_field[]', field);
                    searchParams.append('search_value[]', value);
                }
            }
            
            // 重置分页到第一页
            currentPage = 1;
            
            if (!hasValidSearch) {
                loadSpecifications();
                return;
            }

            // 执行搜索
            loadSpecifications(searchParams.toString());
        }

        function toggleAllRows(checkbox) {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
                toggleRowActions(cb);
            });
        }

        function toggleRowActions(checkbox) {
            const manageBtn = document.getElementById('manageBtn');
            const deleteBtn = document.getElementById('deleteBtn');
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            
            manageBtn.disabled = checkedBoxes.length !== 1; // 只有选中一行时才能启用管理按钮
            deleteBtn.disabled = checkedBoxes.length === 0; // 只要有选中行就启用删除按钮
        }

        function editSelected() {
            const checkedRow = document.querySelector('tbody input[type="checkbox"]:checked');
            if (checkedRow) {
                const id = checkedRow.closest('tr').dataset.id;
                editSpecification(id);
            }
        }

        function deleteSelected() {
            const checkedRows = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkedRows.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checkedRows.length} 条记录吗？`)) {
                const deletePromises = Array.from(checkedRows).map(checkbox => {
                    const id = checkbox.closest('tr').dataset.id;
                    return fetch(`/api/general_inspection_specifications/${id}`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`删除失败: ${response.statusText}`);
                        }
                        return response.json();
                    });
                });

                Promise.all(deletePromises)
                    .then(() => {
                        loadSpecifications();
                        resetEditState();
                    })
                    .catch(error => {
                        console.error('Error deleting specifications:', error);
                        alert('删除失败: ' + error.message);
                    });
            }
        }

        function validateUniqueId(uniqueId) {
            // 检查格式: GIS + 6位日期 + 3位序号
            const regex = /^GIS\d{6}\d{3}$/;
            if (!regex.test(uniqueId)) {
                alert('唯一码格式不正确！应为: GIS + 年月日 + 3位序号，例如: GIS250218001');
                return false;
            }
            return true;
        }

        function validateAQLValues() {
            const cr = parseFloat(document.querySelector('tbody input[name="cr"]').value);
            const maj = parseFloat(document.querySelector('tbody input[name="maj"]').value);
            const min = parseFloat(document.querySelector('tbody input[name="min"]').value);
            
            if (cr < 0 || cr > 100 || maj < 0 || maj > 100 || min < 0 || min > 100) {
                alert('AQL值必须在0-100之间');
                return false;
            }
            return true;
        }

        // 添加自动填充 AQL 值的函数
        function autoFillAQLValues(materialType, inspectionItem) {
            if (!materialType || !inspectionItem) return;
            
            fetch(`/api/aql_settings/match?material_type=${encodeURIComponent(materialType)}&inspection_item=${encodeURIComponent(inspectionItem)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.found) {
                        // 找到匹配的 AQL 设置，自动填充值
                        const row = document.querySelector('tr[data-id="new"]') || 
                                   document.querySelector('tbody input[type="checkbox"]:checked').closest('tr');
                        
                        if (row) {
                            // 更新抽样水准
                            const samplingSelect = row.querySelector('select[name="sampling_level"]');
                            if (samplingSelect) {
                                samplingSelect.value = data.sampling_level;
                            }
                            
                            // 更新 CR, MAJ, MIN 值
                            ['cr', 'maj', 'min'].forEach(field => {
                                const select = row.querySelector(`select[name="${field}"]`);
                                const input = row.querySelector(`input[name="${field}"]`);
                                if (select && data[field]) {
                                    if (data[field] === 'custom') {
                                        select.value = 'custom';
                                        if (input) input.value = data[field + '_value'];
                                    } else {
                                        select.value = data[field];
                                    }
                                }
                            });
                        }
                    }
                })
                .catch(error => console.error('Error fetching AQL settings:', error));
        }

        // 添加 resetButtons 函数
        function resetButtons() {
            // 显示主要按钮
            document.getElementById('addBtn').style.display = 'inline';
            document.getElementById('manageBtn').style.display = 'inline';
            document.getElementById('deleteBtn').style.display = 'inline';
            
            // 隐藏所有操作按钮
            document.getElementById('saveBtn').style.display = 'none';
            document.getElementById('saveNewBtn').style.display = 'none';
            document.getElementById('cancelBtn').style.display = 'none';
            
            // 重置按钮状态
            document.getElementById('manageBtn').disabled = true;
            document.getElementById('deleteBtn').disabled = true;
            
            // 启用所有复选框
            document.querySelectorAll('tbody input[type="checkbox"]').forEach(cb => {
                cb.disabled = false;
                cb.checked = false;
            });
            
            // 移除新行（如果存在）
            const newRow = document.querySelector('tr[data-id="new"]');
            if (newRow) {
                newRow.remove();
            }
            const tableContainer = document.querySelector('.table-container');
            tableContainer.style.width = '100%';
        }

        // 添加 cancelEdit 函数
        function cancelEdit() {
            loadSpecifications();
            resetButtons();
        }

        // 添加 showAddRow 函数
        async function showAddRow() {
            // 隐藏主要按钮，显示保存和取消按钮
            document.getElementById('addBtn').style.display = 'none';
            document.getElementById('manageBtn').style.display = 'none';
            document.getElementById('deleteBtn').style.display = 'none';
            document.getElementById('saveNewBtn').style.display = 'inline';
            document.getElementById('cancelBtn').style.display = 'inline';

            // 获取今天的日期，格式为 yyMMdd
            const today = new Date();
            const prefix = `GIS${today.getFullYear().toString().slice(-2)}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`;

            // 获取当前最大序号
            try {
                const response = await fetch(`/api/general_inspection_specifications/max_id?prefix=${prefix}`);
                const data = await response.json();
                const nextId = (data.max_id + 1).toString().padStart(3, '0');
                const uniqueId = `${prefix}${nextId}`;

                // 创建新行
                const tbody = document.querySelector('#specificationsTable tbody');
                const newRow = document.createElement('tr');
                newRow.dataset.id = 'new';
                newRow.innerHTML = `
                    <td><input type="checkbox" disabled></td>
                    <td>新</td>
                    <td>${uniqueId}</td>
                    <td>
                        <label class="custom-checkbox">
                            <input type="checkbox" name="is_standard">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><input type="text" data-field="material_type" required></td>
                    <td><input type="text" data-field="inspection_item" required></td>
                    <td><input type="text" data-field="inspection_method" required></td>
                    <td><input type="text" data-field="inspection_description" required></td>
                    <td>
                        <select name="sampling_level" class="sampling-select">
                            ${generateSamplingLevelOptions('')}
                        </select>
                    </td>
                    <td>
                        <select name="cr" class="aql-select" onchange="toggleCustomAQL(this)">
                            ${generateAQLOptions('cr', '')}
                        </select>
                        <input type="number" class="custom-aql" style="display: none" 
                            step="0.001" min="0" max="100">
                    </td>
                    <td>
                        <select name="maj" class="aql-select" onchange="toggleCustomAQL(this)">
                            ${generateAQLOptions('maj', '')}
                        </select>
                        <input type="number" class="custom-aql" style="display: none" 
                            step="0.001" min="0" max="100">
                    </td>
                    <td>
                        <select name="min" class="aql-select" onchange="toggleCustomAQL(this)">
                            ${generateAQLOptions('min', '')}
                        </select>
                        <input type="number" class="custom-aql" style="display: none" 
                            step="0.001" min="0" max="100">
                    </td>
                `;

                // 将新行插入到表格的开头
                if (tbody.firstChild) {
                    tbody.insertBefore(newRow, tbody.firstChild);
                } else {
                    tbody.appendChild(newRow);
                }

                // 设置字段验证
                setupFieldValidation(newRow);

                // 设置 AQL 自动填充
                setupAQLAutoFill(newRow);

                // 调整滚动位置，确保新行完全可见
                const tableContainer = document.querySelector('.table-container');
                const headerHeight = document.querySelector('thead').offsetHeight;
                tableContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });

            } catch (error) {
                console.error('Error getting max ID:', error);
                alert('添加新行失败: ' + error.message);
            }
        }

        // 添加 setupFieldValidation 函数
        function setupFieldValidation(row) {
            const fields = ['material_type', 'inspection_item', 'inspection_method', 'inspection_description'];
            
            fields.forEach(field => {
                const input = row.querySelector(`input[data-field="${field}"]`);
                if (input) {
                    // 使用防抖处理重复检查
                    let timeout;
                    const checkDuplicateHandler = async () => {
                        clearTimeout(timeout);
                        timeout = setTimeout(async () => {
                            // 如果正在进行 AQL 匹配，不进行重复检查
                            if (row.isAQLChecking) {
                                return;
                            }
                            
                            const isDuplicate = await checkDuplicate(input);
                            if (isDuplicate) {
                                document.getElementById('saveNewBtn').disabled = true;
                            } else {
                                document.getElementById('saveNewBtn').disabled = false;
                            }
                        }, 300);
                    };
                    
                    // 添加失去焦点事件监听器
                    input.addEventListener('blur', checkDuplicateHandler);
                    // 保存事件处理函数引用，以便后续可以移除
                    input.checkDuplicateHandler = checkDuplicateHandler;
                }
            });
        }

        // 添加 saveNewRow 函数
        async function saveNewRow() {
            const newRow = document.querySelector('tr[data-id="new"]');
            if (!newRow) return;
            
            window.isSaving = true;

            try {
                // 修改：使用新方法获取AQL值
                const getAQLValue = (name) => {
                    const select = newRow.querySelector(`select[name="${name}"]`);
                    const input = newRow.querySelector(`input[name="${name}"].custom-aql`);
                    if (select && select.value === 'custom' && input) {
                        return input.value.trim(); // 直接获取输入框的原始值
                    }
                    return select ? select.value : '';
                };

                const formData = {
                    unique_id: newRow.cells[2].textContent,
                    is_standard: newRow.querySelector('input[name="is_standard"]').checked ? 'Y' : 'N',
                    material_type: newRow.querySelector('input[data-field="material_type"]').value.trim(),
                    inspection_item: newRow.querySelector('input[data-field="inspection_item"]').value.trim(),
                    inspection_method: newRow.querySelector('input[data-field="inspection_method"]').value.trim(),
                    inspection_description: newRow.querySelector('input[data-field="inspection_description"]').value.trim(),
                    sampling_level: newRow.querySelector('select[name="sampling_level"]').value,
                    // 修改：使用新的AQL值获取方法
                    cr: getAQLValue('cr'),
                    maj: getAQLValue('maj'),
                    min: getAQLValue('min'),
                    is_custom_cr: newRow.querySelector('select[name="cr"]').value === 'custom',
                    is_custom_maj: newRow.querySelector('select[name="maj"]').value === 'custom',
                    is_custom_min: newRow.querySelector('select[name="min"]').value === 'custom'
                };

                const requiredFields = ['material_type', 'inspection_item', 'inspection_method', 'inspection_description'];
                const missingFields = requiredFields.filter(field => !formData[field]);
                
                if (missingFields.length > 0) {
                    alert('请填写所有必填字段：' + missingFields.join(', '));
                    return;
                }

                const params = new URLSearchParams({
                    material_type: formData.material_type,
                    inspection_item: formData.inspection_item,
                    inspection_method: formData.inspection_method,
                    inspection_description: formData.inspection_description
                });

                const checkResponse = await fetch(`/api/general_inspection_specifications/check_duplicate?${params}`);
                const checkData = await checkResponse.json();
                
                if (checkData.status === 'duplicate') {
                    alert(`已存在相同的记录：
        物料类型: ${checkData.duplicate_info.material_type}
        检验项目: ${checkData.duplicate_info.inspection_item}
        检验方法: ${checkData.duplicate_info.inspection_method}
        检验说明: ${checkData.duplicate_info.inspection_description}
        唯一码: ${checkData.duplicate_info.unique_id}`);
                    return;
                }

                const response = await fetch('/api/general_inspection_specifications', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                
                if (data.status === 'success') {
                    alert('保存成功！新记录的唯一码为：' + data.unique_id);
                    await loadSpecifications();
                    resetButtons();
                } else {
                    throw new Error(data.message || '保存失败');
                }
            } catch (error) {
                console.error('保存失败:', error);
                alert(`保存失败: ${error.message}`);
            } finally {
                window.isSaving = false;
            }
        }

        // 确保 getAQLValue 函数也被定义
        function getAQLValue(row, name) {
            const select = row.querySelector(`select[name="${name}"]`);
            if (!select) return '';
            
            if (select.value === 'custom') {
                const customInput = row.querySelector(`input[name="${name}"].custom-aql`);
                // 直接返回原始输入值（保留小数点后的0）
                return customInput ? customInput.value.trim() : '';
            }
            // 返回下拉选项的原始值（包括字符串格式的浮点数）
            return select.value;
        }

        // 添加 startEdit 函数
        function startEdit() {
            const checkedRow = document.querySelector('tbody input[type="checkbox"]:checked').closest('tr');
            if (!checkedRow) {
                alert('请选择一行进行编辑');
                return;
            }

            const cells = checkedRow.cells;

            // 获取自定义AQL值
            const getCustomAQL = (cell) => {
                const select = cell.querySelector('select');
                const input = cell.querySelector('input.custom-aql');
                return select && select.value === 'custom' && input ? input.value : '';
            };

            // 修改：确保正确获取和保存原始数据，包括自定义AQL值
            const originalData = {
                id: checkedRow.dataset.id,
                unique_id: cells[2].textContent.trim(),
                is_standard: cells[3].textContent.trim() === 'Y',
                material_type: cells[4].textContent.trim(),
                inspection_item: cells[5].textContent.trim(),
                inspection_method: cells[6].textContent.trim(),
                inspection_description: cells[7].textContent.trim(),
                sampling_level: cells[8].textContent.trim(),
                cr: getCustomAQL(cells[9]) || cells[9].textContent.trim(),
                maj: getCustomAQL(cells[10]) || cells[10].textContent.trim(),
                min: getCustomAQL(cells[11]) || cells[11].textContent.trim()
            };

            // 使用 Object.defineProperty 确保 originalData 不会被意外修改
            Object.defineProperty(checkedRow, 'originalData', {
                value: originalData,
                writable: false,
                configurable: true
            });

            // 保存当前行的ID，用于重复检查
            checkedRow.dataset.editingId = checkedRow.dataset.id;

            // 记录当前编辑的行
            checkedRow.classList.add('editing');

            checkedRow.innerHTML = `
                <td><input type="checkbox" checked disabled></td>
                <td>${originalData.id}</td>
                <td>${originalData.unique_id}</td>
                <td>
                    <label class="custom-checkbox">
                        <input type="checkbox" name="is_standard" ${originalData.is_standard ? 'checked' : ''}>
                        <span class="checkmark"></span>
                    </label>
                </td>
                <td><input type="text" data-field="material_type" value="${originalData.material_type}" required class="editable-input"></td>
                <td><input type="text" data-field="inspection_item" value="${originalData.inspection_item}" required class="editable-input"></td>
                <td><input type="text" data-field="inspection_method" value="${originalData.inspection_method}" required class="editable-input"></td>
                <td><input type="text" data-field="inspection_description" value="${originalData.inspection_description}" required class="editable-input"></td>
                <td>
                    <select name="sampling_level" class="sampling-select editable-select">
                        ${generateSamplingLevelOptions(originalData.sampling_level)}
                    </select>
                </td>
                  <td>
                    <select name="cr" class="aql-select editable-select" onchange="toggleCustomAQL(this)">
                        ${generateAQLOptions('cr', originalData.cr)}
                    </select>
                    <input type="number" name="cr" class="custom-aql editable-input" 
                        style="display: ${originalData.cr && !['', '0.01', '0.015', '0.025', '0.04', '0.065', '0.1', '0.15', '0.25', '0.4', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0'].includes(originalData.cr) ? 'inline' : 'none'}" 
                        step="0.001" min="0" max="100" 
                        value="${originalData.cr.replace(/\.0+$/, '')}"> <!-- 移除重复的value定义 -->
                </td>
                <td>
                    <select name="maj" class="aql-select editable-select" onchange="toggleCustomAQL(this)">
                        ${generateAQLOptions('maj', originalData.maj)}
                    </select>
                    <input type="number" name="maj" class="custom-aql editable-input" 
                        style="display: ${originalData.maj && !['', '0.01', '0.015', '0.025', '0.04', '0.065', '0.1', '0.15', '0.25', '0.4', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0'].includes(originalData.maj) ? 'inline' : 'none'}" 
                        step="0.001" min="0" max="100" 
                        value="${originalData.maj.replace(/\.0+$/, '')}"> <!-- 移除重复的value定义 -->
                </td>
                <td>
                    <select name="min" class="aql-select editable-select" onchange="toggleCustomAQL(this)">
                        ${generateAQLOptions('min', originalData.min)}
                    </select>
                    <input type="number" name="min" class="custom-aql editable-input" 
                        style="display: ${originalData.min && !['', '0.01', '0.015', '0.025', '0.04', '0.065', '0.1', '0.15', '0.25', '0.4', '0.65', '1.0', '1.5', '2.5', '4.0', '6.5', '10.0'].includes(originalData.min) ? 'inline' : 'none'}" 
                        step="0.001" min="0" max="100" 
                        value="${originalData.min.replace(/\.0+$/, '')}"> <!-- 移除重复的value定义 -->
                </td>
            `;

            // 为所有输入框和选择框添加样式和事件
            const editableElements = checkedRow.querySelectorAll('.editable-input, .editable-select');
            editableElements.forEach(element => {
                // 添加基础样式
                element.style.width = '100%';
                element.style.boxSizing = 'border-box';
                element.style.padding = '4px';
                element.style.border = '1px solid #ddd';
                element.style.borderRadius = '4px';
                element.style.backgroundColor = 'white';

                // 确保元素可以接收输入
                element.style.pointerEvents = 'auto';
                element.style.cursor = element.tagName === 'SELECT' ? 'pointer' : 'text';
                
                // 添加事件监听器
                element.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
                
                element.addEventListener('focus', (e) => {
                    e.target.style.outline = '2px solid #007bff';
                    e.target.style.borderColor = '#007bff';
                    if (element.tagName === 'INPUT') {
                        e.target.select();
                    }
                });
                
                element.addEventListener('blur', (e) => {
                    e.target.style.outline = 'none';
                    e.target.style.borderColor = '#ddd';
                });
            });

            // 设置字段验证
            setupEditFieldValidation(checkedRow);
            
            // 设置 AQL 自动填充
            setupAQLAutoFill(checkedRow);

            // 显示保存和取消按钮
            document.getElementById('saveBtn').style.display = 'inline';
            document.getElementById('cancelBtn').style.display = 'inline';
            document.getElementById('addBtn').style.display = 'none';
            document.getElementById('manageBtn').style.display = 'none';
            document.getElementById('deleteBtn').style.display = 'none';
        }

        document.addEventListener('DOMContentLoaded', function() {
            initSortableHeaders();
            loadSpecifications();
            initColumnResize(); // 初始化列拖动
        });

        
        function toggleCustomAQL(select) {
            const customInput = select.nextElementSibling;
            customInput.style.display = select.value === 'custom' ? 'inline' : 'none';
            if (select.value === 'custom') {
                customInput.value = '';
                customInput.focus();
            } else {
                // 修改：当下拉选择非自定义时，将值同步到输入框
                const selectedValue = select.value;
                if (selectedValue && !isNaN(selectedValue)) {
                    customInput.value = parseFloat(selectedValue).toString();
                }
            }
        }
        
        let isResizing = false; // 默认未处于拖动状态


        function initColumnResize() {
            const resizeLine = document.createElement('div');
            resizeLine.className = 'resize-line';
            document.body.appendChild(resizeLine);

            // 需要拖动的列
            const draggableSelectors = [
                '.col-inspection-item',    // 检验项目
                '.col-inspection-method',  // 检验方法
                '.col-inspection-description', // 检验说明
                '.col-material-type'       // 添加物料类型列
            ];

            draggableSelectors.forEach(selector => {
                const headers = document.querySelectorAll(`#specificationsTable th${selector}`);
                headers.forEach(header => {
                    // 确保不包含需要锁定的列
                    if (header.classList.contains('col-checkbox') || 
                        header.classList.contains('col-id') ||
                        header.classList.contains('col-unique-id') ||
                        header.classList.contains('col-is-standard') ||
                        header.classList.contains('col-sampling-level') || 
                        header.classList.contains('col-cr') || 
                        header.classList.contains('col-maj') || 
                        header.classList.contains('col-min')) return;

                    const handle = document.createElement('div');
                    handle.className = 'resize-handle';
                    header.appendChild(handle);

                    let startX = 0;
                    let startWidth = 0;
                    let isResizing = false; // 局部标志位

                    handle.addEventListener('mousedown', (e) => {
                        isResizing = true; // 开始拖动
                        startX = e.clientX;
                        startWidth = header.offsetWidth;
                        resizeLine.style.display = 'block';
                        resizeLine.style.left = `${header.getBoundingClientRect().right}px`;
                        e.preventDefault();
                    });

                    document.addEventListener('mousemove', (e) => {
                        if (!isResizing) return;
                        const deltaX = e.clientX - startX;
                        const minWidth = 60;
                        const maxWidth = 600;
                        const newWidth = Math.min(maxWidth, Math.max(minWidth, startWidth + deltaX));
                        resizeLine.style.left = `${startX + deltaX}px`;
                    });

                    document.addEventListener('mouseup', (e) => {
                        if (!isResizing) return;
                        isResizing = false; // 结束拖动
                        resizeLine.style.display = 'none';

                        const finalWidth = Math.min(600, Math.max(60, startWidth + (e.clientX - startX)));
                        header.style.width = `${finalWidth}px`;
                        header.style.minWidth = `${finalWidth}px`;
                        header.style.maxWidth = `${finalWidth}px`;

                        const colIndex = Array.from(header.parentElement.children).indexOf(header);
                        document.querySelectorAll(`#specificationsTable td:nth-child(${colIndex + 1})`)
                            .forEach(td => {
                                td.style.width = `${finalWidth}px`;
                                td.style.minWidth = `${finalWidth}px`;
                                td.style.maxWidth = `${finalWidth}px`;
                            });

                        // 在拖动结束后，将表格容器的宽度设置为 auto
                        const tableContainer = document.querySelector('.table-container');
                        tableContainer.style.width = 'auto';
                    });
                });
            });
        }
        
    </script>

</body>
</html>