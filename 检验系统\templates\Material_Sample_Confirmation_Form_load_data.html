<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>物料样板确认书</title>
    <style>
        .image-upload-cell 
        .image-upload-box{
            height: 120px;
            position: relative;
        }
        body {
            width: 1000px;  /* 根据实际需求调整 */
            margin: 0 auto;
            font-family: "Microsoft YaHei"; 
            font-size: 12px;
        }
        table {
            table-layout: fixed;  /* 固定表格布局 */
            width: 100%;
            max-width: 1000px;
            border-collapse: collapse;
            border: 2px solid #000000;
            border-spacing: 0; /* 显式声明 */
        }
        td input[type="text"] {
            width: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
            height: 100%;
            border: none;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border: 2px solid #000000;
            border-radius: 5px;
        }
        td input[type="text"] {
            height: 100%;
            box-sizing: border-box;
            padding: 4px 3px;
            margin: 0;
            display: block;
        }
        td input[type="text"]:focus {
            outline: 2px solid #000000;
            border-radius: 5px;
        }
        td {
            border: 1px solid #000000;
            padding: 2px 1px;
            vertical-align: middle;
            line-height: 1.2;
        }
        .main-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            background: #D9D9D9;
            padding: 8px 0;
        }
        .module-title {
            background: #F2F2F2;
            font-weight: bold;
            padding-left: 4px;
        }
        .sub-header {
            background: #E6E6E6;
        }
        .col-no { width: 35px; }
        .col-pos { width: 90px; }
        .col-value { width: 55px; }
        .col-measure { width: 35px; }
        .col-check { width: 70px; }
        .col-note { width: 100px; }
        .align-center { text-align: center; }
        .align-left { text-align: left; padding-left: 4px; }
        .check-box {
            width: 14px;
            height: 14px;
            border: 1px solid #000;
            display: inline-block;
            margin: 0 5px;
            vertical-align: middle;
        }
        input[type="checkbox"] {
            margin: 0 3px 0 10px;
        }

        .custom-row-height {
            width: 100%; 
            border-collapse: collapse;
            table-layout: fixed;
            border: none;
            border-spacing: 0; /* 显式声明 */
            height: 10px; /* 根据需要调整高度 */
        }

        .custom-row-height td {
            height: 10px; /* 确保单元格的高度与行高一致 */
            vertical-align: middle;
        }
        textarea {
            /* border: none !important; /* 移除边框 
            resize: none !important;; 
            overflow: auto !important;;
            outline: none !important;;*/
            width: 100%; /* 输入框宽度与列宽一致 */
            height: auto; /* 自动调整高度 */
            min-height: 50px; /* 最小高度 */
            max-height: 200px; /* 最大高度 */
            padding: 4px 3px; /* 与td padding保持一致 */
            margin: 0;
            font-size: 13px;
            box-sizing: border-box !important;; /* 包含内边距和边框 */
            border: 1px solid transparent!important;; /* 默认无边框 */
            resize: vertical!important;; /* 允许垂直调整大小 */
            overflow-y: auto!important;; /* 允许垂直滚动 */
            outline: none!important;; /* 移除焦点时的轮廓 */
        }
        textarea:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        textarea[name^="appearance_"][name$="_note"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important;
        }

        textarea[name^="appearance_"][name$="_note"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }       

        textarea[name^="function_"][name$="_note"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important;
        }

        textarea[name^="function_"][name$="_note"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        textarea[name^="function_"][name$="_other"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 58px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important; /* 移除边框 */;
        }

        textarea[name^="function_"][name$="_other"]:focus {
            border: 3px solid #000000!important;; /* 焦点时的边框 */
            outline: none!important;; /* 移除焦点时的轮廓 */
            border-radius: 5px;
        }

        input[name="review"] {
            height: text-height; /* 根据文本内容高度自动调整 */
            min-height: 50px; /* 最小高度 */
            max-height: 100px; /* 最大高度 */
            overflow-y: auto; /* 允许垂直滚动 */
            resize: vertical; /* 允许垂直调整大小 */
            border: none!important; /* 移除边框 */
            border-radius: 5px;
        }
        .checkbox-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .col-compliance {
            width: 100px;  /* 根据需求调整具体数值 */
            text-align: center;
        }
        label {
            margin-left: 1px; /* 文字与复选框的间距 */
            cursor: pointer; /* 鼠标悬停时显示手型 */
            user-select: none; /* 禁止文字被选中 */
        }
        /* 图片上传样式 */
        .image-upload-box {
            position: relative;
            width: 100%;
            height: 80px;
            border: 2px dashed #ccc;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .image-upload-box:hover {
            border-color: #999;
            background-color: rgba(0, 0, 0, 0.02);
        }

        .image-upload-box.drag-over {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .image-upload-box.active-hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
        }

        .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            display: none;
            z-index: 2;
            opacity: 0.5; /* 默认半透明 */
            transition: opacity 0.2s ease-in-out;
        }

        .preview-image {
            width: 100%;
            height: 100%; /* 根据图片比例自动调整 */
            object-fit: contain; /* 保持图片比例 */
            position: absolute;
            top: 0;
            left: 0;
        }

        .upload-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            white-space: nowrap;
            font-size: 12px;
            text-align: center;
            width: 90%;
        }

        .upload-shortcut {
            display: block;
            font-size: 10px;
            color: #999;
            margin-top: 4px;
        }

        .image-upload-box.uploading .upload-overlay {
            content: '上传中...';
        }

        .image-note {
            width: 100%;
            height: 20px;
            margin-top: 2px;
            font-size: 12px;
        }

        input[type="file"] {
            display: none;
        }
        .image-upload-box.uploading::after {
            content: "处理中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 14px;
            z-index: 6;
        }
        /* 可选：保持复选框样式 */
        input[name="final_judgment"] {
            display: none;
        }
        input[name="final_judgment"] + label {
            padding: 4px 8px;
            border: 1px solid #999;
            border-radius: 4px;
            cursor: pointer;
        }
        input[name="final_judgment"]:checked + label {
            background-color: #007bff;
            color: white;
        }
 
        .image-upload-box.has-image .delete-btn {
            display: block; /* 有图片时始终显示按钮，但半透明 */
        }

        .image-upload-box.has-image:hover .delete-btn {
            opacity: 1; /* 鼠标悬停时完全不透明 */
        }

        .delete-btn:hover {
            opacity: 1; /* 鼠标悬停在按钮上时完全不透明 */
        }

        .feedback-message {
            position: fixed;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 4px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
            min-width: 200px;
            max-width: 80%;
        }

        .feedback-message.show {
            opacity: 1;
        }

        /* 添加图片压缩功能 */
        .image-upload-box.uploading::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 5;
        }

        .image-upload-box.uploading::after {
            content: "处理中...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 14px;
            z-index: 6;
        }

        .paste-button {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: rgba(0, 123, 255, 0.7);
            color: white;
            border: none;
            border-radius: 3px;
            padding: 3px 6px;
            font-size: 10px;
            cursor: pointer;
            z-index: 3;
            opacity: 0.7;
            display: none;
            transition: all 0.2s ease;
        }

        .image-upload-box:hover .paste-button {
            display: block;
        }

        .image-upload-box.has-image .paste-button {
            bottom: 28px; /* 调整位置，避免与删除按钮重叠 */
        }

        .paste-button:hover {
            opacity: 1;
            background: rgba(0, 123, 255, 0.9);
        }
        /* 强制显示尺寸1-3 */
        #size-row-1, #size-row-2, #size-row-3 {
            display: table-row !important;
        }
        /* 图片放大模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            cursor: zoom-in; /* 初始光标 */
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .image-modal.show {
            opacity: 1;
        }
        .image-modal.grabbing {
            cursor: grabbing !important;
        }

        /* 图片容器 */
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            max-width: 90%;
            max-height: 90%;
            border: 2px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.3s ease, filter 0.3s ease;
            filter: blur(2px);
        }
        .modal-content.show {
            transform: translate(-50%, -50%) scale(1);
            filter: blur(0);
        }
        .modal-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s linear infinite;
            z-index: 1001;
            display: none;
        }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
            z-index: 1002;
        }
        .close:hover {
            opacity: 1;
        }
        .image-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 20px;
            padding: 5px 15px;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .image-modal:hover .image-controls {
            opacity: 0.7;
        }
        .image-controls:hover {
            opacity: 1 !important;
        }
        .control-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            margin: 0 10px;
            cursor: pointer;
            padding: 5px 10px;
        }
        .control-btn:hover {
            background-color: rgba(255,255,255,0.2);
            border-radius: 4px;
        }
        .tooltip {
            position: absolute;
            background-color: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            white-space: nowrap;
        }
    </style>
    <script>
        // 修改检查行是否为空的函数，确保更准确地判断
        function checkRowIsEmpty(row) {
            const inputs = row.querySelectorAll('input[type="text"], textarea');
            let hasContent = false;
            
            // 检查行中所有输入元素是否包含数据
            for (let input of inputs) {
                const value = input.value ? input.value.trim() : '';
                // 不为空且不为"None"/"none"则视为有内容
                if (value !== '' && value.toLowerCase() !== 'none') {
                    hasContent = true;
                    break;
                }
            }
            
            // 检查行中所有单元格内文本是否包含数据
            if (!hasContent) {
                const cells = row.querySelectorAll('td:not(:empty)');
                for (let cell of cells) {
                    // 排除只包含表单元素的单元格
                    if (!cell.querySelector('input, textarea')) {
                        const text = cell.textContent.trim();
                        if (text !== '' && text.toLowerCase() !== 'none' && text !== '/') {
                            hasContent = true;
                            break;
                        }
                    }
                }
            }
            
            return !hasContent;
        }

        // 在DOM加载完成后检查所有动态尺寸行
        document.addEventListener('DOMContentLoaded', function() {
            // 首先处理尺寸行4-30
            for (let i = 4; i <= 30; i++) {
                const row = document.getElementById(`size-row-${i}`);
                if (row) {
                    const isEmpty = checkRowIsEmpty(row);
                    row.style.display = isEmpty ? 'none' : 'table-row';
                }
            }
            
            // 处理所有动态尺寸行
            document.querySelectorAll('.dynamic-size-row').forEach(row => {
                const isEmpty = checkRowIsEmpty(row);
                row.style.display = isEmpty ? 'none' : 'table-row';
            });
        });
        
        // 图片点击放大功能
        document.addEventListener('DOMContentLoaded', function() {
            // 创建模态框元素
            const modal = document.createElement('div');
            modal.className = 'image-modal';
            modal.innerHTML = `
                <span class="close">&times;</span>
                <div class="modal-loader"></div>
                <img class="modal-content" id="modal-image">
                <div class="image-controls">
                    <button class="control-btn" id="zoom-out">−</button>
                    <button class="control-btn" id="reset-view">重置</button>
                    <button class="control-btn" id="zoom-in">+</button>
                </div>
                <div class="tooltip" id="image-tooltip"></div>
            `;
            document.body.appendChild(modal);
            
            const modalImg = document.getElementById('modal-image');
            const loader = modal.querySelector('.modal-loader');
            const tooltip = document.getElementById('image-tooltip');
            const zoomIn = document.getElementById('zoom-in');
            const zoomOut = document.getElementById('zoom-out');
            const resetView = document.getElementById('reset-view');
            
            // 创建缓存对象存储预加载图片
            const imageCache = {};
            
            // 显示操作提示
            function showTooltip(text, x, y, duration = 1500) {
                tooltip.textContent = text;
                tooltip.style.left = `${x}px`;
                tooltip.style.top = `${y}px`;
                tooltip.style.opacity = '1';
                
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                }, duration);
            }
            
            // 获取所有预览图片并预加载
            const previewImages = document.querySelectorAll('.preview-image');
            previewImages.forEach(img => {
                // 为每张图片添加样式和监听器
                img.style.cursor = 'zoom-in';
                img.setAttribute('title', '点击放大');
                
                // 预加载图片
                if (img.src) {
                    const preloadImg = new Image();
                    preloadImg.src = img.src;
                    imageCache[img.src] = preloadImg;
                }
                
                // 点击事件
                img.addEventListener('click', function() {
                    openModal(this);
                });
            });
            
            // 打开模态框
            function openModal(imgElement) {
                // 显示加载状态
                loader.style.display = 'block';
                modal.style.display = 'block';
                
                // 设置图片源
                modalImg.src = imgElement.src;
                
                // 重置变换
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform(true);
                
                // 动画显示模态框
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
                
                // 图片加载完成后
                modalImg.onload = function() {
                    loader.style.display = 'none';
                    setTimeout(() => {
                        modalImg.classList.add('show');
                        
                        // 显示操作提示
                        const rect = modalImg.getBoundingClientRect();
                        showTooltip('滚轮或触摸缩放，拖拽移动，双击重置', 
                                  window.innerWidth / 2, 
                                  window.innerHeight - 100, 
                                  3000);
                    }, 50);
                };
            }
            
            // 关闭模态框
            function closeModal() {
                modal.classList.remove('show');
                modalImg.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    translateX = 0;
                    translateY = 0;
                    scale = 1;
                }, 300);
            }
            
            // 添加关闭按钮点击事件
            const closeBtn = document.querySelector('.close');
            closeBtn.addEventListener('click', closeModal);
            
            // 点击模态框背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
            
            // 缩放和移动相关变量
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let isDragging = false;
            let startX, startY;
            let lastTapTime = 0;
            
            // 更新图片变换
            function updateTransform(isInitial = false) {
                const transform = `
                    translate(-50%, -50%)
                    translate(${translateX}px, ${translateY}px)
                    scale(${scale})
                `;
                
                if (isInitial) {
                    modalImg.style.transform = transform;
                } else {
                    modalImg.style.transition = 'none';
                    modalImg.style.transform = transform;
                    // 强制重排
                    void modalImg.offsetHeight;
                    modalImg.style.transition = 'transform 0.05s ease';
                }
            }
            
            // 计算动态缩放步长
            function calculateZoomStep(currentScale) {
                // 在不同缩放级别使用不同的步长
                if (currentScale < 0.5) return 0.1;
                if (currentScale < 1) return 0.15;
                if (currentScale < 2) return 0.25;
                if (currentScale < 5) return 0.5;
                return 1;
            }
            
            // 缩放功能
            function zoomImage(direction, fixedScale = null) {
                const zoomStep = fixedScale || calculateZoomStep(scale);
                const oldScale = scale;
                
                if (direction === 'in') {
                    scale = Math.min(10, scale + zoomStep);
                } else {
                    scale = Math.max(0.1, scale - zoomStep);
                }
                
                // 防止过小缩放
                if (scale < 0.1) scale = 0.1;
                
                // 显示当前缩放比例
                showTooltip(`缩放: ${Math.round(scale * 100)}%`, 
                          window.innerWidth / 2,
                          window.innerHeight / 2);
                
                updateTransform();
            }
            
            // 滚轮缩放
            modal.addEventListener('wheel', function(e) {
                e.preventDefault();
                zoomImage(e.deltaY < 0 ? 'in' : 'out');
            }, { passive: false });
            
            // 控制按钮事件
            zoomIn.addEventListener('click', () => zoomImage('in'));
            zoomOut.addEventListener('click', () => zoomImage('out'));
            resetView.addEventListener('click', () => {
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform();
                showTooltip('视图已重置', window.innerWidth / 2, window.innerHeight / 2);
            });
            
            // 拖拽功能
            modalImg.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);
            
            function startDrag(e) {
                if (e.button === 0) { // 左键点击
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    modal.style.cursor = 'grabbing';
                    e.preventDefault();
                    
                    // 检测双击
                    const now = new Date().getTime();
                    const timeDiff = now - lastTapTime;
                    if (timeDiff < 300 && timeDiff > 0) {
                        // 双击重置
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                        showTooltip('视图已重置', e.clientX, e.clientY);
                    }
                    lastTapTime = now;
                }
            }
            
            function drag(e) {
                if (!isDragging) return;
                
                const dx = e.clientX - startX;
                const dy = e.clientY - startY;
                startX = e.clientX;
                startY = e.clientY;
                
                // 动态调整拖动灵敏度
                const speedFactor = scale > 1 ? 1 : Math.sqrt(scale);
                translateX += dx / speedFactor;
                translateY += dy / speedFactor;
                
                updateTransform();
            }
            
            function endDrag() {
                if (isDragging) {
                    isDragging = false;
                    modal.style.cursor = 'zoom-in';
                }
            }
            
            // 键盘控制
            document.addEventListener('keydown', function(e) {
                if (modal.style.display !== 'block') return;
                
                switch (e.key) {
                    case 'Escape':
                        closeModal();
                        break;
                    case 'ArrowLeft':
                        translateX += 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowRight':
                        translateX -= 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowUp':
                        translateY += 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case 'ArrowDown':
                        translateY -= 30 / Math.sqrt(scale);
                        updateTransform();
                        e.preventDefault();
                        break;
                    case '+':
                    case '=':
                        zoomImage('in', 0.25);
                        e.preventDefault();
                        break;
                    case '-':
                    case '_':
                        zoomImage('out', 0.25);
                        e.preventDefault();
                        break;
                    case '0':
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                        showTooltip('视图已重置', window.innerWidth / 2, window.innerHeight / 2);
                        e.preventDefault();
                        break;
                }
            });
            
            // 触摸设备支持
            let lastDistance = 0;
            let initialScale = 1;
            
            modalImg.addEventListener('touchstart', function(e) {
                if (e.touches.length === 1) {
                    // 单指拖动
                    isDragging = true;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    
                    // 检测双击
                    const now = new Date().getTime();
                    const timeDiff = now - lastTapTime;
                    if (timeDiff < 300 && timeDiff > 0) {
                        // 双击重置
                        scale = 1;
                        translateX = 0;
                        translateY = 0;
                        updateTransform();
                    }
                    lastTapTime = now;
                } else if (e.touches.length === 2) {
                    // 双指缩放
                    isDragging = false;
                    const dx = e.touches[0].clientX - e.touches[1].clientX;
                    const dy = e.touches[0].clientY - e.touches[1].clientY;
                    lastDistance = Math.sqrt(dx * dx + dy * dy);
                    initialScale = scale;
                }
            });
            
            modalImg.addEventListener('touchmove', function(e) {
                e.preventDefault(); // 防止页面滚动
                
                if (e.touches.length === 1 && isDragging) {
                    // 单指拖动
                    const dx = e.touches[0].clientX - startX;
                    const dy = e.touches[0].clientY - startY;
                    startX = e.touches[0].clientX;
                    startY = e.touches[0].clientY;
                    
                    // 根据缩放级别调整拖动灵敏度
                    const speedFactor = scale > 1 ? 1 : Math.sqrt(scale);
                    translateX += dx / speedFactor;
                    translateY += dy / speedFactor;
                    
                    updateTransform();
                } else if (e.touches.length === 2) {
                    // 双指缩放
                    const dx = e.touches[0].clientX - e.touches[1].clientX;
                    const dy = e.touches[0].clientY - e.touches[1].clientY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const newScale = initialScale * (distance / lastDistance);
                    
                    // 限制缩放范围
                    scale = Math.max(0.1, Math.min(10, newScale));
                    
                    updateTransform();
                    
                    // 显示缩放比例
                    showTooltip(`缩放: ${Math.round(scale * 100)}%`, 
                              window.innerWidth / 2,
                              window.innerHeight / 2);
                }
            });
            
            modalImg.addEventListener('touchend', function(e) {
                if (e.touches.length < 1) {
                    isDragging = false;
                }
            });
        });
    </script>
</head>
<body>
    <form id="material-form">
        <table>
            <!-- 主标题 -->
            <tr>
                <a href="{{ url_for('index') }}">返回首页</a>
                <td colspan="14" class="main-title">物料样板确认书
                    <button type="button" id="submit-btn" style="float: right; margin-right: 20px;">提交</button>
                </td>
                
            </tr>
            <!-- 报告编码 -->
            <tr>
                <td colspan="14">报告编码: <span id="report-code">{{ report_code }}</span></td>
            </tr>   


            <!-- 供应商信息 -->
            <tr>
                <td colspan="3" class="align-left">供应商/车间<br>（代理商要注明原厂）</td>
                <td colspan="3" class="align-center">
                    {{supplier}}
                </td>
                <td colspan="2" class="align-center">送检日期</td>
                <td colspan="2" class="align-center">{{inspection_date}}</td>
                <td class="align-center">样板数量</td>
                <td class="align-center">
                    {{sample_count}}
                </td>
                <td class="align-center">检验员</td>
                <td class="align-center">
                    {{inspector}}
                </td>
            </tr>

            <!-- 物料信息行 - 进一步调整结构以完全匹配图片 -->
            <tr>
                <td colspan="3">物料料号</td>          
                <td colspan="3">图号</td>
                <td colspan="3">物料名称</td>
                <td colspan="1">图纸版本</td>
                <td colspan="2"> 材质</td>
                <td colspan="2">表面处理</td>
            </tr>

            <!-- 添加第四行输入框 -->
            <tr>
                <td colspan="3" style="height: 20px;">
                    {{ material_number }}
                </td>
                <td colspan="3" style="height: 20px;">
                    {{graph_number}}
                </td>
                <td colspan="3" style="height: 20px;">
                    {{material_name}}
                </td>
                <td colspan="1" style="height: 20px;">
                    {{drawing_version}}
                </td>
                <td colspan="2" style="height: 20px;">
                    {{material_texture}}
                </td>
                <td colspan="2" style="height: 20px;">
                    {{surface_processing}}
                </td>
            </tr>

            <!-- 样板状态 -->
            <tr>
                <td colspan="14" class="align-left" style="height: 25px;">
                    样板提供状态：
                    <!-- 复选框动态选中 -->
                    <input type="checkbox" id="trial-sample" 
                        {{ 'checked' if '试产样品' in sample_status_list else '' }}>
                    <label for="trial-sample">试产样品</label>

                    <input type="checkbox" id="big-sample" 
                        {{ 'checked' if '大货样品' in sample_status_list else '' }}>
                    <label for="big-sample">大货样品</label>

                    <input type="checkbox" id="template" 
                        {{ 'checked' if '重制样板' in sample_status_list else '' }}>
                    <label for="template">重制样板</label>

                    <input type="checkbox" id="other" 
                        {{ 'checked' if '其他' in sample_status_list else '' }}>
                    <label for="other">其他：</label>

                    <!-- 文本框显示和内容填充 -->
                    <input type="text" id="other_textbox" name="other_textbox"
                        style="display: none;"  <!-- 初始化为隐藏 -->
                        {{ other_textbox_value|default('') }}
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const otherCheckbox = document.getElementById('other');
                                const otherTextbox = document.getElementById('other_textbox');

                                // 初始化时根据checked状态显示文本框
                                if (otherCheckbox.checked) {
                                    otherTextbox.style.display = 'inline-block';
                                    // 使用safe过滤器确保值正确渲染
                                    otherTextbox.value = "{{ other_textbox_value|tojson|safe }}";
                                }

                                // 监听复选框变化
                                otherCheckbox.addEventListener('change', function() {
                                    otherTextbox.style.display = this.checked ? 'inline-block' : 'none';
                                    // 当取消勾选时清空输入值（可选）
                                    if (!this.checked) {
                                        otherTextbox.value = '';
                                    }
                                });
                                // 只处理动态生成的尺寸行（排除前3条固定行）
                                document.querySelectorAll('.dynamic-size-row').forEach(row => {
                                    const isEmpty = checkRowIsEmpty(row);
                                    row.style.display = isEmpty ? 'none' : '';
                                });
                            });
                        </script>
                </td>
            </tr>

            <!-- ================= 尺寸检查 ================= -->
            <tr class="module-title">
                <td id="size-section-cell" rowspan="{% if size_row_count < 3 %}5{% else %}{{ size_row_count + 2 }}{% endif %}" style="width: 30px;">一、尺寸</td>
                <td colspan="5" class="align-center">图纸尺寸</td>
                <td colspan="5" class="align-center">实测尺寸(mm)</td>
                <td colspan="1" class="col-compliance-size align-center" rowspan="2">符合性</td>
                <td colspan="2" class="align-center" rowspan="2">注意事项</td>
            </tr>
            <tr class="sub-header">
                <td class="col-no align-center">NO</td>
                <td class="col-pos">位置</td>
                <td class="col-value">标准值</td>
                <td class="col-value">最小值</td>
                <td class="col-value">最大值</td>
                <td class="col-measure">1</td>
                <td class="col-measure">2</td>
                <td class="col-measure">3</td>
                <td class="col-measure">4</td>
                <td class="col-measure">5</td>
            </tr>
            <!-- 尺寸数据行 -->
            <!-- 尺寸数据行（固定前三条） -->
            <tr class="custom-row-height" id="size-row-1" style="display: table-row !important;">
                <td class="align-center">1</td>
                <td data-id="size-1" data-image="true" class="align-center">
                    {{ size_1_position }}
                </td>
                <td class="align-center">{{ size_1_value }}</td>
                <td class="align-center">{{ size_1_min }}</td>
                <td class="align-center">{{ size_1_max }}</td>
                <td class="align-center">{{ size_1_measure_1 }}</td>
                <td class="align-center">{{ size_1_measure_2 }}</td>
                <td class="align-center">{{ size_1_measure_3 }}</td>
                <td class="align-center">{{ size_1_measure_4 }}</td>
                <td class="align-center">{{ size_1_measure_5 }}</td>
                <td class="align-center">
                    {{ size_1_check }}
                </td>
                <td colspan="2"><textarea name="size_1_note">{{ size_1_note }}</textarea></td>
            </tr>
            <tr class="custom-row-height" id="size-row-2" style="display: table-row !important;">
                <td class="align-center">2</td>
                <td data-id="size-2" data-image="true" class="align-center">{{ size_2_position }}</td>
                <td class="align-center">{{ size_2_value }}</td>
                <td class="align-center">{{ size_2_min }}</td>
                <td class="align-center">{{ size_2_max }}</td>
                <td class="align-center">{{ size_2_measure_1 }}</td>
                <td class="align-center">{{ size_2_measure_2 }}</td>
                <td class="align-center">{{ size_2_measure_3 }}</td>
                <td class="align-center">{{ size_2_measure_4 }}</td>
                <td class="align-center">{{ size_2_measure_5 }}</td>
                <td class="align-center">
                    {{ size_2_check }}
                </td>
                <td colspan="2"><textarea name="size_2_note">{{ size_2_note }}</textarea></td>
            </tr>
            <tr class="custom-row-height" id="size-row-3" style="display: table-row !important;">
                <td class="align-center">3</td>
                <td data-id="size-3" data-image="true" class="align-center">{{ size_3_position }}</td>
                <td class="align-center">{{ size_3_value }}</td>
                <td class="align-center">{{ size_3_min }}</td>
                <td class="align-center">{{ size_3_max }}</td>
                <td class="align-center">{{ size_3_measure_1 }}</td>
                <td class="align-center">{{ size_3_measure_2 }}</td>
                <td class="align-center">{{ size_3_measure_3 }}</td>
                <td class="align-center">{{ size_3_measure_4 }}</td>
                <td class="align-center">{{ size_3_measure_5 }}</td>
                <td class="align-center">
                    {{ size_3_check }}
                </td>
                <td colspan="2"><textarea name="size_3_note">{{ size_3_note }}</textarea></td>
            </tr>
            <!-- 动态渲染其他尺寸数据 -->
            {% for data in size_data %}
                {% if data.size_number > 3 %} <!-- 跳过前3条固定行 -->
                <tr class="custom-row-height dynamic-size-row" id="size-row-{{ data.size_number }}">
                    <td class="align-center">{{ data.size_number }}</td>
                    <td data-id="size-{{ data.size_number }}" data-image="true" class="align-center">
                        {{ data.position }}
                    </td>
                    <td class="align-center">{{ data.value }}</td>
                    <td class="align-center">{{ data.min_value }}</td>
                    <td class="align-center">{{ data.max_value }}</td>
                    <td class="align-center">{{ data.measure_1 }}</td>
                    <td class="align-center">{{ data.measure_2 }}</td>
                    <td class="align-center">{{ data.measure_3 }}</td>
                    <td class="align-center">{{ data.measure_4 }}</td>
                    <td class="align-center">{{ data.measure_5 }}</td>
                    <td class="align-center">
                        {{ data.check_result }}
                    </td>
                    <td colspan="2"><textarea name="size_{{ data.size_number }}_note">{{ data.note }}</textarea></td>
                </tr>
                {% endif %}
            {% endfor %}

            <!-- ================= 外观检查 ================= -->
            <tr class="module-title">
                <td rowspan="5" style="width: 30px;">二、外观</td>
                <td class="col-no align-center">NO</td>
                <td colspan="9" class="align-center">要求描述</td>
                <td class="col-check align-center col-compliance">符合性</td>
                <td class="col-note align-center" colspan="2">注意事项</td>
            </tr>
            <!-- 外观条目 -->
            <tr>
                <td class="align-center">1</td>
                <td colspan="9" class="align-left">表面刮痕、凹痕、裂纹、沙孔等不良符合《外观标准》要求</td>
                <td class="align-center">
                     {{ appearance_data[0]['check_result']}}
                </td>
                <td colspan="2"><textarea name="appearance_1_note">{{ appearance_data[0]['note'] }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">2</td>
                <td colspan="9" class="align-left">不能出现表面生锈、披锋刮手、焊接无脱焊、表面抛光纹路、光泽度符合要求</td>
                <td class="align-center">
                    {{ appearance_data[1]['check_result']}} </td>
                <td colspan="2"><textarea name="appearance_2_note">{{ appearance_data[1]['note'] }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">3</td>
                <td colspan="9" class="align-left">无明显的气味</td>
                <td class="align-center">
                    {{ appearance_data[2]['check_result']}}
                </td>
                <td colspan="2"><textarea name="appearance_3_note">{{ appearance_data[2]['note'] }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">4</td>
                <td colspan="9" class="align-left">
                    <textarea name="appearance_4_other" id="appearance-other" placeholder="其他/特殊要求：">{{ appearance_data[3]['other_info'] }}</textarea>
                </td>
                <td class="align-center">
                    {{ appearance_data[3]['check_result']}}
                 </td>
                <td colspan="2"><textarea name="appearance_4_note">{{ appearance_data[3]['note'] }}</textarea></td>
            </tr>
            <tr class="module-title">
                <td rowspan="7" class="align-center">三、功能/可靠性</td>
                <td class="align-center">NO.</td>
                <td colspan="9" class="align-center">要求描述</td>
                <td>符合性</td>
                <td colspan="2">注意事项</td>
            </tr>
            <tr>
                <td class="align-center">1</td>
                <td colspan="9" class="align-left">牙管内、外螺牙顺畅（管缝无高起）；弯管的折弯角度符合要求</td>
                <td class="align-center">{{ function_1_check }}</td>
                <td colspan="2"><textarea name="function_1_note">{{ function_1_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">2</td>
                <td colspan="9" class="align-left">部件的认证标识参数符合要求</td>
                <td class="align-center">{{ function_2_check }}</td>
                <td colspan="2"><textarea name="function_2_note">{{ function_2_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">3</td>
                <td colspan="9" class="align-left">使用配合件进行试装配检验</td>
                <td class="align-center">{{ function_3_check }}</td>
                <td colspan="2"><textarea name="function_3_note">{{ function_3_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">4</td>
                <td colspan="9" class="align-left">
                    电气性能：光电参数、耐压测试、亮灯性能、老化测试： 
                    {{ function_4_burnin }}
                    <br>
                    电阻、电感、电容等关键电气参数：
                    {{ function_4_electrical }}
                </td>
                <td class="align-center">{{ function_4_check }}</td>
                <td colspan="2"><textarea name="function_4_note">{{ function_4_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">5</td>
                <td colspan="9" class="align-left">
                    <!-- 添加复选框 -->
                    <input type="checkbox" id="function_5_tests_salt_fog" name="function_5_tests[]" value="盐雾测试" {{ 'checked' if '盐雾测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_salt_fog">盐雾测试</label>
                    
                    <input type="checkbox" id="function_5_tests_thickness" name="function_5_tests[]" value="厚度测试" {{ 'checked' if '厚度测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_thickness">厚度测试</label>
                    
                    <input type="checkbox" id="function_5_tests_flame_retardant" name="function_5_tests[]" value="阻燃测试" {{ 'checked' if '阻燃测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_flame_retardant">阻燃测试</label>
                    
                    <input type="checkbox" id="function_5_tests_hardness" name="function_5_tests[]" value="硬度测试" {{ 'checked' if '硬度测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_hardness">硬度测试</label>
                    
                    <input type="checkbox" id="function_5_tests_adhesion" name="function_5_tests[]" value="附着力测试" {{ 'checked' if '附着力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_adhesion">附着力测试</label><br>
                    
                    <input type="checkbox" id="function_5_tests_environment" name="function_5_tests[]" value="环境测试" {{ 'checked' if '环境测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_environment">环境测试</label>
                    
                    <input type="checkbox" id="function_5_tests_stress" name="function_5_tests[]" value="应力测试" {{ 'checked' if '应力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_stress">应力测试</label>
                    
                    <input type="checkbox" id="function_5_tests_torque" name="function_5_tests[]" value="扭力测试" {{ 'checked' if '扭力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_torque">扭力测试</label>
                    
                    <input type="checkbox" id="function_5_tests_impact" name="function_5_tests[]" value="冲击测试" {{ 'checked' if '冲击测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_impact">冲击测试</label>
                    
                    <input type="checkbox" id="function_5_tests_pull_force" name="function_5_tests[]" value="推拉力测试" {{ 'checked' if '推拉力测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_pull_force">推拉力测试</label><br>
                    
                    <input type="checkbox" id="function_5_tests_scratch_resistance" name="function_5_tests[]" value="耐刮擦性测试" {{ 'checked' if '耐刮擦性测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_scratch_resistance">耐刮擦性测试</label>
                    
                    <input type="checkbox" id="function_5_tests_other" name="function_5_tests[]" value="其他测试" {{ 'checked' if '其他测试' in function_5_tests else '' }}>
                    <label for="function_5_tests_other">其他测试：</label>
                    {{ function_5_other_test }}
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // 获取复选框元素
                            const checkbox = document.getElementById('function_5_tests_other');
                            const textbox = document.getElementById('function_5_other_test');
                    
                            // 监听复选框的状态变化
                            checkbox.addEventListener('change', function() {
                                // 根据复选框是否被选中，显示或隐藏文本框
                                textbox.style.display = this.checked ? 'inline-block' : 'none';
                            });
                        });
                    </script>
                </td>
                <td class="align-center">{{ function_5_check }}</td>
                <td colspan="2"><textarea name="function_5_note">{{ function_5_note }}</textarea></td>
            </tr>
            <tr>
                <td class="align-center">6</td>
                <td colspan="9" class="align-left"><textarea name="function_6_other" placeholder="其他/特殊要求：">{{ function_6_other }}</textarea></td>

                <td class="align-center">{{ function_6_check }}</td>
                <td colspan="2"><textarea name="function_6_note">{{ function_6_note }}</textarea></td>
            </tr>

            <!-- ================= 补充问题点 ================= -->
            <tr class="module-title">
                <td colspan="14">四、补充问题点/题点</td>
            </tr>
            {% for question in questions|batch(3) %}
            <tr>
                {% for q in question %}
                <td colspan="{% if loop.index == 1 %}4{% else %}5{% endif %}" class="image-upload-cell">
                    <div class="image-upload-box">
                        {% if q.question_number in valid_question_numbers and question_images[q.question_number] and not question_images[q.question_number].no_image %}
                            <img class="preview-image" src="{{ question_images[q.question_number].full_path }}" />
                        {% else %}
                            <div class="upload-overlay">无图片</div>
                        {% endif %}
                    </div>
                </td>
                {% endfor %}
            </tr>
            <tr>
                {% for q in question %}
                <td colspan="{% if loop.index == 1 %}4{% else %}5{% endif %}" style="height: 10px;">
                    {% if q.question_text is defined and q.question_text != '' %}
                        <textarea>{{ q.question_text }}</textarea>
                    {% else %}
                        {% if question_images[q.question_number] and not question_images[q.question_number].no_image %}
                            <img src="{{ question_images[q.question_number]['full_path'] }}" style="max-width:100%; max-height:120px;">
                        {% else %}
                            无图片
                            <br>
                            {{ questions|selectattr('question_number', 'equalto', q.question_number)|map(attribute='question_text')|first }}
                        {% endif %}
                    {% endif %}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}

            <!-- ================= 最终判定 ================= -->
            <tr>
                <td rowspan="2" colspan="2" class="align-center">最终判定</td>
                <td colspan="10" class="align-center" style="height: 30px;font-size: 13px;">{{ final_judgment }}</td>
                
                <td colspan="2" class="align-center">审核</td>
            </tr>
            <tr>
                <td colspan="1" class="align-left">意见：</td>
                <td colspan="9">
                    <textarea id="opinion" name="opinion" rows="2" cols="55">{{ opinion }}</textarea>
                </td>
                <td colspan="1">
                    {{ review }}
                </td>
                <td colspan="1"></td>
            </tr>
        </table>
    </form>
    <script>
        document.getElementById('submit-btn').addEventListener('click', () => {
            fetch('/submit', { method: 'POST', body: new FormData(document.forms[0]) })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('report-code').textContent = data.report_code;  
                });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // 遍历所有尺寸行（1到30）
            for (let i = 4; i <= 30; i++) {
                const row = document.getElementById(`size-row-${i}`);
                if (!row) continue; // 如果该行不存在（如未定义的行号）

                // 检查该行是否所有输入字段为空
                const isEmpty = checkRowIsEmpty(row);

                // 根据是否为空显示/隐藏行
                row.style.display = isEmpty ? 'none' : 'table-row';
            }

            // 处理动态尺寸行
            document.querySelectorAll('.dynamic-size-row').forEach(row => {
                const isEmpty = checkRowIsEmpty(row);
                row.style.display = isEmpty ? 'none' : 'table-row';
            });
        });

        // 为所有输入框添加输入监听
        document.querySelectorAll('input[type="text"], textarea').forEach(input => {
            input.addEventListener('input', function() {
                const row = this.closest('tr');
                if (row && row.classList.contains('dynamic-size-row')) {
                    const isEmpty = checkRowIsEmpty(row);
                    row.style.display = isEmpty ? 'none' : 'table-row';
                }
            });
        });
    </script>
    
</body>
</html>
