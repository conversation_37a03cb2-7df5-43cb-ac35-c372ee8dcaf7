<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>物料管理</title>
    <script>
        function openEditWindow(materialId) {
            var editWindow = window.open('/edit_material/' + encodeURIComponent(materialId), 'Edit Material', 'width=600,height=400');
        }
    </script>
</head>
<body>
    <h1>物料管理</h1>
    <form method="post">
        <label for="search_term">物料料号搜索:</label>
        <input type="text" id="search_term" name="search_term" value="{{ search_term }}">
        <input type="submit" value="搜索">
    </form>
    <button onclick="toggleDeleteButtons()">管理</button>
    {% if materials %}
    <table border="1" id="material-table">
        <tr>
            <th style="display: none;">操作</th>
            <th>物料料号</th>
            <th>物料名称</th>
            <th>供应商</th> 
            <th>录入时间</th>
        </tr>
        {% for material in materials %}
            <tr>
                <td style="display: none;">
                    <a href="#" class="delete-button" onclick="confirmDelete('{{ material[0] }}')">删除</a>
                    <a href="#" class="edit-button" onclick="openEditWindow('{{ material[0] }}')">修改</a>
                </td>
                <td>{{ material[1] }}</td>
                <td>{{ material[2] }}</td>
                <td>
                    {% if material[4] %}
                        {{ material[4]|replace(',', ', ') }}
                    {% else %}
                        无供应商
                    {% endif %}
                </td>
                <td>{{ material[3] }}</td>
            </tr>
        {% endfor %}
    </table>
    {% else %}
    <p>暂无物料信息。</p>
    {% endif %}
    <a href="{{ url_for('index') }}">返回首页</a>

    <script>
        function toggleDeleteButtons() {
            var th = document.querySelector('#material-table th:first-child');
            var tds = document.querySelectorAll('#material-table td:first-child');
            if (th.style.display === 'none') {
                th.style.display = 'table-cell';
                tds.forEach(function(td) {
                    td.style.display = 'table-cell';
                });
            } else {
                th.style.display = 'none';
                tds.forEach(function(td) {
                    td.style.display = 'none';
                });
            }
        }

        function confirmDelete(material_id) {
            if (confirm('删除物料同时会删除检验记录')) {
                if (confirm('是否确认删除')) {
                    window.location.href = '/delete_material/' + encodeURIComponent(material_id);
                }
            }
        }
    </script>
</body>
</html>