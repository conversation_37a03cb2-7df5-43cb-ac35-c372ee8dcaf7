import datetime
import random
import uuid
import os
import shutil
import json
import re
from flask import Blueprint, render_template, jsonify, request, redirect, url_for, current_app, send_from_directory, session
from db_config import get_db_connection
import mysql.connector

Material_Sample_Confirmation_Form_modify_bp = Blueprint('Material_Sample_Confirmation_Form_modify_bp', __name__)

# 临时存储问题点数据的字典，使用report_code作为键
temp_question_data_store = {}

def register_global_routes(app):
    """在主应用中注册全局路由"""
    @app.route('/submit', methods=['POST'])
    def global_submit_redirect():
        """将全局/submit路径重定向到正确的蓝图路由"""
        print("检测到对全局/submit的请求，重定向到蓝图的update路由")
        return redirect(url_for('Material_Sample_Confirmation_Form_modify_bp.update_form'))

def get_image_base_path():
    """获取图片存储的基础路径，从配置文件读取"""
    try:
        # 读取配置文件
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                base_path = config.get('image_base_path', 'D:/检验系统图片/')
                # 统一使用正斜杠，避免路径问题
                base_path = base_path.replace('\\', '/')
                # 确保路径以斜杠结尾
                if not base_path.endswith('/'):
                    base_path += '/'
                return base_path
        else:
            # 默认路径，确保使用正斜杠
            return 'D:/检验系统图片/'  # 默认路径
    except Exception as e:
        print(f"获取基础图片路径出错: {e}")
        # 默认路径，确保使用正斜杠
        return 'D:/检验系统图片/'

def get_image_final_path(report_code):
    """
    根据报告编码构建最终的图片存储路径
    遵循 年份/月份/报告编码 的目录结构
    示例: D:/检验系统图片/2025/06/MSC20250601003/
    """
    base_path = get_image_base_path()
    
    if report_code and len(report_code) >= 10 and report_code.startswith("MSC"):
        # 从报告编码中解析年份和月份
        # 报告编码格式为 MSC + YYYYMM(年月) + DD(日期) + XXX(序号)
        # 例如: MSC20250601003 中提取年份2025和月份06
        try:
            year = report_code[3:7]  # 完整年份，如 2025
            month = report_code[7:9]  # 月份，如 06
            
            # 构建最终路径
            final_path = os.path.join(base_path, year, month, report_code)
            os.makedirs(final_path, exist_ok=True)
            # 统一使用正斜杠，避免路径问题
            final_path = final_path.replace('\\', '/')
            return final_path
        except Exception as e:
            print(f"解析报告编码出错: {e}")
            # 备用路径
            fallback_path = os.path.join(base_path, "未分类", report_code)
            os.makedirs(fallback_path, exist_ok=True)
            # 统一使用正斜杠，避免路径问题
            fallback_path = fallback_path.replace('\\', '/')
            return fallback_path
    else:
        # 如果报告编码格式不正确，使用备用路径
        fallback_path = os.path.join(base_path, "未分类", report_code if report_code else "未知报告")
        os.makedirs(fallback_path, exist_ok=True)
        # 统一使用正斜杠，避免路径问题
        fallback_path = fallback_path.replace('\\', '/')
        return fallback_path

# 新增函数：检查原始路径是否存在并返回正确路径
def get_original_image_dir(report_code):
    """
    尝试查找报告编码对应的原始图片目录
    这个函数会检查常见的路径模式，尝试找到已存在的原始图片目录
    """
    base_path = get_image_base_path()
    
    # 尝试不同的路径模式
    potential_paths = []
    
    # 1. 尝试标准的年/月/报告编码路径
    if report_code and len(report_code) >= 10 and report_code.startswith("MSC"):
        try:
            year = report_code[3:7]  # 完整年份，如 2025
            month = report_code[7:9]  # 月份，如 06
            potential_paths.append(os.path.join(base_path, year, month, report_code))
        except Exception:
            pass
    
    # 2. 尝试其他可能的路径格式（如年/周号格式）
    # 遍历年份目录
    if os.path.exists(base_path) and os.path.isdir(base_path):
        for year_dir in os.listdir(base_path):
            year_path = os.path.join(base_path, year_dir)
            if os.path.isdir(year_path):
                # 遍历年份下的子目录
                for sub_dir in os.listdir(year_path):
                    sub_path = os.path.join(year_path, sub_dir)
                    if os.path.isdir(sub_path):
                        # 检查是否存在报告编码目录
                        report_dir = os.path.join(sub_path, report_code)
                        if os.path.exists(report_dir) and os.path.isdir(report_dir):
                            potential_paths.append(report_dir)
    
    # 3. 直接尝试在基础路径下查找报告编码目录
    direct_path = os.path.join(base_path, report_code)
    potential_paths.append(direct_path)
    
    # 4. 添加未分类目录
    fallback_path = os.path.join(base_path, "未分类", report_code)
    potential_paths.append(fallback_path)
    
    # 检查路径是否存在
    for path in potential_paths:
        # 统一使用正斜杠，避免路径问题
        path = path.replace('\\', '/')
        if os.path.exists(path) and os.path.isdir(path):
            print(f"找到原始图片目录: {path}")
            return path
    
    # 如果没有找到已存在的目录，返回标准生成的路径
    print(f"未找到原始图片目录，将使用默认路径")
    final_path = get_image_final_path(report_code)
    # 统一使用正斜杠，避免路径问题
    final_path = final_path.replace('\\', '/')
    return final_path

# 将图片访问路由从app移动到Blueprint
@Material_Sample_Confirmation_Form_modify_bp.route('/images/MSCF_modify_temp_uploads/<path:filename>')
def custom_static(filename):
    base_image_path = get_image_base_path()
    temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads')
    return send_from_directory(temp_uploads_dir, filename)

# 增加一个新的路由，支持在路径中包含子文件夹
@Material_Sample_Confirmation_Form_modify_bp.route('/images/MSCF_modify_temp_uploads/<report_code>/<path:filename>')
def temp_image_with_folder(report_code, filename):
    base_image_path = get_image_base_path()
    temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads', report_code)
    return send_from_directory(temp_uploads_dir, filename)

# 动态数据接口（返回JSON）
@Material_Sample_Confirmation_Form_modify_bp.route('/<sample_id>')
def load_sample_data(sample_id):
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取图片重排序设置
        enable_image_reordering = 'no'
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    enable_image_reordering = config.get('enable_image_reordering', 'no')
                    print(f"页面加载时获取图片重排序设置: {enable_image_reordering}")
            else:
                print("未找到配置文件，使用默认设置：不进行问题点图片重排序")
        except Exception as e:
            print(f"读取图片重排序设置时出错: {e}")
        
        # 初始化或更新临时存储中的重排序设置
        if sample_id not in temp_question_data_store:
            temp_question_data_store[sample_id] = {
                'enable_reordering': enable_image_reordering,
                'question_data': [],
                'question_mapping': {}
            }
        else:
            # 更新现有记录中的重排序设置
            temp_question_data_store[sample_id]['enable_reordering'] = enable_image_reordering
            print(f"更新报告 {sample_id} 的临时存储数据，重排序设置为: {enable_image_reordering}")
        
        # 查询基本信息
        cursor.execute("""
            SELECT report_code, supplier, inspection_date, sample_count, inspector,
                   material_number, graph_number, material_name, drawing_version,
                   material_texture, surface_processing, sample_status, other_textbox,
                   final_judgment, opinion, review
            FROM material_sample_confirmation_form
            WHERE report_code = %s
        """, (sample_id,))
        base_info = cursor.fetchone()
        
        if not base_info:
            # 确保没有未读结果
            if cursor.with_rows:
                cursor.fetchall()
            return jsonify({"error": "未找到对应报告数据"}), 404
        
        # 查询尺寸数据
        cursor.execute("""
            SELECT 
                size_number, 
                COALESCE(position, '') as position, 
                COALESCE(value, '') as value, 
                COALESCE(min_value, '') as min_value, 
                COALESCE(max_value, '') as max_value,
                COALESCE(measure_1, '') as measure_1, 
                COALESCE(measure_2, '') as measure_2, 
                COALESCE(measure_3, '') as measure_3, 
                COALESCE(measure_4, '') as measure_4, 
                COALESCE(measure_5, '') as measure_5,
                COALESCE(NULLIF(check_result, ''), '/') as check_result, 
                COALESCE(note, '') as note
            FROM material_sample_size_data
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY size_number
        """, (sample_id,))
        
        # 确保完整获取结果集
        all_size_data = cursor.fetchall()
        
        # 确保size_data按size_number排序
        size_data = []
        existing_row_numbers = set()
        
        # 首先处理所有数据行，记录已存在的行号
        for row in all_size_data:
            existing_row_numbers.add(row['size_number'])
            # 无条件添加前3行
            if row['size_number'] <= 3:
                size_data.append(row)
            # 对于第4行及以后，只包含非空行
            elif not all(val is None or val == '' or str(val).lower() == 'none' 
                      for val in [row['position'], row['value'], row['min_value'], 
                                row['max_value'], row['measure_1'], row['measure_2'], 
                                row['measure_3'], row['measure_4'], row['measure_5']]):
                size_data.append(row)
        
        # 确保前3行数据存在，如果数据库中没有，则创建空记录
        for i in range(1, 4):
            if i not in existing_row_numbers:
                # 创建空记录
                empty_row = {
                    'size_number': i,
                    'position': '',
                    'value': '',
                    'min_value': '',
                    'max_value': '',
                    'measure_1': '',
                    'measure_2': '',
                    'measure_3': '',
                    'measure_4': '',
                    'measure_5': '',
                    'check_result': '/',
                    'note': ''
                }
                size_data.append(empty_row)
                print(f"创建第{i}行的空记录，因为数据库中不存在")
        
        # 确保size_data按size_number排序
        size_data.sort(key=lambda x: x['size_number'])
                
        # 统计非空尺寸数据行数，确保至少包含3行
        size_row_count = max(3, len(size_data))
        
        # 分离前3行和其他行
        fixed_rows = [row for row in size_data if row['size_number'] <= 3]
        other_rows = [row for row in size_data if row['size_number'] > 3]
        
        # 重新编号第4行及以后的数据
        for i, row in enumerate(other_rows):
            row['size_number'] = i + 4
            
        # 确保前3行数据存在
        existing_numbers = {row['size_number'] for row in fixed_rows}
        for i in range(1, 4):
            if i not in existing_numbers:
                empty_row = {
                    'size_number': i,
                    'position': '',
                    'value': '',
                    'min_value': '',
                    'max_value': '',
                    'measure_1': '',
                    'measure_2': '',
                    'measure_3': '',
                    'measure_4': '',
                    'measure_5': '',
                    'check_result': '/',
                    'note': ''
                }
                fixed_rows.append(empty_row)
        
        # 按行号排序固定行
        fixed_rows.sort(key=lambda x: x['size_number'])
        
        # 检查第1行是否为空，如果为空且第2行有数据，则进行前移
        row_1 = next((row for row in fixed_rows if row['size_number'] == 1), None)
        row_2 = next((row for row in fixed_rows if row['size_number'] == 2), None)
        row_3 = next((row for row in fixed_rows if row['size_number'] == 3), None)
        
        # 检查第1行是否真的为空（所有重要字段都为空）
        if row_1 and all(not row_1[field] or row_1[field] == '/' for field in 
                        ['position', 'value', 'min_value', 'max_value', 
                         'measure_1', 'measure_2', 'measure_3', 'measure_4', 'measure_5']):
            
            # 第1行为空，检查第2行是否有数据
            if row_2 and any(row_2[field] and row_2[field] != '/' for field in 
                           ['position', 'value', 'min_value', 'max_value', 
                            'measure_1', 'measure_2', 'measure_3', 'measure_4', 'measure_5']):
                
                # 将第2行数据移到第1行
                row_1['position'] = row_2['position']
                row_1['value'] = row_2['value']
                row_1['min_value'] = row_2['min_value']
                row_1['max_value'] = row_2['max_value']
                row_1['measure_1'] = row_2['measure_1']
                row_1['measure_2'] = row_2['measure_2']
                row_1['measure_3'] = row_2['measure_3']
                row_1['measure_4'] = row_2['measure_4']
                row_1['measure_5'] = row_2['measure_5']
                row_1['check_result'] = row_2['check_result']
                row_1['note'] = row_2['note']
                
                # 将第3行数据移到第2行
                row_2['position'] = row_3['position']
                row_2['value'] = row_3['value']
                row_2['min_value'] = row_3['min_value']
                row_2['max_value'] = row_3['max_value']
                row_2['measure_1'] = row_3['measure_1']
                row_2['measure_2'] = row_3['measure_2']
                row_2['measure_3'] = row_3['measure_3']
                row_2['measure_4'] = row_3['measure_4']
                row_2['measure_5'] = row_3['measure_5']
                row_2['check_result'] = row_3['check_result']
                row_2['note'] = row_3['note']
                
                # 清空第3行
                row_3['position'] = ''
                row_3['value'] = ''
                row_3['min_value'] = ''
                row_3['max_value'] = ''
                row_3['measure_1'] = ''
                row_3['measure_2'] = ''
                row_3['measure_3'] = ''
                row_3['measure_4'] = ''
                row_3['measure_5'] = ''
                row_3['check_result'] = '/'
                row_3['note'] = ''
                
                # 如果有第4行数据，移到第3行
                if other_rows and len(other_rows) > 0:
                    row_4 = other_rows[0]  # 第一个其他行就是第4行
                    
                    # 将第4行数据移到第3行
                    row_3['position'] = row_4['position']
                    row_3['value'] = row_4['value']
                    row_3['min_value'] = row_4['min_value']
                    row_3['max_value'] = row_4['max_value']
                    row_3['measure_1'] = row_4['measure_1']
                    row_3['measure_2'] = row_4['measure_2']
                    row_3['measure_3'] = row_4['measure_3']
                    row_3['measure_4'] = row_4['measure_4']
                    row_3['measure_5'] = row_4['measure_5']
                    row_3['check_result'] = row_4['check_result']
                    row_3['note'] = row_4['note']
                    
                    # 移除第4行数据
                    other_rows.pop(0)
                    
                    # 重新编号剩余行
                    for i, row in enumerate(other_rows):
                        row['size_number'] = i + 4
            
            # 如果第2行也为空，检查第3行是否有数据
            elif row_2 and all(not row_2.get(field, '') or row_2.get(field, '') == '/' for field in 
                             ['position', 'value', 'min_value', 'max_value']) and \
                 not (row_2.get('measure_1') or row_2.get('measure_2') or row_2.get('measure_3') or row_2.get('measure_4') or row_2.get('measure_5')) and \
                 row_3 and (row_3.get('position') or row_3.get('value') or row_3.get('min_value') or row_3.get('max_value') or 
                           (row_3.get('measure_1') or row_3.get('measure_2') or row_3.get('measure_3') or row_3.get('measure_4') or row_3.get('measure_5')) or row_3.get('check_result')):
                
                # 将第3行数据移到第1行
                row_1['position'] = row_3['position']
                row_1['value'] = row_3['value']
                row_1['min_value'] = row_3['min_value']
                row_1['max_value'] = row_3['max_value']
                row_1['measure_1'] = row_3['measure_1']
                row_1['measure_2'] = row_3['measure_2']
                row_1['measure_3'] = row_3['measure_3']
                row_1['measure_4'] = row_3['measure_4']
                row_1['measure_5'] = row_3['measure_5']
                row_1['check_result'] = row_3['check_result']
                row_1['note'] = row_3['note']
                
                # 清空第2行和第3行
                for row in [row_2, row_3]:
                    row['position'] = ''
                    row['value'] = ''
                    row['min_value'] = ''
                    row['max_value'] = ''
                    row['measure_1'] = ''
                    row['measure_2'] = ''
                    row['measure_3'] = ''
                    row['measure_4'] = ''
                    row['measure_5'] = ''
                    row['check_result'] = ''
                    row['note'] = ''
                
                # 如果有第4行数据，移到第2行
                if other_rows and len(other_rows) > 0:
                    row_4 = other_rows[0]
                    
                    # 将第4行数据移到第2行
                    row_2['position'] = row_4['position']
                    row_2['value'] = row_4['value']
                    row_2['min_value'] = row_4['min_value']
                    row_2['max_value'] = row_4['max_value']
                    row_2['measure_1'] = row_4['measure_1']
                    row_2['measure_2'] = row_4['measure_2']
                    row_2['measure_3'] = row_4['measure_3']
                    row_2['measure_4'] = row_4['measure_4']
                    row_2['measure_5'] = row_4['measure_5']
                    row_2['check_result'] = row_4['check_result']
                    row_2['note'] = row_4['note']
                    
                    # 移除第4行数据
                    other_rows.pop(0)
                    
                    # 如果还有第5行数据，移到第3行
                    if other_rows and len(other_rows) > 0:
                        row_5 = other_rows[0]
                        
                        # 将第5行数据移到第3行
                        row_3['position'] = row_5['position']
                        row_3['value'] = row_5['value']
                        row_3['min_value'] = row_5['min_value']
                        row_3['max_value'] = row_5['max_value']
                        row_3['measure_1'] = row_5['measure_1']
                        row_3['measure_2'] = row_5['measure_2']
                        row_3['measure_3'] = row_5['measure_3']
                        row_3['measure_4'] = row_5['measure_4']
                        row_3['measure_5'] = row_5['measure_5']
                        row_3['check_result'] = row_5['check_result']
                        row_3['note'] = row_5['note']
                        
                        # 移除第5行数据
                        other_rows.pop(0)
                
        # 检查第2行是否为空，如果为空且第3行有数据，则进行前移
        elif row_2 and not (row_2.get('position') or row_2.get('value') or row_2.get('min_value') or row_2.get('max_value') or 
                          (row_2.get('measure_1') or row_2.get('measure_2') or row_2.get('measure_3') or row_2.get('measure_4') or row_2.get('measure_5')) or row_2.get('check_result')):
            
            # 第2行为空，检查第3行是否有数据
            if row_3 and (row_3.get('position') or row_3.get('value') or row_3.get('min_value') or row_3.get('max_value') or 
                        (row_3.get('measure_1') or row_3.get('measure_2') or row_3.get('measure_3') or row_3.get('measure_4') or row_3.get('measure_5')) or row_3.get('check_result')):
                
                # 将第3行数据移到第2行
                row_2['position'] = row_3['position']
                row_2['value'] = row_3['value']
                row_2['min_value'] = row_3['min_value']
                row_2['max_value'] = row_3['max_value']
                row_2['measure_1'] = row_3['measure_1']
                row_2['measure_2'] = row_3['measure_2']
                row_2['measure_3'] = row_3['measure_3']
                row_2['measure_4'] = row_3['measure_4']
                row_2['measure_5'] = row_3['measure_5']
                row_2['check_result'] = row_3['check_result']
                row_2['note'] = row_3['note']
                
                # 清空第3行
                row_3['position'] = ''
                row_3['value'] = ''
                row_3['min_value'] = ''
                row_3['max_value'] = ''
                row_3['measure_1'] = ''
                row_3['measure_2'] = ''
                row_3['measure_3'] = ''
                row_3['measure_4'] = ''
                row_3['measure_5'] = ''
                row_3['check_result'] = ''
                row_3['note'] = ''
                
                # 如果有第4行数据，移到第3行
                if other_rows and len(other_rows) > 0:
                    row_4 = other_rows[0]  # 第一个其他行就是第4行
                    
                    # 将第4行数据移到第3行
                    row_3['position'] = row_4['position']
                    row_3['value'] = row_4['value']
                    row_3['min_value'] = row_4['min_value']
                    row_3['max_value'] = row_4['max_value']
                    row_3['measure_1'] = row_4['measure_1']
                    row_3['measure_2'] = row_4['measure_2']
                    row_3['measure_3'] = row_4['measure_3']
                    row_3['measure_4'] = row_4['measure_4']
                    row_3['measure_5'] = row_4['measure_5']
                    row_3['check_result'] = row_4['check_result']
                    row_3['note'] = row_4['note']
                    
                    # 移除第4行数据
                    other_rows.pop(0)
                    
                    # 如果还有第5行数据，移到第3行
                    if other_rows and len(other_rows) > 0:
                        row_5 = other_rows[0]
                        
                        # 将第5行数据移到第3行
                        row_3['position'] = row_5['position']
                        row_3['value'] = row_5['value']
                        row_3['min_value'] = row_5['min_value']
                        row_3['max_value'] = row_5['max_value']
                        row_3['measure_1'] = row_5['measure_1']
                        row_3['measure_2'] = row_5['measure_2']
                        row_3['measure_3'] = row_5['measure_3']
                        row_3['measure_4'] = row_5['measure_4']
                        row_3['measure_5'] = row_5['measure_5']
                        row_3['check_result'] = row_5['check_result']
                        row_3['note'] = row_5['note']
                        
                        # 移除第5行数据
                        other_rows.pop(0)
                
        # 合并数据并重新排序
        size_data = fixed_rows + other_rows
        # 修复：确保每个元素都有 size_number 字段，如果没有则使用 original_number
        for i, row in enumerate(size_data):
            if 'size_number' not in row:
                if 'original_number' in row:
                    row['size_number'] = row['original_number']
                else:
                    row['size_number'] = i + 1  # 默认使用索引+1作为排序键
        
        # 使用安全的排序方式
        try:
            size_data.sort(key=lambda x: x['size_number'])
        except Exception as e:
            print(f"排序尺寸数据时出错: {e}，使用默认顺序")
        
        # 确保尺寸1-3的字段存在
        size_1 = next((item for item in size_data if item['size_number'] == 1), {})
        size_2 = next((item for item in size_data if item['size_number'] == 2), {})
        size_3 = next((item for item in size_data if item['size_number'] == 3), {})
        size_4 = next((item for item in size_data if item['size_number'] == 4), {})
        size_5 = next((item for item in size_data if item['size_number'] == 5), {})
        
        # 查询外观检查数据
        cursor.execute("""
            SELECT 
                check_number, 
                COALESCE(NULLIF(check_result, ''), '/') as check_result, 
                COALESCE(note, '') as note,
                COALESCE(other_info, '') as other_info
            FROM material_sample_appearance
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (sample_id,))
        appearance_data = cursor.fetchall()
        
        # 查询功能检查数据
        cursor.execute("""
            SELECT check_number, check_result, note, burnin_info, electrical_info,
                   tests_info, other_test, other_info
            FROM material_sample_function
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY check_number
        """, (sample_id,))
        function_data = cursor.fetchall()
        
        # 查询问题记录和图片路径
        cursor.execute("""
            SELECT q.question_number, q.question_text, q.image_path
            FROM material_sample_questions q
            WHERE q.form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            ORDER BY q.question_number
        """, (sample_id,))
        
        # 修复：先获取完整结果集
        question_rows = cursor.fetchall()
        
        # 修改：确保问题点数据连续，处理可能存在的"洞"
        questions = []
        question_images = {}
        valid_question_numbers = []
        
        # 获取配置中的图片存储基础路径
        base_image_path = get_image_base_path()
        
        # 创建目标文件夹路径，使用配置中的图片存储路径
        # 确保temp_uploads_dir目录存在
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads')
        os.makedirs(temp_uploads_dir, exist_ok=True)
        
        # 检查是否存在相同报告编码的文件夹
        target_folder = os.path.join(temp_uploads_dir, sample_id)
        
        # 每次加载页面时，清理旧的临时文件夹
        if os.path.exists(target_folder):
            # 选项1: 删除旧文件夹并重新创建
            try:
                print(f"清理临时文件夹: {target_folder}")
                shutil.rmtree(target_folder)
            except Exception as e:
                print(f"清理临时文件夹失败: {e}")
                # 清理失败时，尝试清空文件夹内的文件
                try:
                    for item in os.listdir(target_folder):
                        item_path = os.path.join(target_folder, item)
                        if os.path.isfile(item_path):
                            os.unlink(item_path)
                            print(f"已删除文件: {item_path}")
                        else:
                            shutil.rmtree(item_path)
                            print(f"已删除子文件夹: {item_path}")
                except Exception as clean_error:
                    print(f"清空文件夹内容失败: {clean_error}")

        # 重新创建目标文件夹
        os.makedirs(target_folder, exist_ok=True)
        
        # 修改网站访问路径基础，适配新的路由
        site_path_base = f"/Material_Sample_Confirmation_Form_modify/images/MSCF_modify_temp_uploads/{sample_id}"
        
        # 首先创建一个字典来保存所有问题数据
        question_data = {}
        max_question_number = 0
        last_content_position = 0  # 跟踪最后一个有内容的位置
        
        for row in question_rows:
            question_number = row['question_number']
            max_question_number = max(max_question_number, question_number)
            
            # 保存问题数据到字典中
            question_data[question_number] = {
                'question_text': row['question_text'] if row['question_text'] else '',
                'image_path': row['image_path']
            }
            
            # 如果问题有文本或图片，更新最后内容位置
            if row['question_text'] or row['image_path']:
                last_content_position = max(last_content_position, question_number)
            
            # 处理图片
            if row['image_path']:
                # 确保路径格式正确
                image_path = row['image_path'].replace('/', '\\')
                
                # 获取基础图片路径
                base_image_path = get_image_base_path()
                
                # 检查路径是否存在，尝试多种方式查找
                found_path = None
                
                # 1. 检查原始路径
                if os.path.exists(image_path):
                    found_path = image_path
                else:
                    # 2. 尝试从static目录开始查找
                    static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', image_path.lstrip('\\'))
                    if os.path.exists(static_path):
                        found_path = static_path
                    else:
                        # 3. 尝试从绝对路径查找
                        abs_path = os.path.abspath(image_path)
                        if os.path.exists(abs_path):
                            found_path = abs_path
                        else:
                            # 4. 尝试从图片存储基础路径查找
                            base_path = os.path.join(base_image_path, image_path.lstrip('\\'))
                            if os.path.exists(base_path):
                                found_path = base_path
                
                # 如果找到了图片，复制到临时位置
                if found_path:
                    file_name = os.path.basename(found_path)
                    dest_path = os.path.join(target_folder, file_name)
                    
                    # 复制图片到目标文件夹
                    shutil.copy2(found_path, dest_path)
                    
                    # 保存图片信息，包括原始路径和原始目录路径
                    question_images[question_number] = {
                        'dir_path': os.path.dirname(found_path),
                        'file_name': file_name,
                        'full_path': f'{site_path_base}/{file_name}',
                        'original_path': found_path,  # 保存原始路径供表单提交时使用
                        'original_dir': os.path.dirname(found_path)  # 保存原始目录路径
                    }
                
                    print(f"原始图片路径: {row['image_path']}")
                    print(f"找到图片路径: {found_path}")
                    print(f"复制到: {dest_path}")
                    print(f"网站访问路径: {question_images[question_number]['full_path']}")
                    print(f"原始目录: {question_images[question_number]['original_dir']}")
        
        # 计算需要显示的最小问题点数量
        # 初始最少显示6个单元格（2行，每行3个）
        min_display_count = 6
        
        # 如果有内容的最后位置大于最小显示，则使用它
        if last_content_position > min_display_count:
            # 向上取整到3的倍数，确保完整的行显示
            display_count = (last_content_position + 2) // 3 * 3
        else:
            display_count = min_display_count
        
        # 确保数据连续性，创建完整的问题列表
        # 只创建到 display_count 的问题点
        for i in range(1, display_count + 1):
            if i in question_data:
                questions.append({
                    'question_number': i,
                    'question_text': question_data[i]['question_text']
                })
                if question_data[i]['question_text'] or i in question_images:
                    valid_question_numbers.append(i)
            else:
                # 对于不存在的问题编号，创建空记录
                questions.append({
                    'question_number': i,
                    'question_text': ''
                })
        
        try:
            # 检查function_data是否包含必要的记录
            function_5_record = None
            for item in function_data:
                if item['check_number'] == 5:
                    function_5_record = item
                    break
            
            # 安全处理function_5_tests
            function_5_tests = []
            if function_5_record and function_5_record.get('tests_info'):
                try:
                    function_5_tests = [test.strip('" ') for test in function_5_record['tests_info'].split(',')]
                except Exception as e:
                    print(f"处理function_5_tests时出错: {e}")
                    function_5_tests = []

            return render_template(
                'Material_Sample_Confirmation_Form_modify.html',
                report_code=base_info['report_code'],
                supplier=base_info['supplier'],
                inspection_date=base_info['inspection_date'],
                sample_count=base_info['sample_count'] if base_info['sample_count'] is not None else '',
                inspector=base_info['inspector'],
                material_number=base_info['material_number'],
                graph_number=base_info['graph_number'],
                material_name=base_info['material_name'],
                drawing_version=base_info['drawing_version'],
                material_texture=base_info['material_texture'],
                surface_processing=base_info['surface_processing'],
                sample_status_list = base_info['sample_status'].split(',') if base_info['sample_status'] else [],
                other_textbox_value = base_info['other_textbox'],
                final_judgment=base_info['final_judgment'],
                opinion=base_info['opinion'],
                review=base_info['review'],
                size_row_count=size_row_count,
                question_images=question_images,
                valid_question_numbers=valid_question_numbers,
                enable_image_reordering=enable_image_reordering,  # 添加重排序设置
                using_reordering_mode="no",  # 默认不使用重排序模式

                # 尺寸1-3的字段
                size_1_position=size_1.get('position', ''),
                size_1_value=size_1.get('value', ''),
                size_1_min=size_1.get('min_value', ''),
                size_1_max=size_1.get('max_value', ''),
                size_1_measure_1=size_1.get('measure_1', ''),
                size_1_measure_2=size_1.get('measure_2', ''),
                size_1_measure_3=size_1.get('measure_3', ''),
                size_1_measure_4=size_1.get('measure_4', ''),
                size_1_measure_5=size_1.get('measure_5', ''),
                size_1_check=size_1.get('check_result', ''),
                size_1_note=size_1.get('note', ''),
                
                size_2_position=size_2.get('position', ''),
                size_2_value=size_2.get('value', ''),
                size_2_min=size_2.get('min_value', ''),
                size_2_max=size_2.get('max_value', ''),
                size_2_measure_1=size_2.get('measure_1', ''),
                size_2_measure_2=size_2.get('measure_2', ''),
                size_2_measure_3=size_2.get('measure_3', ''),
                size_2_measure_4=size_2.get('measure_4', ''),
                size_2_measure_5=size_2.get('measure_5', ''),
                size_2_check=size_2.get('check_result', ''),
                size_2_note=size_2.get('note', ''),
                
                size_3_position=size_3.get('position', ''),
                size_3_value=size_3.get('value', ''),
                size_3_min=size_3.get('min_value', ''),
                size_3_max=size_3.get('max_value', ''),
                size_3_measure_1=size_3.get('measure_1', ''),
                size_3_measure_2=size_3.get('measure_2', ''),
                size_3_measure_3=size_3.get('measure_3', ''),
                size_3_measure_4=size_3.get('measure_4', ''),
                size_3_measure_5=size_3.get('measure_5', ''),
                size_3_check=size_3.get('check_result', ''),
                size_3_note=size_3.get('note', ''),
                
                size_4_position=size_4.get('position', ''),
                size_4_value=size_4.get('value', ''),
                size_4_min=size_4.get('min_value', ''),
                size_4_max=size_4.get('max_value', ''),
                size_4_measure_1=size_4.get('measure_1', ''),
                size_4_measure_2=size_4.get('measure_2', ''),
                size_4_measure_3=size_4.get('measure_3', ''),
                size_4_measure_4=size_4.get('measure_4', ''),
                size_4_measure_5=size_4.get('measure_5', ''),
                size_4_check=size_4.get('check_result', ''),
                size_4_note=size_4.get('note', ''),
                
                size_5_position=size_5.get('position', ''),
                size_5_value=size_5.get('value', ''),
                size_5_min=size_5.get('min_value', ''),
                size_5_max=size_5.get('max_value', ''),
                size_5_measure_1=size_5.get('measure_1', ''),
                size_5_measure_2=size_5.get('measure_2', ''),
                size_5_measure_3=size_5.get('measure_3', ''),
                size_5_measure_4=size_5.get('measure_4', ''),
                size_5_measure_5=size_5.get('measure_5', ''),
                size_5_check=size_5.get('check_result', ''),
                size_5_note=size_5.get('note', ''),
              
                # 动态数据
                size_data=size_data,
                appearance_data=appearance_data,
                function_data=function_data,
                questions=questions,
                
                # 功能检查数据 - 更安全的获取方式
                function_1_check=next((item.get('check_result', '/') for item in function_data if item.get('check_number') == 1), '/'),
                function_1_note=next((item.get('note', '') for item in function_data if item.get('check_number') == 1), ''),
                function_2_check=next((item.get('check_result', '/') for item in function_data if item.get('check_number') == 2), '/'),
                function_2_note=next((item.get('note', '') for item in function_data if item.get('check_number') == 2), ''),
                function_3_check=next((item.get('check_result', '/') for item in function_data if item.get('check_number') == 3), '/'),
                function_3_note=next((item.get('note', '') for item in function_data if item.get('check_number') == 3), ''),
                function_4_check=next((item.get('check_result', '/') for item in function_data if item.get('check_number') == 4), '/'),
                function_4_note=next((item.get('note', '') for item in function_data if item.get('check_number') == 4), ''),
                function_4_burnin=next((item.get('burnin_info', '') for item in function_data if item.get('check_number') == 4), ''),
                function_4_electrical=next((item.get('electrical_info', '') for item in function_data if item.get('check_number') == 4), ''),
                function_5_check=next((item.get('check_result', '/') for item in function_data if item.get('check_number') == 5), '/'),
                function_5_note=next((item.get('note', '') for item in function_data if item.get('check_number') == 5), ''),
                function_5_tests=function_5_tests,  # 使用上面安全处理过的数据
                function_5_other_test=next((item.get('other_test', '') for item in function_data if item.get('check_number') == 5), ''),
                function_6_check=next((item.get('check_result', '/') for item in function_data if item.get('check_number') == 6), '/'),
                function_6_note=next((item.get('note', '') for item in function_data if item.get('check_number') == 6), ''),
                function_6_other=next((item.get('other_info', '') for item in function_data if item.get('check_number') == 6), '')
            )
        except Exception as render_error:
            import traceback
            error_detail = traceback.format_exc()
            print(f"渲染模板时发生错误: {render_error}")
            print(f"错误详情: {error_detail}")
            # 返回一个简单的错误页面，显示错误详情
            return f"""
            <html>
            <head><title>渲染错误</title></head>
            <body>
                <h1>渲染模板时发生错误</h1>
                <p>{str(render_error)}</p>
                <pre>{error_detail}</pre>
                <p><a href="/">返回首页</a></p>
            </body>
            </html>
            """, 500
        
    except mysql.connector.Error as err:
        return jsonify({"error": f"数据库错误: {str(err)}"}), 500
    except Exception as e:
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500
    finally:
        # 确保在关闭游标前处理所有未读取的结果
        if cursor and cursor.with_rows:
            try:
                cursor.fetchall()  # 消费所有剩余结果
                print("消费了剩余的未读结果")
            except:
                pass  # 忽略任何可能的错误
        
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@Material_Sample_Confirmation_Form_modify_bp.route('/delete_image', methods=['POST'])
def delete_image():
    """删除问题对应的图片（仅删除临时目录中的图片）"""
    conn = None
    cursor = None
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "无效的请求数据"}), 400
            
        report_code = data.get('report_code')
        question_number = data.get('question_number')
        
        if not report_code or not question_number:
            return jsonify({"success": False, "message": "缺少必要参数"}), 400
            
        print(f"处理图片删除请求: 报告编码={report_code}, 问题编号={question_number}")
        
        # 获取配置中的图片存储基础路径
        base_image_path = get_image_base_path()
        
        # 获取临时文件夹路径 - 修改为新结构
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads', report_code)
        
        # 检查临时目录是否存在
        if not os.path.exists(temp_uploads_dir):
            print(f"临时目录不存在: {temp_uploads_dir}")
            return jsonify({"success": True, "message": "没有需要删除的临时图片"}), 200
        
        # 查找与问题编号匹配的图片
        deleted_files = []
        for filename in os.listdir(temp_uploads_dir):
            # 尝试从文件名中提取问题编号
            try:
                # 假设文件名格式为 "问题编号_日期_时间.扩展名"
                parts = filename.split('_')
                if parts and int(parts[0]) == int(question_number):
                    file_to_delete = os.path.join(temp_uploads_dir, filename)
                    os.remove(file_to_delete)
                    deleted_files.append(filename)
                    print(f"已删除临时图片: {file_to_delete}")
            except Exception:
                # 使用更宽松的文件名匹配逻辑
                str_question_number = str(question_number)
                if (f"question_{str_question_number}" in filename or 
                    f"_{str_question_number}." in filename or
                    f"_{str_question_number}_" in filename or
                    filename.startswith(f"{str_question_number}_")):
                    file_to_delete = os.path.join(temp_uploads_dir, filename)
                    try:
                        os.remove(file_to_delete)
                        deleted_files.append(filename)
                        print(f"已删除临时图片: {file_to_delete}")
                    except Exception as e:
                        print(f"删除临时图片失败: {file_to_delete}, 错误: {e}")
        
        # 注意：在修改模式下，不再更新数据库中的图片路径，只删除临时文件
        # 数据库路径更新将在表单提交时处理
        
        if deleted_files:
            return jsonify({
                "success": True, 
                "message": f"已删除 {len(deleted_files)} 个临时图片文件",
                "deleted_files": deleted_files
            })
        else:
            return jsonify({"success": True, "message": "没有找到匹配的临时图片文件"})
            
    except Exception as e:
        import traceback
        print(f"删除图片时发生错误: {e}")
        print(traceback.format_exc())
        return jsonify({"success": False, "message": f"发生错误: {str(e)}"}), 500
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

@Material_Sample_Confirmation_Form_modify_bp.route('/submit', methods=['POST'])
def handle_legacy_submit():
    """处理/submit的旧式提交，重定向到update路由"""
    print("检测到对/submit的请求，重定向到update")
    return update_form()  # 直接调用update_form函数处理请求

@Material_Sample_Confirmation_Form_modify_bp.route('/update', methods=['POST'])
def update_form():
    print("成功匹配到/Material_Sample_Confirmation_Form_modify/update路由")
    conn = None
    cursor = None
    
    try:
        # 获取表单数据
        report_code = request.form.get('report_code')
        
        if not report_code:
            return jsonify({"success": False, "message": "缺少报告编码"}), 400
        
        # 创建数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查询表单ID
        cursor.execute("SELECT id FROM material_sample_confirmation_form WHERE report_code = %s", (report_code,))
        form_result = cursor.fetchone()
        
        if not form_result:
            return jsonify({"success": False, "message": f"未找到报告编码为 {report_code} 的表单"}), 404
        
        form_id = form_result[0]
        
        # 获取重排序设置
        enable_image_reordering = 'yes'  # 默认启用重排序
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    enable_image_reordering = config.get('enable_image_reordering', 'yes')
                    print(f"从配置中获取图片重排序设置: {enable_image_reordering}")
        except Exception as e:
            print(f"读取图片重排序设置时出错: {e}")
        
        sample_status = request.form.getlist('sample_status[]')  # 获取所有选中的复选框值
        other_textbox = request.form.get('other_textbox', '')    # 获取其他文本框的内容

        # 将复选框值转为字符串存储
        sample_status_str = ','.join(sample_status) if sample_status else ''
        
        # 更新主表记录
        cursor.execute("""UPDATE material_sample_confirmation_form 
            SET supplier = %s, 
                inspection_date = %s, 
                sample_count = %s, 
                inspector = %s, 
                material_number = %s, 
                graph_number = %s, 
                material_name = %s, 
                drawing_version = %s, 
                material_texture = %s, 
                surface_processing = %s, 
                sample_status = %s, 
                other_textbox = %s, 
                final_judgment = %s, 
                opinion = %s, 
                review = %s
            WHERE report_code = %s
        """, (
            request.form['supplier'],
            request.form['inspection_date'],
            request.form['sample_count'],
            request.form['inspector'],
            request.form['material_number'],
            request.form['graph_number'],
            request.form['material_name'],
            request.form['drawing_version'],
            request.form['material_texture'],
            request.form['surface_processing'],
            sample_status_str,
            request.form.get('other_textbox', ''),
            request.form.get('final_judgment', ''),
            request.form.get('opinion', ''),
            request.form.get('review', ''),
            report_code
        ))
        
        # 删除旧的尺寸数据
        cursor.execute("DELETE FROM material_sample_size_data WHERE form_id = %s", (form_id,))
        
        # 收集所有有数据的尺寸行
        size_data_rows = []
        for i in range(1, 31):
            position = request.form.get(f'size_{i}_position', '')
            value = request.form.get(f'size_{i}_value', '')
            min_value = request.form.get(f'size_{i}_min', '')
            max_value = request.form.get(f'size_{i}_max', '')
            measures = [
                request.form.get(f'size_{i}_measure_{j}', '') for j in range(1,6)
            ]
            check_result = request.form.get(f'size_{i}_check', '')
            note = request.form.get(f'size_{i}_note', '')
            
            # 如果所有重要字段都为空，跳过这一行（但保留前3行）
            if i <= 3 or not (not position and not value and not min_value and not max_value and all(not m for m in measures) and not check_result):
                # 有数据的行或前3行添加到列表
                size_data_rows.append({
                    'original_number': i,
                    'position': position,
                    'value': value,
                    'min_value': min_value,
                    'max_value': max_value,
                    'measure_1': measures[0],
                    'measure_2': measures[1],
                    'measure_3': measures[2],
                    'measure_4': measures[3],
                    'measure_5': measures[4],
                    'check_result': check_result,
                    'note': note
                })
        
        # 分离前3行和其他行
        fixed_rows = [row for row in size_data_rows if row['original_number'] <= 3]
        other_rows = [row for row in size_data_rows if row['original_number'] > 3]
        
        # 确保前3行都存在（如果不存在则添加空行）
        existing_numbers = {row['original_number'] for row in fixed_rows}
        for i in range(1, 4):
            if i not in existing_numbers:
                empty_row = {
                    'original_number': i,
                    'position': '',
                    'value': '',
                    'min_value': '',
                    'max_value': '',
                    'measure_1': '',
                    'measure_2': '',
                    'measure_3': '',
                    'measure_4': '',
                    'measure_5': '',
                    'check_result': '',
                    'note': ''
                }
                fixed_rows.append(empty_row)
        
        # 按行号排序固定行
        fixed_rows.sort(key=lambda x: x['original_number'])
        
        # 检查第1行是否为空，如果为空且第2行有数据，则进行前移
        row_1 = next((row for row in fixed_rows if row['original_number'] == 1), None)
        row_2 = next((row for row in fixed_rows if row['original_number'] == 2), None)
        row_3 = next((row for row in fixed_rows if row['original_number'] == 3), None)
        
        # 检查第1行是否真的为空（所有重要字段都为空）
        if row_1 and not (row_1.get('position') or row_1.get('value') or row_1.get('min_value') or row_1.get('max_value') or 
                        (row_1.get('measure_1') or row_1.get('measure_2') or row_1.get('measure_3') or row_1.get('measure_4') or row_1.get('measure_5')) or row_1.get('check_result')):
            
            # 第1行为空，检查第2行是否有数据
            if row_2 and (row_2.get('position') or row_2.get('value') or row_2.get('min_value') or row_2.get('max_value') or 
                        (row_2.get('measure_1') or row_2.get('measure_2') or row_2.get('measure_3') or row_2.get('measure_4') or row_2.get('measure_5')) or row_2.get('check_result')):
                
                # 将第2行数据移到第1行
                row_1['position'] = row_2['position']
                row_1['value'] = row_2['value']
                row_1['min_value'] = row_2['min_value']
                row_1['max_value'] = row_2['max_value']
                row_1['measure_1'] = row_2['measure_1']
                row_1['measure_2'] = row_2['measure_2']
                row_1['measure_3'] = row_2['measure_3']
                row_1['measure_4'] = row_2['measure_4']
                row_1['measure_5'] = row_2['measure_5']
                row_1['check_result'] = row_2['check_result']
                row_1['note'] = row_2['note']
                
                # 将第3行数据移到第2行
                row_2['position'] = row_3['position']
                row_2['value'] = row_3['value']
                row_2['min_value'] = row_3['min_value']
                row_2['max_value'] = row_3['max_value']
                row_2['measure_1'] = row_3['measure_1']
                row_2['measure_2'] = row_3['measure_2']
                row_2['measure_3'] = row_3['measure_3']
                row_2['measure_4'] = row_3['measure_4']
                row_2['measure_5'] = row_3['measure_5']
                row_2['check_result'] = row_3['check_result']
                row_2['note'] = row_3['note']
                
                # 清空第3行
                row_3['position'] = ''
                row_3['value'] = ''
                row_3['min_value'] = ''
                row_3['max_value'] = ''
                row_3['measure_1'] = ''
                row_3['measure_2'] = ''
                row_3['measure_3'] = ''
                row_3['measure_4'] = ''
                row_3['measure_5'] = ''
                row_3['check_result'] = '/'
                row_3['note'] = ''
                
                # 如果有第4行数据，移到第3行
                if other_rows and len(other_rows) > 0:
                    row_4 = other_rows[0]  # 第一个其他行就是第4行
                    
                    # 将第4行数据移到第3行
                    row_3['position'] = row_4['position']
                    row_3['value'] = row_4['value']
                    row_3['min_value'] = row_4['min_value']
                    row_3['max_value'] = row_4['max_value']
                    row_3['measure_1'] = row_4['measure_1']
                    row_3['measure_2'] = row_4['measure_2']
                    row_3['measure_3'] = row_4['measure_3']
                    row_3['measure_4'] = row_4['measure_4']
                    row_3['measure_5'] = row_4['measure_5']
                    row_3['check_result'] = row_4['check_result']
                    row_3['note'] = row_4['note']
                    
                    # 移除第4行数据
                    other_rows.pop(0)
            
            # 如果第2行也为空，检查第3行是否有数据
            elif row_2 and all(not row_2.get(field, '') or row_2.get(field, '') == '/' for field in 
                             ['position', 'value', 'min_value', 'max_value']) and \
                 not (row_2.get('measure_1') or row_2.get('measure_2') or row_2.get('measure_3') or row_2.get('measure_4') or row_2.get('measure_5')) and \
                 row_3 and (row_3.get('position') or row_3.get('value') or row_3.get('min_value') or row_3.get('max_value') or 
                           (row_3.get('measure_1') or row_3.get('measure_2') or row_3.get('measure_3') or row_3.get('measure_4') or row_3.get('measure_5')) or row_3.get('check_result')):
                
                # 将第3行数据移到第1行
                row_1['position'] = row_3['position']
                row_1['value'] = row_3['value']
                row_1['min_value'] = row_3['min_value']
                row_1['max_value'] = row_3['max_value']
                row_1['measure_1'] = row_3['measure_1']
                row_1['measure_2'] = row_3['measure_2']
                row_1['measure_3'] = row_3['measure_3']
                row_1['measure_4'] = row_3['measure_4']
                row_1['measure_5'] = row_3['measure_5']
                row_1['check_result'] = row_3['check_result']
                row_1['note'] = row_3['note']
                
                # 清空第2行和第3行
                for row in [row_2, row_3]:
                    row['position'] = ''
                    row['value'] = ''
                    row['min_value'] = ''
                    row['max_value'] = ''
                    row['measure_1'] = ''
                    row['measure_2'] = ''
                    row['measure_3'] = ''
                    row['measure_4'] = ''
                    row['measure_5'] = ''
                    row['check_result'] = ''
                    row['note'] = ''
                
                # 如果有第4行数据，移到第2行
                if other_rows and len(other_rows) > 0:
                    row_4 = other_rows[0]
                    
                    # 将第4行数据移到第2行
                    row_2['position'] = row_4['position']
                    row_2['value'] = row_4['value']
                    row_2['min_value'] = row_4['min_value']
                    row_2['max_value'] = row_4['max_value']
                    row_2['measure_1'] = row_4['measure_1']
                    row_2['measure_2'] = row_4['measure_2']
                    row_2['measure_3'] = row_4['measure_3']
                    row_2['measure_4'] = row_4['measure_4']
                    row_2['measure_5'] = row_4['measure_5']
                    row_2['check_result'] = row_4['check_result']
                    row_2['note'] = row_4['note']
                    
                    # 移除第4行数据
                    other_rows.pop(0)
                    
                    # 如果还有第5行数据，移到第3行
                    if other_rows and len(other_rows) > 0:
                        row_5 = other_rows[0]
                        
                        # 将第5行数据移到第3行
                        row_3['position'] = row_5['position']
                        row_3['value'] = row_5['value']
                        row_3['min_value'] = row_5['min_value']
                        row_3['max_value'] = row_5['max_value']
                        row_3['measure_1'] = row_5['measure_1']
                        row_3['measure_2'] = row_5['measure_2']
                        row_3['measure_3'] = row_5['measure_3']
                        row_3['measure_4'] = row_5['measure_4']
                        row_3['measure_5'] = row_5['measure_5']
                        row_3['check_result'] = row_5['check_result']
                        row_3['note'] = row_5['note']
                        
                        # 移除第5行数据
                        other_rows.pop(0)
                
        # 检查第2行是否为空，如果为空且第3行有数据，则进行前移
        elif row_2 and not (row_2.get('position') or row_2.get('value') or row_2.get('min_value') or row_2.get('max_value') or 
                          (row_2.get('measure_1') or row_2.get('measure_2') or row_2.get('measure_3') or row_2.get('measure_4') or row_2.get('measure_5')) or row_2.get('check_result')):
            
            # 第2行为空，检查第3行是否有数据
            if row_3 and (row_3.get('position') or row_3.get('value') or row_3.get('min_value') or row_3.get('max_value') or 
                        (row_3.get('measure_1') or row_3.get('measure_2') or row_3.get('measure_3') or row_3.get('measure_4') or row_3.get('measure_5')) or row_3.get('check_result')):
                
                # 将第3行数据移到第2行
                row_2['position'] = row_3['position']
                row_2['value'] = row_3['value']
                row_2['min_value'] = row_3['min_value']
                row_2['max_value'] = row_3['max_value']
                row_2['measure_1'] = row_3['measure_1']
                row_2['measure_2'] = row_3['measure_2']
                row_2['measure_3'] = row_3['measure_3']
                row_2['measure_4'] = row_3['measure_4']
                row_2['measure_5'] = row_3['measure_5']
                row_2['check_result'] = row_3['check_result']
                row_2['note'] = row_3['note']
                
                # 清空第3行
                row_3['position'] = ''
                row_3['value'] = ''
                row_3['min_value'] = ''
                row_3['max_value'] = ''
                row_3['measure_1'] = ''
                row_3['measure_2'] = ''
                row_3['measure_3'] = ''
                row_3['measure_4'] = ''
                row_3['measure_5'] = ''
                row_3['check_result'] = ''
                row_3['note'] = ''
                
                # 如果有第4行数据，移到第3行
                if other_rows and len(other_rows) > 0:
                    row_4 = other_rows[0]  # 第一个其他行就是第4行
                    
                    # 将第4行数据移到第3行
                    row_3['position'] = row_4['position']
                    row_3['value'] = row_4['value']
                    row_3['min_value'] = row_4['min_value']
                    row_3['max_value'] = row_4['max_value']
                    row_3['measure_1'] = row_4['measure_1']
                    row_3['measure_2'] = row_4['measure_2']
                    row_3['measure_3'] = row_4['measure_3']
                    row_3['measure_4'] = row_4['measure_4']
                    row_3['measure_5'] = row_4['measure_5']
                    row_3['check_result'] = row_4['check_result']
                    row_3['note'] = row_4['note']
                    
                    # 移除第4行数据
                    other_rows.pop(0)
                    
                    # 如果还有第5行数据，移到第3行
                    if other_rows and len(other_rows) > 0:
                        row_5 = other_rows[0]
                        
                        # 将第5行数据移到第3行
                        row_3['position'] = row_5['position']
                        row_3['value'] = row_5['value']
                        row_3['min_value'] = row_5['min_value']
                        row_3['max_value'] = row_5['max_value']
                        row_3['measure_1'] = row_5['measure_1']
                        row_3['measure_2'] = row_5['measure_2']
                        row_3['measure_3'] = row_5['measure_3']
                        row_3['measure_4'] = row_5['measure_4']
                        row_3['measure_5'] = row_5['measure_5']
                        row_3['check_result'] = row_5['check_result']
                        row_3['note'] = row_5['note']
                        
                        # 移除第5行数据
                        other_rows.pop(0)
                
        # 合并数据并重新排序
        size_data = fixed_rows + other_rows
        # 修复：确保每个元素都有 size_number 字段，如果没有则使用 original_number
        for i, row in enumerate(size_data):
            if 'size_number' not in row:
                if 'original_number' in row:
                    row['size_number'] = row['original_number']
                else:
                    row['size_number'] = i + 1  # 默认使用索引+1作为排序键
        
        # 使用安全的排序方式
        try:
            size_data.sort(key=lambda x: x['size_number'])
        except Exception as e:
            print(f"排序尺寸数据时出错: {e}，使用默认顺序")
        
        # 保存尺寸数据 - 前3行保持原编号
        for row in fixed_rows:
            size_number = row['original_number']
            position = row['position']
            value = row['value']
            min_value = row['min_value']
            max_value = row['max_value']
            measures = [
                row['measure_1'], 
                row['measure_2'], 
                row['measure_3'], 
                row['measure_4'], 
                row['measure_5']
            ]
            check_result = row['check_result']
            note = row['note']
            
            # 处理空字符串，转换为None(NULL)
            value = None if value == '' else value
            min_value = None if min_value == '' else min_value
            max_value = None if max_value == '' else max_value
            measures = [None if m == '' else m for m in measures]
            
            cursor.execute("""
                INSERT INTO material_sample_size_data 
                (form_id, size_number, position, value, min_value, max_value, 
                 measure_1, measure_2, measure_3, measure_4, measure_5, 
                 check_result, note)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                form_id, size_number, position, value, min_value, max_value,
                *measures, check_result, note
            ))
            if cursor.with_rows:
                cursor.fetchall()
                
        # 保存尺寸数据 - 第4行及以后使用新编号
        for i, row in enumerate(other_rows):
            size_number = i + 4  # 直接计算行号，从4开始
            position = row['position']
            value = row['value']
            min_value = row['min_value']
            max_value = row['max_value']
            measures = [
                row['measure_1'], 
                row['measure_2'], 
                row['measure_3'], 
                row['measure_4'], 
                row['measure_5']
            ]
            check_result = row['check_result']
            note = row['note']
            
            # 处理空字符串，转换为None(NULL)
            value = None if value == '' else value
            min_value = None if min_value == '' else min_value
            max_value = None if max_value == '' else max_value
            measures = [None if m == '' else m for m in measures]
            
            cursor.execute("""
                INSERT INTO material_sample_size_data 
                (form_id, size_number, position, value, min_value, max_value, 
                 measure_1, measure_2, measure_3, measure_4, measure_5, 
                 check_result, note)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                form_id, size_number, position, value, min_value, max_value,
                *measures, check_result, note
            ))
            if cursor.with_rows:
                cursor.fetchall()
        
        # 删除旧的外观检查数据
        cursor.execute("DELETE FROM material_sample_appearance WHERE form_id = %s", (form_id,))
        
        # 保存外观检查
        for i in range(1,5):
            check_number = i
            check_result = request.form.get(f'appearance_{i}_check', '')
            note = request.form.get(f'appearance_{i}_note', '')
            other_info = request.form.get(f'appearance_{i}_other', '')
            cursor.execute("""
                INSERT INTO material_sample_appearance 
                (form_id, check_number, check_result, note, other_info)
                VALUES (%s, %s, %s, %s, %s)
            """, (form_id, check_number, check_result, note, other_info))
            if cursor.with_rows:
                cursor.fetchall()
        
        # 删除旧的功能检查数据
        cursor.execute("DELETE FROM material_sample_function WHERE form_id = %s", (form_id,))
        
        # 保存功能检查
        for i in range(1,7):
            check_number = i
            check_result = request.form.get(f'function_{i}_check', '')
            note = request.form.get(f'function_{i}_note', '')
            burnin = request.form.get(f'function_{i}_burnin', '')
            electrical = request.form.get(f'function_{i}_electrical', '')
            tests_list = request.form.getlist(f'function_{i}_tests[]')
            tests = ','.join(tests_list)
            other_test = request.form.get(f'function_{i}_other_test', '')
            other_info = request.form.get(f'function_{i}_other', '')
            cursor.execute("""
                INSERT INTO material_sample_function 
                (form_id, check_number, check_result, note, burnin_info, 
                electrical_info, tests_info, other_test, other_info)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (form_id, check_number, check_result, note, burnin, electrical, tests, other_test, other_info))
            if cursor.with_rows:
                cursor.fetchall()
        
        # 处理问题记录
        # 先删除旧的记录
        cursor.execute("DELETE FROM material_sample_questions WHERE form_id = %s", (form_id,))
        
        print("开始处理问题点数据...")
        # 记录已处理的问题编号
        processed_question_numbers = set()
        
        # 检查是否使用了重排序模式（从表单中获取）
        using_reordering_mode = request.form.get('using_reordering_mode', 'no')
        if using_reordering_mode.lower() == 'yes':
            print("表单提交时使用了重排序模式标记")
            # 如果表单提交时使用了重排序模式，强制启用重排序设置
            enable_image_reordering = 'yes'
            print("根据表单提交标记，强制启用重排序设置")
            
        # 获取问题点图片状态信息
        question_images_status = {}
        try:
            if 'question_images_status' in request.form:
                question_images_status = json.loads(request.form.get('question_images_status', '{}'))
                print(f"从表单获取到问题点图片状态信息: {len(question_images_status)} 个问题点有图片")
        except Exception as e:
            print(f"解析问题点图片状态信息失败: {e}")
        
        # 检查是否有前端传递的问题点映射关系JSON
        frontend_mapping = {}
        if 'question_mapping_json' in request.form:
            try:
                frontend_mapping = json.loads(request.form.get('question_mapping_json', '{}'))
                print(f"从前端接收到问题点映射关系JSON: {frontend_mapping}")
            except Exception as e:
                print(f"解析前端传递的问题点映射关系JSON失败: {e}")
        
        # 检查是否有临时存储的问题点映射关系
        stored_mapping_data = temp_question_data_store.get(report_code)
        
        # 如果有前端传递的映射关系，更新临时存储中的映射关系
        if frontend_mapping and stored_mapping_data:
            stored_mapping_data['question_mapping'] = frontend_mapping
            print("已使用前端传递的映射关系更新临时存储")
        if stored_mapping_data:
            # 如果有前端传递的映射关系，优先使用前端的
            if frontend_mapping:
                stored_mapping_data['question_mapping'] = frontend_mapping
                print("已使用前端传递的映射关系更新临时存储")
            
            print(f"找到临时存储的问题点映射数据，使用存储的映射关系进行处理")
            question_data = stored_mapping_data.get('question_data', [])
            question_mapping = stored_mapping_data.get('question_mapping', {})
            
            # 根据配置决定是否重排序问题点
            if enable_image_reordering.lower() == 'yes':
                print("启用问题点图片重排序模式 - 按顺序重新编号问题点")
                # 重新编号问题点，确保连续且无空位
                reordered_data = []
                for i, data in enumerate(question_data):
                    if data:  # 如果有数据
                        # 关键修复：检查问题点是否为空（无文本且无图片）
                        question_text = data.get('text', '').strip()
                        image_src = data.get('imageSrc', '')
                        
                        if question_text or image_src:  # 只添加非空的问题点
                            reordered_data.append(data)
                        else:
                            print(f"跳过空问题点: 索引={i}, 原始编号={data.get('originalNumber')}")
                
                # 使用重新排序后的数据
                question_data = reordered_data
                
                # 初始化问题点映射关系
                question_mapping = {}
                
                # 构建新的映射关系，确保前端传递的映射关系被正确应用
                if stored_mapping_data and 'question_mapping' in stored_mapping_data:
                    question_mapping = stored_mapping_data.get('question_mapping', {})
                    print(f"从临时存储获取问题点映射关系: {len(question_mapping)} 条映射")
                
                # 如果前端传递了映射关系，优先使用前端的映射关系
                if frontend_mapping:
                    question_mapping = frontend_mapping
                    print(f"使用前端传递的映射关系进行重排序: {len(frontend_mapping)} 条映射")
                
                # 如果没有任何映射关系，创建默认映射（保持原始编号）
                if not question_mapping:
                    print("未找到任何映射关系，创建默认映射")
                    for i, data in enumerate(question_data):
                        if data and 'originalNumber' in data:
                            original_number = data['originalNumber']
                            question_mapping[original_number] = i + 1
                            print(f"创建默认映射: {original_number} -> {i + 1}")
                
                # 修复：重排序模式下，不再无条件清空数据库中的所有问题点记录
                # 而是先检索现有记录，保存图片路径信息，然后在更新时保留这些信息
                existing_images = {}
                cursor.execute("""
                    SELECT question_number, image_path 
                    FROM material_sample_questions 
                    WHERE form_id = %s AND image_path IS NOT NULL AND image_path != ''
                """, (form_id,))
                
                for row in cursor.fetchall():
                    question_number, image_path = row
                    if image_path:
                        existing_images[question_number] = image_path
                        print(f"保存现有问题点 #{question_number} 的图片路径: {image_path}")
                
                # 现在可以安全地清空数据库中的问题点记录，因为我们已经保存了图片路径
                cursor.execute("DELETE FROM material_sample_questions WHERE form_id = %s", (form_id,))
                print("重排序模式：已清空数据库中的所有问题点记录，但保留了图片路径信息")
            else:
                print("不启用问题点图片重排序 - 按数据库的位置进行修改，保持原始编号")
                # 当不启用重排序时，保持问题点的原始位置和编号
                # 保持原始编号不变，不进行重新编号
                
                # 修复：即使在非重排序模式下，也保存现有的图片路径信息
                existing_images = {}
                cursor.execute("""
                    SELECT question_number, image_path 
                    FROM material_sample_questions 
                    WHERE form_id = %s AND image_path IS NOT NULL AND image_path != ''
                """, (form_id,))
                
                for row in cursor.fetchall():
                    question_number, image_path = row
                    if image_path:
                        existing_images[question_number] = image_path
                        print(f"保存现有问题点 #{question_number} 的图片路径: {image_path}")
                
                # 清空旧记录
                cursor.execute("DELETE FROM material_sample_questions WHERE form_id = %s", (form_id,))
                print("已清空数据库中的所有问题点记录，但保留了图片路径信息")
            
            # 使用临时存储的数据处理问题点
            # 首先记录所有的问题点数据，包括空的问题点
            all_question_positions = {}
            
            # 将表单中的图片状态信息添加到问题点数据中
            if question_images_status:
                print(f"将表单中的图片状态信息添加到问题点数据中")
                for i, data in enumerate(question_data):
                    if data:
                        question_number = i + 1
                        if str(question_number) in question_images_status:
                            image_info = question_images_status[str(question_number)]
                            if not data.get('imageSrc') and image_info.get('image_src'):
                                data['imageSrc'] = image_info['image_src']
                                data['hasImage'] = True
                                print(f"为问题点 {question_number} 添加图片URL: {image_info['image_src'][:30]}...")
            
            for i, data in enumerate(question_data):
                if data:
                    # 获取问题点编号
                    original_number = data.get('originalNumber')
                    position = i + 1
                    if original_number:
                        # 记录各自的位置，以便后续处理
                        all_question_positions[original_number] = position
            
            # 使用临时存储的数据处理问题点
            for i, data in enumerate(question_data):
                if not data:
                    continue
                    
                # 检查问题点是否为空（无文本且无图片）
                question_text = data.get('text', '').strip()
                image_src = data.get('imageSrc')
                original_number = data.get('originalNumber')
                
                # 关键修复：只有当真正有内容时才处理图片和数据库更新
                # 确保空的问题点被跳过，不会错误映射
                if not image_src and not question_text:
                    print(f"跳过空问题点: 索引={i}, 原始编号={data.get('originalNumber')}")
                    continue
                    
                # 根据是否启用重排序，使用不同的问题点编号方式
                if enable_image_reordering.lower() == 'yes':
                    # 重排序模式：检查是否有映射关系
                    original_number = data.get('originalNumber')
                    
                    # 首先使用连续编号（非空问题点按顺序排列）
                    question_number = i + 1
                    print(f"重排序模式：使用连续编号: {question_number}")
                    
                    # 确保映射关系是最新的（以连续编号为准）
                    if original_number and question_mapping:
                        if original_number in question_mapping:
                            try:
                                mapped_number = int(question_mapping[original_number])
                                if mapped_number != question_number:
                                    print(f"注意：映射的问题编号 {mapped_number} 与连续编号 {question_number} 不一致，更新为连续编号")
                                    # 更新映射关系，确保与连续编号一致
                                    question_mapping[original_number] = question_number
                            except (ValueError, TypeError):
                                print(f"重排序模式：无法转换映射的问题编号: {question_mapping[original_number]}，使用连续编号: {question_number}")
                                question_mapping[original_number] = question_number
                        else:
                            # 如果映射关系中没有该问题点，添加映射
                            question_mapping[original_number] = question_number
                            print(f"重排序模式：为问题点 {original_number} 添加映射关系: -> {question_number}")
                    
                    # 确保当前处理的问题点编号与图片文件名中的编号保持一致
                    # 图片文件名重命名在store_question_mapping中处理
                else:
                    # 不重排序模式：优先使用原始位置信息
                    if 'currentPosition' in data and data['currentPosition'] and 1 <= int(data['currentPosition']) <= 18:
                        question_number = int(data['currentPosition'])
                        print(f"使用问题点的原始位置: {question_number}")
                    else:
                        # 尝试使用原始编号
                        original_number = data.get('originalNumber')
                        if original_number and str(original_number).isdigit() and 1 <= int(original_number) <= 18:
                            question_number = int(original_number)
                            print(f"使用问题点的原始编号: {question_number}")
                        else:
                            # 尝试处理"new-X-Y"格式的编号
                            if original_number and str(original_number).startswith('new-'):
                                # 如果是new-X-Y格式，查找映射表或使用顺序编号
                                mapping = {}
                                if 'question_mapping' in request.form:
                                    try:
                                        mapping = json.loads(request.form.get('question_mapping', '{}'))
                                    except Exception as e:
                                        print(f"解析问题点映射数据失败: {e}")
                                
                                if original_number in mapping:
                                    try:
                                        mapped_number = int(mapping[original_number])
                                        if 1 <= mapped_number <= 18:
                                            question_number = mapped_number
                                            print(f"使用映射的问题编号: {original_number} -> {question_number}")
                                        else:
                                            question_number = i + 1
                                            print(f"映射的问题编号超出范围: {mapped_number}，使用顺序编号: {question_number}")
                                    except (ValueError, TypeError):
                                        question_number = i + 1
                                        print(f"无法转换映射的问题编号: {mapping[original_number]}，使用顺序编号: {question_number}")
                                else:
                                    question_number = i + 1
                                    print(f"问题点 {original_number} 没有映射关系，使用顺序编号: {question_number}")
                            else:
                                # 如果没有有效的原始编号，则使用顺序编号
                                question_number = i + 1
                                print(f"问题点索引 {i} 使用顺序编号: {question_number}")
                        
                # 如果是网络路径，需要转换为本地文件路径
                image_path = ''
                if image_src:
                    # 从URL中提取文件名
                    if '/' in image_src:
                        filename = image_src.split('/')[-1]
                        # 构建最终路径
                        image_path = os.path.join(get_image_final_path(report_code), filename)
                
                # 修复：如果当前问题点没有图片路径但在existing_images中有，使用原有的图片路径
                if not image_path and question_number in existing_images:
                    image_path = existing_images[question_number]
                    print(f"使用原有问题点 #{question_number} 的图片路径: {image_path}")
                
                cursor.execute("""
                    INSERT INTO material_sample_questions 
                    (form_id, question_number, question_text, image_path)
                    VALUES (%s, %s, %s, %s)
                """, (form_id, question_number, question_text, image_path))
                if cursor.with_rows:
                    cursor.fetchall()
                processed_question_numbers.add(question_number)
                print(f"已保存问题点 #{question_number}: 文本长度={len(question_text)}, 图片路径={'有' if image_path else '无'}")
            
            # 如果是重排序模式，在表单提交完成后重新检查并更新图片映射关系
            if enable_image_reordering.lower() == 'yes':
                print("重排序模式：在表单提交完成后重新检查图片映射关系")
                
                # 收集更新后的问题点信息
                updated_question_data = []
                
                # 查询更新后的问题点数据
                cursor.execute("""
                    SELECT q.id, q.question_number, q.question_text, q.image_path
                    FROM material_sample_questions q
                    WHERE q.form_id = %s
                    ORDER BY q.question_number
                """, (form_id,))
                
                questions = cursor.fetchall()
                
                for q in questions:
                    # 只关注有图片的问题点
                    if q[3]:  # image_path不为空
                        # 从图片路径中提取文件名
                        image_path = q[3]
                        image_name = os.path.basename(image_path) if image_path else ''
                        
                        if image_name:
                            # 从文件名中提取问题点编号
                            file_number = ''
                            try:
                                name_parts = image_name.split('_')
                                if name_parts and len(name_parts) > 0:
                                    file_number = name_parts[0]
                            except Exception as e:
                                print(f"无法从文件名提取问题点编号: {e}")
                            
                            # 如果文件编号与问题点编号不一致，记录需要更新
                            if file_number and file_number != str(q[1]):
                                print(f"发现问题点编号与图片文件名不一致: 问题点编号={q[1]}, 文件编号={file_number}, 文件名={image_name}")
                                updated_question_data.append({
                                    'originalNumber': str(file_number),  # 使用文件名中的编号作为原始编号
                                    'newNumber': q[1],  # 使用数据库中的问题点编号作为新编号
                                    'imageName': image_name,
                                    'imagePath': image_path
                                })
                
                # 如果有需要更新的图片，调用store_question_mapping处理
                if updated_question_data:
                    print(f"发现 {len(updated_question_data)} 个问题点的图片需要重命名")
                    
                    # 存储更新的问题点映射关系
                    temp_question_data_store[report_code] = {
                        'question_data': updated_question_data,
                        'question_mapping': question_mapping,  # 使用当前的映射关系
                        'enable_reordering': 'yes',  # 强制启用重排序
                        'timestamp': datetime.datetime.now().isoformat()
                    }
                    
                    # 不立即删除临时存储的数据，等待后续处理
                    print(f"已更新报告 {report_code} 的临时存储数据，等待后续处理")
                else:
                    # 清理临时存储的数据
                    if report_code in temp_question_data_store:
                        del temp_question_data_store[report_code]
                        print(f"问题点图片已全部与问题点编号一致，清理临时存储数据")
            else:
                # 非重排序模式，直接清理临时存储的数据
                if report_code in temp_question_data_store:
                    del temp_question_data_store[report_code]
                    print(f"已清理报告 {report_code} 的临时存储数据")
        else:
            print("未找到临时存储的问题点映射数据，使用表单数据处理问题点")
            
            # 修复：保存现有的图片路径信息
            existing_images = {}
            cursor.execute("""
                SELECT question_number, image_path 
                FROM material_sample_questions 
                WHERE form_id = %s AND image_path IS NOT NULL AND image_path != ''
            """, (form_id,))
            
            for row in cursor.fetchall():
                question_number, image_path = row
                if image_path:
                    existing_images[question_number] = image_path
                    print(f"保存现有问题点 #{question_number} 的图片路径: {image_path}")
            
            # 清空旧记录
            cursor.execute("DELETE FROM material_sample_questions WHERE form_id = %s", (form_id,))
            print("已清空数据库中的所有问题点记录，但保留了图片路径信息")
            
            # 1. 处理标准命名格式的问题点 (question_1_text 到 question_18_text)
            for i in range(1, 19):  # 最多18个问题点
                question_text = request.form.get(f'question_{i}_text', '').strip()
                image_path = request.form.get(f'question_{i}_image', '')
                
                # 修复：如果没有提供图片路径但在existing_images中有，使用原有的图片路径
                if not image_path and i in existing_images:
                    image_path = existing_images[i]
                    print(f"使用原有问题点 #{i} 的图片路径: {image_path}")
                
                # 关键修复：只有存在文本或图片路径时才插入记录
                # 确保空单元格不会被保存到数据库中
                if question_text or image_path:
                    cursor.execute("""
                        INSERT INTO material_sample_questions 
                        (form_id, question_number, question_text, image_path)
                        VALUES (%s, %s, %s, %s)
                    """, (form_id, i, question_text, image_path))
                    if cursor.with_rows:
                        cursor.fetchall()
                    processed_question_numbers.add(i)
                    print(f"已保存标准问题点 #{i}: 文本长度={len(question_text)}, 图片路径={'有' if image_path else '无'}")
        
        # 2. 处理自动生成的问题点 (question_auto_*_*_text 格式)
        auto_questions = []
        for key in request.form:
            if key.startswith('question_auto_') and key.endswith('_text'):
                question_text = request.form.get(key, '').strip()
                if question_text:  # 只处理有内容的问题点
                    # 尝试从键名中提取行列信息
                    try:
                        # 格式: question_auto_ROW_COL_text
                        parts = key.split('_')
                        if len(parts) >= 5:
                            row = int(parts[2])
                            col = int(parts[3])
                            # 计算问题编号: (行-1)*3 + 列 + 1
                            question_number = (row - 1) * 3 + col + 1
                            
                            # 检查问题编号是否已被使用
                            if question_number not in processed_question_numbers and question_number <= 18:
                                # 构建可能的图片路径键名
                                image_key = key.replace('_text', '_image')
                                image_path = request.form.get(image_key, '')
                                
                                auto_questions.append({
                                    'number': question_number,
                                    'text': question_text,
                                    'image_path': image_path,
                                    'key': key
                                })
                                print(f"找到自动问题点: key={key}, 编号={question_number}, 文本长度={len(question_text)}")
                    except Exception as e:
                        print(f"解析自动问题点键名出错: {e}, 键名={key}")
        
        # 保存自动问题点
        for question in auto_questions:
            if question['number'] not in processed_question_numbers:
                cursor.execute("""
                    INSERT INTO material_sample_questions 
                    (form_id, question_number, question_text, image_path)
                    VALUES (%s, %s, %s, %s)
                """, (form_id, question['number'], question['text'], question['image_path']))
                if cursor.with_rows:
                    cursor.fetchall()
                processed_question_numbers.add(question['number'])
                print(f"已保存自动问题点 #{question['number']}: 文本长度={len(question['text'])}, 图片路径={'有' if question['image_path'] else '无'}")
        
        # 3. 处理动态添加的问题点 (可能使用其他命名格式)
        # 确保任何情况下，都不会将空单元格内容错误映射到其他单元格
        for key in request.form:
            # 匹配可能的问题点文本字段模式
            if key.startswith('question_') and key.endswith('_text') and not key.startswith('question_empty_'):
                # 跳过已处理的标准命名格式和自动问题点
                if any(key == f'question_{i}_text' for i in range(1, 19)) or key.startswith('question_auto_'):
                    continue
                
                question_text = request.form.get(key, '').strip()
                if not question_text:
                    continue  # 跳过空文本
                
                # 尝试提取问题编号
                try:
                    # 处理 question_new_*_*_text 等格式
                    print(f"处理动态问题点: key={key}, 文本长度={len(question_text)}")
                    
                    # 从key中提取问题编号
                    parts = key.split('_')
                    question_number = None  # 初始化问题编号变量

                    if len(parts) > 1:
                        try:
                            # 尝试将第二个部分解析为数字
                            question_number = int(parts[1])
                            if question_number >= 1 and question_number <= 18:
                                # 只有在有效范围内且未处理过时才处理
                                if question_number not in processed_question_numbers:
                                    # 构建可能的图片路径键名
                                    image_key = key.replace('_text', '_image')
                                    image_path = request.form.get(image_key, '')
                                    
                                    cursor.execute("""
                                        INSERT INTO material_sample_questions 
                                        (form_id, question_number, question_text, image_path)
                                        VALUES (%s, %s, %s, %s)
                                    """, (form_id, question_number, question_text, image_path))
                                    if cursor.with_rows:
                                        cursor.fetchall()
                                    processed_question_numbers.add(question_number)
                                    print(f"已保存动态问题点: 编号 #{question_number}, 文本长度={len(question_text)}, 图片路径={'有' if image_path else '无'}")
                                else:
                                    print(f"警告: 问题点 {question_number} 已经处理过，跳过重复处理")
                        except (ValueError, IndexError):
                            pass
                
                    # 如果不能从key提取有效编号，则查找下一个可用的问题编号
                    if question_number is None or question_number not in range(1, 19):
                        next_question_number = 1
                        while next_question_number <= 18 and next_question_number in processed_question_numbers:
                            next_question_number += 1
                        
                        if next_question_number <= 18:
                            # 构建可能的图片路径键名
                            image_key = key.replace('_text', '_image')
                            image_path = request.form.get(image_key, '')
                            
                            cursor.execute("""
                                INSERT INTO material_sample_questions 
                                (form_id, question_number, question_text, image_path)
                                VALUES (%s, %s, %s, %s)
                            """, (form_id, next_question_number, question_text, image_path))
                            if cursor.with_rows:
                                cursor.fetchall()
                            processed_question_numbers.add(next_question_number)
                            print(f"已保存动态问题点: 分配编号 #{next_question_number}, 文本长度={len(question_text)}, 图片路径={'有' if image_path else '无'}")
                        else:
                            print(f"警告: 问题点数量超过18个上限，忽略: {key}")
                    else:
                        print(f"警告: 问题点 {question_number} 已经处理过，跳过重复处理")
                except Exception as e:
                    print(f"处理动态问题点时出错: {e}, 字段名: {key}")
                    continue
        
        print(f"问题点处理完成，共保存 {len(processed_question_numbers)} 个问题点")
        
        # 注意：这里不再处理'question_empty_'开头的字段，确保它们在提交前和提交后都为空
        
        # 处理临时上传的图片，将它们移动到最终位置
        base_image_path = get_image_base_path()
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads', report_code)
        
        # 获取最终存储路径 - 优先使用原始图片目录路径
        # 查询数据库获取该报告的所有问题记录中的图片路径，提取原始目录
        cursor.execute("""
            SELECT q.image_path 
            FROM material_sample_questions q
            JOIN material_sample_confirmation_form f ON q.form_id = f.id
            WHERE f.report_code = %s AND q.image_path IS NOT NULL AND q.image_path != ''
            LIMIT 1
        """, (report_code,))
        
        original_dir = None
        image_path_result = cursor.fetchone()
        if image_path_result and image_path_result[0]:
            original_path = image_path_result[0].replace('/', '\\')
            if os.path.exists(original_path):
                original_dir = os.path.dirname(original_path)
                print(f"找到原始图片目录: {original_dir}")
        
        # 如果没有从数据库找到有效的原始目录，尝试查找现有目录
        if not original_dir or not os.path.exists(original_dir):
            original_dir = get_original_image_dir(report_code)
        
        # 使用找到的原始目录作为最终目录，并确保路径格式正确
        final_dir = original_dir
        # 统一使用正斜杠，避免路径问题
        final_dir = final_dir.replace('\\', '/')
        
        print(f"开始处理图片移动：报告编码 {report_code}")
        print(f"临时目录: {temp_uploads_dir}")
        print(f"最终目录: {final_dir}")
        
        # 确保最终目录存在
        os.makedirs(final_dir, exist_ok=True)
        
        # 检查是否有临时存储的问题点映射数据
        stored_mapping_data = temp_question_data_store.get(report_code)
        stored_enable_reordering = 'no'
        
        if stored_mapping_data:
            stored_enable_reordering = stored_mapping_data.get('enable_reordering', 'no')
            print(f"从临时存储获取图片重排序设置: {stored_enable_reordering}")
        
        # 修复：当启用重排序且有存储映射数据时，不清空最终目录中的图片
        # 仅当不是重排序模式或确实需要清空时才清空目录
        # 清空最终目录中与问题点相关的所有图片，防止重复
        should_clear_dir = True
        
        # 当启用了重排序且有存储的映射数据时，检查是否真的有修改
        if stored_mapping_data and stored_enable_reordering.lower() == 'yes':
            # 检查是否真的有修改，如果没有实际修改，不清空目录
            question_data = stored_mapping_data.get('question_data', [])
            question_mapping = stored_mapping_data.get('question_mapping', {})
            
            # 如果mapping为空或只包含相同编号映射（如"1":"1"），说明没有实际重排序
            no_actual_reordering = True
            for orig_num, new_num in question_mapping.items():
                if str(orig_num) != str(new_num):
                    no_actual_reordering = False
                    break
            
            if no_actual_reordering:
                should_clear_dir = False
                print("检测到没有实际的重排序，将保留原有图片")
        
        # 只有当确实需要清空时才执行
        if should_clear_dir:
            try:
                if os.path.exists(final_dir):
                    # 获取最终目录中的所有文件
                    existing_files = os.listdir(final_dir)
                    deleted_count = 0
                    
                    # 识别问题点相关的图片文件（通常以问题编号开头）
                    for filename in existing_files:
                        # 检查是否为问题点图片（通过文件名前缀判断）
                        is_question_image = False
                        for q_num in range(1, 19):  # 处理所有18个问题点
                            if filename.startswith(f"{q_num}_") or f"_{q_num}_" in filename:
                                is_question_image = True
                                break
                        
                        if is_question_image:
                            try:
                                file_path = os.path.join(final_dir, filename)
                                os.remove(file_path)
                                deleted_count += 1
                                print(f"已删除最终目录中的旧图片: {file_path}")
                            except Exception as e:
                                print(f"删除最终目录中的旧图片失败: {file_path}, 错误: {e}")
                    
                    print(f"已从最终目录清理 {deleted_count} 个旧图片文件")
            except Exception as e:
                print(f"清理最终目录中的旧图片时出错: {e}")
        
        # 从数据库中获取问题和图片关联信息
        cursor.execute("""
            SELECT q.id, q.question_number, q.question_text, q.image_path 
            FROM material_sample_questions q
            WHERE q.form_id = %s
        """, (form_id,))
        
        questions = cursor.fetchall()
        print(f"数据库中找到 {len(questions)} 个问题记录")
        
        # 记录问题编号到图片路径的映射
        question_images = {}
        for question in questions:
            question_id = question[0]
            question_number = question[1]
            image_path = question[3]
            
            if image_path:
                question_images[question_number] = {
                    'db_id': question_id,
                    'current_path': image_path,
                    'filename': os.path.basename(image_path)
                }

        # 如果临时目录存在，处理其中的图片
        moved_images = []
        if os.path.exists(temp_uploads_dir):
            print(f"找到临时目录: {temp_uploads_dir}")
            
            # 如果有临时存储的问题点映射数据
            if stored_mapping_data:
                # 获取重排序设置
                stored_enable_reordering = stored_mapping_data.get('enable_reordering', 'no')
                print(f"从临时存储获取图片重排序设置: {stored_enable_reordering}")
                
                if stored_enable_reordering.lower() == 'yes':
                    print("检测到启用了问题点图片重排序，将使用映射关系处理图片")
                else:
                    print("检测到未启用问题点图片重排序，将保持原始编号")
                
                question_data = stored_mapping_data.get('question_data', [])
                question_mapping = stored_mapping_data.get('question_mapping', {})
                
                # 创建一个新的映射，从原始问题编号到新问题编号
                new_mapping = {}
                original_numbers = {}
                
                # 处理每个问题点数据
                for i, data in enumerate(question_data):
                    if not data:
                        continue
                        
                    new_question_number = i + 1  # 新的问题编号
                    original_number = data.get('originalNumber')  # 原始问题编号
                    
                    if original_number:
                        new_mapping[original_number] = new_question_number
                        original_numbers[new_question_number] = original_number
                        print(f"映射关系: 原始问题编号 {original_number} -> 新问题编号 {new_question_number}")
                
                # 临时目录中所有文件的列表
                temp_files = []
                if os.path.exists(temp_uploads_dir) and os.path.isdir(temp_uploads_dir):
                    temp_files = os.listdir(temp_uploads_dir)
                
                # 创建问题点编号到图片文件名的映射
                question_image_files = {}
                for filename in temp_files:
                    # 尝试从文件名中提取问题编号
                    try:
                        file_number = filename.split('_')[0]
                        if file_number.isdigit():
                            question_number = int(file_number)
                            question_image_files[question_number] = filename
                    except Exception as e:
                        print(f"从文件名提取问题编号失败: {filename}, 错误: {e}")
                
                # 处理每个问题点数据
                for i, data in enumerate(question_data):
                    if not data:
                        continue
                    
                    # 根据是否启用重排序，使用不同的问题点编号方式
                    if stored_enable_reordering.lower() == 'yes':
                        # 重排序模式：使用连续编号
                        new_number = i + 1  # 重排序后的问题编号
                        
                        # 检查是否有映射关系调整
                        original_number = data.get('originalNumber')
                        if original_number and original_number in question_mapping:
                            # 使用映射关系中的值，确保正确处理
                            mapped_number = question_mapping[original_number]
                            try:
                                mapped_int = int(mapped_number)
                                if 1 <= mapped_int <= 18:
                                    new_number = mapped_int
                                    print(f"使用映射的问题编号: {original_number} -> {new_number}")
                            except (ValueError, TypeError):
                                print(f"映射值无效: {mapped_number}，使用默认编号: {new_number}")
                    else:
                        # 不重排序模式：使用原始编号
                        original_number = data.get('originalNumber')
                        if original_number and str(original_number).isdigit() and 1 <= int(original_number) <= 18:
                            new_number = int(original_number)
                        else:
                            # 如果没有有效的原始编号，则使用顺序编号
                            new_number = i + 1
                    
                    image_src = data.get('imageSrc')
                    question_text = data.get('text', '').strip()
                    
                    # 关键修复：只有当真正有内容时才处理图片和数据库更新
                    # 确保空的问题点被跳过，不会错误映射
                    if not image_src and not question_text:
                        print(f"跳过空问题点: 索引={i}, 原始编号={data.get('originalNumber')}")
                        continue
                    
                    # 修复：确保图片与文本一起移动
                    # 如果这个问题点有原始编号，并且原始编号对应的图片存在
                    if original_number and str(original_number).isdigit():
                        orig_num_int = int(original_number)
                        if orig_num_int in question_image_files:
                            # 找到原始编号对应的图片
                            orig_image_filename = question_image_files[orig_num_int]
                            
                            # 如果当前问题点没有图片但原始编号对应的问题点有图片，使用原始编号的图片
                            if not image_src:
                                # 构建临时路径
                                temp_path = os.path.join(temp_uploads_dir, orig_image_filename)
                                if os.path.exists(temp_path):
                                    # 创建新的文件名，保持时间戳部分不变
                                    name_parts = orig_image_filename.split('_')
                                    if len(name_parts) > 1:
                                        timestamp_parts = name_parts[1:]
                                        new_filename = f"{new_number}_{'_'.join(timestamp_parts)}"
                                        
                                        # 构建最终路径
                                        final_path = os.path.join(final_dir, new_filename)
                                        
                                        # 确保路径格式正确
                                        temp_path = temp_path.replace('\\', '/')
                                        final_path = final_path.replace('\\', '/')
                                        
                                        # 复制文件到最终目录
                                        try:
                                            shutil.copy2(temp_path, final_path)
                                            print(f"已将问题点 {orig_num_int} 的图片复制到问题点 {new_number} 的位置: {temp_path} -> {final_path}")
                                            
                                            # 添加到已移动图片列表
                                            moved_images.append({
                                                'question_number': new_number,
                                                'original_number': orig_num_int,
                                                'src_path': temp_path,
                                                'dest_path': final_path,
                                                'filename': new_filename
                                            })
                                            
                                            # 更新数据库中的图片路径
                                            cursor.execute("""
                                                UPDATE material_sample_questions 
                                                SET image_path = %s 
                                                WHERE form_id = %s AND question_number = %s
                                            """, (final_path, form_id, new_number))
                                            
                                            print(f"已更新问题 {new_number} 的图片路径: {final_path}")
                                            
                                            # 删除临时文件
                                            try:
                                                os.remove(temp_path)
                                                print(f"已删除临时文件: {temp_path}")
                                            except Exception as e:
                                                print(f"删除临时文件失败: {temp_path}, 错误: {e}")
                                        except Exception as e:
                                            print(f"复制图片或更新数据库失败: {e}")
                    
                    if image_src and '/' in image_src:
                        # 从URL中提取文件名
                        filename = image_src.split('/')[-1]
                        
                        # 构建临时路径和最终路径
                        temp_path = os.path.join(temp_uploads_dir, filename)
                        final_path = os.path.join(final_dir, filename)
                        
                        # 确保路径格式正确，统一使用正斜杠
                        temp_path = temp_path.replace('\\', '/')
                        final_path = final_path.replace('\\', '/')
                        
                        # 检查文件是否存在于临时目录
                        if os.path.exists(temp_path):
                            try:
                                # 复制文件到最终目录
                                shutil.copy2(temp_path, final_path)
                                print(f"已将重排序的图片复制到最终位置: {temp_path} -> {final_path}")
                                
                                # 添加到已移动图片列表
                                moved_images.append({
                                    'question_number': new_number,
                                    'src_path': temp_path,
                                    'dest_path': final_path,
                                    'filename': filename
                                })
                                
                                # 更新数据库中的图片路径
                                cursor.execute("""
                                    UPDATE material_sample_questions 
                                    SET image_path = %s 
                                    WHERE form_id = %s AND question_number = %s
                                """, (final_path, form_id, new_number))
                                
                                print(f"已更新问题 {new_number} 的图片路径: {final_path}")
                                
                                # 删除临时文件
                                try:
                                    os.remove(temp_path)
                                    print(f"已删除临时文件: {temp_path}")
                                except Exception as e:
                                    print(f"删除临时文件失败: {temp_path}, 错误: {e}")
                                
                            except Exception as e:
                                print(f"复制图片或更新数据库失败: {e}")
            else:
                print("未启用问题点图片重排序或未找到临时存储的映射数据，使用常规方式处理图片")
                # 首先检查是否存在子目录（针对新的目录结构）
                if os.path.isdir(os.path.join(temp_uploads_dir)):
                    # 遍历临时目录中的所有文件
                    for filename in os.listdir(temp_uploads_dir):
                        file_path = os.path.join(temp_uploads_dir, filename)
                        
                        # 如果是子目录，跳过
                        if os.path.isdir(file_path):
                            print(f"跳过子目录: {file_path}")
                            continue
                        
                        # 处理文件
                        src_path = file_path
                        
                        # 尝试从文件名中提取问题编号
                        question_number = None
                        try:
                            # 首先检查是否是"new-X-Y"格式的文件名
                            if 'new-' in filename:
                                # 从文件名中提取"new-X-Y"部分
                                parts = filename.split('_')[0]  # 获取第一部分，例如"new-3-0"
                                
                                # 检查是否有问题点映射数据
                                if stored_mapping_data and 'mapping' in stored_mapping_data:
                                    mapping = stored_mapping_data.get('mapping', {})
                                    print(f"检查文件 {filename} 的映射关系: {parts}")
                                    
                                    # 如果有映射数据，使用映射的问题编号
                                    if parts in mapping:
                                        mapped_number = mapping.get(parts)
                                        try:
                                            mapped_number = int(mapped_number)
                                            if 1 <= mapped_number <= 18:
                                                question_number = mapped_number
                                                print(f"根据映射找到问题编号: {parts} -> {question_number}")
                                        except (ValueError, TypeError):
                                            print(f"映射值无效: {mapped_number}")
                            else:
                                # 假设文件名格式为 "问题编号_日期_时间.扩展名"
                                # 例如: 5_250529_234703.png
                                parts = filename.split('_')
                                if len(parts) >= 2:
                                    question_number = int(parts[0])
                                    if question_number < 1 or question_number > 18:
                                        question_number = None  # 无效的问题编号
                        except Exception as e:
                            print(f"从文件名提取问题编号时出错: {e}")
                            # 如果无法提取问题编号，使用替代方法
                            for q_num in range(1, 19):  # 处理所有18个问题点
                                if f"_{q_num}_" in filename or filename.startswith(f"{q_num}_"):
                                    question_number = q_num
                                    break
                        
                        if question_number is None:
                            print(f"警告: 无法从文件名 {filename} 中提取问题编号，尝试根据文件模式匹配")
                            for q_num in range(1, 19):  # 尝试匹配可能的问题编号
                                if f"_{q_num}_" in filename or filename.startswith(f"{q_num}_"):
                                    question_number = q_num
                                    print(f"根据模式匹配找到问题编号: {q_num}")
                                    break
                        
                        # 如果仍然无法确定问题编号，使用文件创建时间尝试确定
                        if question_number is None:
                            print(f"无法确定文件 {filename} 的问题编号，尝试使用其他方法")
                            # 这里可以添加其他确定问题编号的逻辑
                            # 例如，根据文件创建时间排序，分配到未使用的问题编号
                            continue
                        
                        # 构建目标路径
                        dest_path = os.path.join(final_dir, filename)
                        
                        # 如果目标文件已存在，先删除
                        if os.path.exists(dest_path):
                            try:
                                os.remove(dest_path)
                                print(f"已删除已存在的图片: {dest_path}")
                            except Exception as e:
                                print(f"删除已存在的图片失败: {dest_path}, 错误: {e}")
                        
                        # 移动文件到最终位置
                        try:
                            # 复制文件
                            shutil.copy2(src_path, dest_path)
                            print(f"已将图片从 {src_path} 复制到 {dest_path}")
                            
                            # 成功复制后，将其添加到移动记录中
                            moved_images.append({
                                'question_number': question_number,
                                'src_path': src_path,
                                'dest_path': dest_path,
                                'filename': filename
                            })
                            
                            # 修复：确保问题点图片路径被更新到数据库，即使没有对应的文本
                            # 检查是否已存在该问题点的记录
                            cursor.execute("""
                                SELECT id FROM material_sample_questions 
                                WHERE form_id = %s AND question_number = %s
                            """, (form_id, question_number))
                            
                            existing_record = cursor.fetchone()
                            if existing_record:
                                # 更新已存在的记录
                                cursor.execute("""
                                    UPDATE material_sample_questions 
                                    SET image_path = %s 
                                    WHERE form_id = %s AND question_number = %s
                                """, (dest_path, form_id, question_number))
                                print(f"已更新问题 #{question_number} 的图片路径: {dest_path}")
                            else:
                                # 创建新记录，即使没有文本内容
                                cursor.execute("""
                                    INSERT INTO material_sample_questions 
                                    (form_id, question_number, question_text, image_path)
                                    VALUES (%s, %s, %s, %s)
                                """, (form_id, question_number, '', dest_path))
                                print(f"已为问题 #{question_number} 创建新记录，图片路径: {dest_path}")
                            
                            # 清理临时文件
                            try:
                                os.remove(src_path)
                                print(f"已删除临时文件: {src_path}")
                            except Exception as e:
                                print(f"删除临时文件失败: {src_path}, 错误: {e}")
                                
                        except Exception as e:
                            print(f"移动图片失败: {src_path} -> {dest_path}, 错误: {e}")
                
                # 检查是否存在报告编码子目录（新的目录结构）
                report_temp_dir = os.path.join(temp_uploads_dir, report_code)
                if os.path.exists(report_temp_dir) and os.path.isdir(report_temp_dir):
                    print(f"找到报告临时目录: {report_temp_dir}")
                    
                    # 如果有临时存储的问题点映射数据
                    if stored_mapping_data:
                        # 获取临时存储的重排序设置
                        stored_enable_reordering = stored_mapping_data.get('enable_reordering', 'no')
                        
                        if stored_enable_reordering.lower() == 'yes':
                            print("检测到启用了问题点图片重排序，将使用映射关系处理报告临时目录中的图片")
                        else:
                            print("检测到未启用问题点图片重排序，将保持原始编号处理报告临时目录中的图片")
                        
                        question_data = stored_mapping_data.get('question_data', [])
                        
                        # 临时目录中所有文件的列表
                        temp_files = []
                        if os.path.exists(report_temp_dir) and os.path.isdir(report_temp_dir):
                            temp_files = os.listdir(report_temp_dir)
                            
                            # 处理报告临时目录中的每个文件
                            for filename in temp_files:
                                # 构建完整的文件路径
                                src_path = os.path.join(report_temp_dir, filename)
                                
                                # 如果是目录，跳过
                                if os.path.isdir(src_path):
                                    print(f"跳过子目录: {src_path}")
                                    continue
                                
                                # 尝试从文件名中提取问题编号
                                question_number = None
                                try:
                                    # 首先检查是否是"new-X-Y"格式的文件名
                                    if 'new-' in filename:
                                        # 从文件名中提取"new-X-Y"部分
                                        parts = filename.split('_')[0]  # 获取第一部分，例如"new-3-0"
                                        
                                        # 直接从new-X-Y格式中提取有效的问题编号
                                        if parts.startswith('new-'):
                                            num_parts = parts.split('-')
                                            if len(num_parts) >= 3 and num_parts[2].isdigit():  # 修复：正确识别"new-4-1"中的"1"
                                                # 使用第三部分作为问题编号
                                                extracted_number = int(num_parts[2])
                                                if 1 <= extracted_number <= 18:
                                                    question_number = extracted_number
                                                    print(f"从new-格式直接提取问题编号: {parts} -> {question_number}")
                                        
                                        # 如果直接提取失败，检查是否有问题点映射数据
                                        if question_number is None and 'question_mapping' in stored_mapping_data:
                                            mapping = stored_mapping_data.get('question_mapping', {})
                                            print(f"检查文件 {filename} 的映射关系: {parts}")
                                            
                                            # 如果有映射数据，使用映射的问题编号
                                            if parts in mapping:
                                                mapped_number = mapping.get(parts)
                                                try:
                                                    mapped_number = int(mapped_number)
                                                    if 1 <= mapped_number <= 18:
                                                        question_number = mapped_number
                                                        print(f"根据映射找到问题编号: {parts} -> {question_number}")
                                                except (ValueError, TypeError):
                                                    print(f"映射值无效: {mapped_number}")
                                    else:
                                        # 假设文件名格式为 "问题编号_日期_时间.扩展名"
                                        # 例如: 5_250529_234703.png
                                        parts = filename.split('_')
                                        if len(parts) >= 2 and parts[0].isdigit():
                                            question_number = int(parts[0])
                                            if question_number < 1 or question_number > 18:
                                                question_number = None  # 无效的问题编号
                                except Exception as e:
                                    print(f"从文件名提取问题编号时出错: {e}")
                                    # 如果无法提取问题编号，使用替代方法
                                    for q_num in range(1, 19):  # 处理所有18个问题点
                                        if f"_{q_num}_" in filename or filename.startswith(f"{q_num}_"):
                                            question_number = q_num
                                            break
                                
                                if question_number is None:
                                    print(f"警告: 无法从文件名 {filename} 中提取问题编号，尝试进一步分析")
                                    
                                    # 再次检查文件名是否包含new-格式
                                    if 'new-' in filename:
                                        match = re.search(r'new-(\d+)-(\d+)', filename)
                                        if match:
                                            # 提取第2个捕获组（y值）而不是第1个（x值）
                                            extracted_number = int(match.group(2))
                                            if 1 <= extracted_number <= 18:
                                                question_number = extracted_number
                                                print(f"使用正则表达式从文件名提取问题编号: {filename} -> {question_number}")
                                    
                                    # 如果仍然无法确定问题编号，跳过处理此文件
                                    if question_number is None:
                                        print(f"警告: 无法最终确定问题编号，跳过处理文件: {filename}")
                                        continue
                                
                                # 构建目标路径
                                dest_path = os.path.join(final_dir, filename)
                                
                                # 如果目标文件已存在，先删除
                                if os.path.exists(dest_path):
                                    try:
                                        os.remove(dest_path)
                                        print(f"已删除已存在的图片: {dest_path}")
                                    except Exception as e:
                                        print(f"删除已存在的图片失败: {dest_path}, 错误: {e}")
                                
                                # 移动文件到最终位置
                                try:
                                    # 复制文件
                                    shutil.copy2(src_path, dest_path)
                                    print(f"已将图片从 {src_path} 复制到 {dest_path}")
                                    
                                    # 成功复制后，将其添加到移动记录中
                                    moved_images.append({
                                        'question_number': question_number,
                                        'src_path': src_path,
                                        'dest_path': dest_path,
                                        'filename': filename
                                    })
                                    
                                    # 清理临时文件
                                    try:
                                        os.remove(src_path)
                                        print(f"已删除临时文件: {src_path}")
                                    except Exception as e:
                                        print(f"删除临时文件失败: {src_path}, 错误: {e}")
                                    
                                except Exception as e:
                                    print(f"移动图片失败: {src_path} -> {dest_path}, 错误: {e}")
        else:
            print(f"临时目录不存在: {temp_uploads_dir}")
        
        # 更新数据库中的图片路径
        print(f"开始更新数据库中的图片路径，共 {len(moved_images)} 个图片")
        
        # 检查是否有问题点映射关系
        question_mapping = {}
        if 'question_mapping' in request.form:
            try:
                question_mapping = json.loads(request.form.get('question_mapping', '{}'))
                print(f"接收到问题点映射关系: {question_mapping}")
            except Exception as e:
                print(f"解析问题点映射关系失败: {e}")
        
        # 收集原始编号信息
        original_numbers = {}
        for key in request.form:
            if key.startswith('question_') and key.endswith('_original_number'):
                try:
                    question_number = int(key.split('_')[1])
                    original_number = request.form[key]
                    original_numbers[question_number] = original_number
                    print(f"问题点 {question_number} 的原始编号: {original_number}")
                except Exception as e:
                    print(f"处理原始编号信息失败: {key}, {e}")
        
        print(f"收集到的原始编号信息: {original_numbers}")
        
        # 检查是否有临时存储的问题点映射关系
        stored_mapping_data = temp_question_data_store.get(report_code)
        if stored_mapping_data:
            print(f"使用临时存储的问题点映射数据进行图片路径更新")
            question_data = stored_mapping_data.get('question_data', [])
            question_mapping = stored_mapping_data.get('question_mapping', {})
            
            # 创建一个新的映射，从原始问题编号到新问题编号
            new_mapping = {}
            
            # 处理每个问题点数据
            for i, data in enumerate(question_data):
                if not data:
                    continue
                    
                new_question_number = i + 1  # 新的问题编号
                original_number = data.get('originalNumber')  # 原始问题编号
                
                if original_number:
                    new_mapping[original_number] = new_question_number
                    print(f"映射关系: 原始问题编号 {original_number} -> 新问题编号 {new_question_number}")
            
            # 处理移动的图片，根据映射关系重命名文件
            for image_info in moved_images:
                original_question_number = image_info.get('question_number')
                if original_question_number is not None:
                    # 获取新的问题编号
                    new_question_number = new_mapping.get(str(original_question_number))
                    
                    if new_question_number is not None:
                        # 原始文件路径
                        original_path = image_info['dest_path']
                        original_filename = image_info['filename']
                        
                        # 解析文件名部分
                        name_parts = original_filename.split('_')
                        extension = original_filename.split('.')[-1]
                        
                        # 创建新文件名，替换问题编号部分
                        if len(name_parts) >= 2:
                            # 替换第一部分为新的问题编号
                            name_parts[0] = str(new_question_number)
                            new_filename = '_'.join(name_parts)
                        else:
                            # 如果文件名格式不符合预期，创建一个新的文件名
                            timestamp = datetime.datetime.now().strftime('%y%m%d_%H%M%S_%f')[:16]  # 取到毫秒的前3位
                            random_suffix = random.randint(1000, 9999)  # 生成随机4位数
                            new_filename = f"{new_question_number}_{timestamp}_{random_suffix}.{extension}"
                        
                        # 新文件路径
                        new_path = os.path.join(final_dir, new_filename)
                        
                        # 如果新文件已存在，先删除
                        if os.path.exists(new_path) and new_path != original_path:
                            try:
                                os.remove(new_path)
                                print(f"已删除已存在的目标文件: {new_path}")
                            except Exception as e:
                                print(f"删除已存在的目标文件失败: {new_path}, 错误: {e}")
                        
                        # 重命名文件
                        try:
                            if original_path != new_path:
                                shutil.copy2(original_path, new_path)
                                os.remove(original_path)
                                print(f"已重命名图片: {original_path} -> {new_path}")
                                
                                # 更新image_info中的路径信息
                                image_info['dest_path'] = new_path
                                image_info['filename'] = new_filename
                                image_info['new_question_number'] = new_question_number
                        except Exception as e:
                            print(f"重命名图片失败: {original_path} -> {new_path}, 错误: {e}")
                    else:
                        print(f"警告: 找不到问题编号 {original_question_number} 的映射关系")
            
            # 更新数据库中的图片路径
            for i, data in enumerate(question_data):
                if not data:
                    continue
                    
                question_number = i + 1
                image_src = data.get('imageSrc')
                has_text = data.get('hasText', False)
                
                # 更全面地检查问题点是否有内容
                question_text = data.get('text', '').strip()
                has_image = bool(image_src) and len(str(image_src)) > 0
                has_text = bool(question_text) or data.get('hasText', False)
                
                print(f"处理问题点 {question_number}: 有图片={has_image}, 有文本={has_text}, 图片URL={image_src}")
                
                # 修复：即使只有图片没有文本，也要保存到数据库
                if has_image or has_text:
                    # 查找对应的移动后图片
                    matched_image = None
                    for image_info in moved_images:
                        if image_info.get('new_question_number') == question_number:
                            matched_image = image_info
                            break
                    
                    # 检查是否已存在该问题点的记录
                    cursor.execute("""
                        SELECT id FROM material_sample_questions 
                        WHERE form_id = %s AND question_number = %s
                    """, (form_id, question_number))
                    
                    existing_record = cursor.fetchone()
                    
                    if has_image:
                        # 有图片的情况
                        if matched_image:
                            # 找到匹配的移动后图片
                            final_path = matched_image['dest_path']
                            
                            if existing_record:
                                cursor.execute("""
                                    UPDATE material_sample_questions 
                                    SET image_path = %s, question_text = %s
                                    WHERE form_id = %s AND question_number = %s
                                """, (final_path, question_text, form_id, question_number))
                            else:
                                # 插入新记录，包括文本
                                cursor.execute("""
                                    INSERT INTO material_sample_questions 
                                    (form_id, question_number, question_text, image_path)
                                    VALUES (%s, %s, %s, %s)
                                """, (form_id, question_number, question_text, final_path))
                            
                            print(f"已更新问题 {question_number} 的图片路径为: {final_path}")
                        else:
                            # 有图片URL但没有找到匹配的移动后图片，尝试直接使用URL中的文件名
                            try:
                                if image_src and '/' in image_src:
                                    # 从URL中提取文件名
                                    filename = image_src.split('/')[-1]
                                    # 构建最终路径
                                    final_path = os.path.join(final_dir, filename)
                                    final_path = final_path.replace('\\', '/')
                                    
                                    if existing_record:
                                        cursor.execute("""
                                            UPDATE material_sample_questions 
                                            SET image_path = %s, question_text = %s
                                            WHERE form_id = %s AND question_number = %s
                                        """, (final_path, question_text, form_id, question_number))
                                    else:
                                        # 插入新记录，包括文本
                                        cursor.execute("""
                                            INSERT INTO material_sample_questions 
                                            (form_id, question_number, question_text, image_path)
                                            VALUES (%s, %s, %s, %s)
                                        """, (form_id, question_number, question_text, final_path))
                                    
                                    print(f"已使用URL中的文件名更新问题 {question_number} 的图片路径为: {final_path}")
                            except Exception as img_err:
                                print(f"处理问题 {question_number} 的图片URL时出错: {img_err}")
                    elif has_text:
                        # 只有文本没有图片的情况
                        if existing_record:
                            cursor.execute("""
                                UPDATE material_sample_questions 
                                SET question_text = %s, image_path = NULL
                                WHERE form_id = %s AND question_number = %s
                            """, (question_text, form_id, question_number))
                        else:
                            cursor.execute("""
                                INSERT INTO material_sample_questions 
                                (form_id, question_number, question_text, image_path)
                                VALUES (%s, %s, %s, NULL)
                            """, (form_id, question_number, question_text))
                        
                        print(f"已更新问题 {question_number} 的文本，无图片")
                    else:
                        print(f"警告: 问题 {question_number} 没有图片或文本数据")
            
            # 最后，检查并删除不再需要的问题点记录
            cursor.execute("""
                SELECT question_number FROM material_sample_questions 
                WHERE form_id = %s
            """, (form_id,))
            
            existing_questions = [row[0] for row in cursor.fetchall()]
            # 更全面地检查有效的问题点编号
            valid_question_numbers = []
            for i, data in enumerate(question_data):
                if data:
                    question_number = i+1
                    question_text = data.get('text', '').strip()
                    image_src = data.get('imageSrc')
                    has_image = bool(image_src) and len(str(image_src)) > 0
                    has_text = bool(question_text) or data.get('hasText', False)
                    
                    if has_image or has_text:
                        valid_question_numbers.append(question_number)
                        print(f"有效问题点 #{question_number}: 有图片={has_image}, 有文本={has_text}")
            
            print(f"有效问题点列表: {valid_question_numbers}")
            
            # 获取临时图片存储路径
            temp_uploads_dir = os.path.join(get_image_base_path(), 'images', 'MSCF_modify_temp_uploads', report_code)
            
            # 检查并删除不再需要的问题点图片和记录
            for q_num in existing_questions:
                if q_num not in valid_question_numbers:
                    # 首先删除临时目录中该问题点的图片
                    if os.path.exists(temp_uploads_dir):
                        deleted_files = []
                        for filename in os.listdir(temp_uploads_dir):
                            try:
                                # 尝试从文件名中提取问题编号
                                parts = filename.split('_')
                                if parts and int(parts[0]) == int(q_num):
                                    file_to_delete = os.path.join(temp_uploads_dir, filename)
                                    os.remove(file_to_delete)
                                    deleted_files.append(filename)
                                    print(f"删除不再需要的问题点 #{q_num} 的临时图片: {file_to_delete}")
                            except Exception:
                                # 使用更宽松的文件名匹配逻辑
                                str_question_number = str(q_num)
                                if (f"question_{str_question_number}" in filename or 
                                    f"_{str_question_number}." in filename or
                                    f"_{str_question_number}_" in filename or
                                    filename.startswith(f"{str_question_number}_")):
                                    file_to_delete = os.path.join(temp_uploads_dir, filename)
                                    try:
                                        os.remove(file_to_delete)
                                        deleted_files.append(filename)
                                        print(f"删除不再需要的问题点 #{q_num} 的临时图片: {file_to_delete}")
                                    except Exception as e:
                                        print(f"删除临时图片失败: {file_to_delete}, 错误: {e}")
                        
                        if deleted_files:
                            print(f"已删除问题点 #{q_num} 的 {len(deleted_files)} 个临时图片文件")
                    
                    # 然后删除数据库记录
                    cursor.execute("""
                        DELETE FROM material_sample_questions 
                        WHERE form_id = %s AND question_number = %s
                    """, (form_id, q_num))
                    print(f"已删除不再需要的问题点记录: {q_num}")
        else:
            # 处理移动的图片
            for image_info in moved_images:
                question_number = image_info.get('question_number')
                if question_number is not None:
                    # 检查是否需要根据映射关系调整问题编号
                    adjusted_number = question_number
                    
                    # 使用原始编号信息进行调整
                    if str(question_number) in question_mapping:
                        adjusted_number = int(question_mapping[str(question_number)])
                        print(f"根据映射关系调整问题编号: {question_number} -> {adjusted_number}")
                        
                        # 重命名图片文件以匹配新的问题编号
                        original_path = image_info['dest_path']
                        original_filename = image_info['filename']
                        
                        # 解析文件名部分
                        name_parts = original_filename.split('_')
                        extension = original_filename.split('.')[-1]
                        
                        # 创建新文件名，替换问题编号部分
                        if len(name_parts) >= 2:
                            # 替换第一部分为新的问题编号
                            name_parts[0] = str(adjusted_number)
                            new_filename = '_'.join(name_parts)
                        else:
                            # 如果文件名格式不符合预期，创建一个新的文件名
                            timestamp = datetime.datetime.now().strftime('%y%m%d_%H%M%S_%f')[:16]  # 取到毫秒的前3位
                            random_suffix = random.randint(1000, 9999)  # 生成随机4位数
                            new_filename = f"{adjusted_number}_{timestamp}_{random_suffix}.{extension}"
                        
                        # 新文件路径
                        new_path = os.path.join(final_dir, new_filename)
                        
                        # 如果新文件已存在，先删除
                        if os.path.exists(new_path) and new_path != original_path:
                            try:
                                os.remove(new_path)
                                print(f"已删除已存在的目标文件: {new_path}")
                            except Exception as e:
                                print(f"删除已存在的目标文件失败: {new_path}, 错误: {e}")
                        
                        # 重命名文件
                        try:
                            if original_path != new_path:
                                shutil.copy2(original_path, new_path)
                                os.remove(original_path)
                                print(f"已重命名图片: {original_path} -> {new_path}")
                                
                                # 更新image_info中的路径信息
                                image_info['dest_path'] = new_path
                                image_info['filename'] = new_filename
                            
                            # 更新数据库中的图片路径为最终路径
                            final_path = new_path
                        except Exception as e:
                            print(f"重命名图片失败: {original_path} -> {new_path}, 错误: {e}")
                            final_path = original_path
                    elif question_number in original_numbers:
                        original_number = original_numbers[question_number]
                        print(f"问题点 {question_number} 的原始编号是 {original_number}")
                        if original_number in question_mapping:
                            adjusted_number = int(question_mapping[original_number])
                            print(f"根据原始编号映射调整问题编号: {question_number}(原始:{original_number}) -> {adjusted_number}")
                            
                            # 重命名图片文件以匹配新的问题编号
                            original_path = image_info['dest_path']
                            original_filename = image_info['filename']
                            
                            # 解析文件名部分
                            name_parts = original_filename.split('_')
                            extension = original_filename.split('.')[-1]
                            
                            # 创建新文件名，替换问题编号部分
                            if len(name_parts) >= 2:
                                # 替换第一部分为新的问题编号
                                name_parts[0] = str(adjusted_number)
                                new_filename = '_'.join(name_parts)
                            else:
                                # 如果文件名格式不符合预期，创建一个新的文件名
                                timestamp = datetime.datetime.now().strftime('%y%m%d_%H%M%S_%f')[:16]  # 取到毫秒的前3位
                                random_suffix = random.randint(1000, 9999)  # 生成随机4位数
                                new_filename = f"{adjusted_number}_{timestamp}_{random_suffix}.{extension}"
                            
                            # 新文件路径
                            new_path = os.path.join(final_dir, new_filename)
                            
                            # 如果新文件已存在，先删除
                            if os.path.exists(new_path) and new_path != original_path:
                                try:
                                    os.remove(new_path)
                                    print(f"已删除已存在的目标文件: {new_path}")
                                except Exception as e:
                                    print(f"删除已存在的目标文件失败: {new_path}, 错误: {e}")
                            
                            # 重命名文件
                            try:
                                if original_path != new_path:
                                    shutil.copy2(original_path, new_path)
                                    os.remove(original_path)
                                    print(f"已重命名图片: {original_path} -> {new_path}")
                                    
                                    # 更新image_info中的路径信息
                                    image_info['dest_path'] = new_path
                                    image_info['filename'] = new_filename
                                
                                # 更新数据库中的图片路径为最终路径
                                final_path = new_path
                            except Exception as e:
                                print(f"重命名图片失败: {original_path} -> {new_path}, 错误: {e}")
                                final_path = original_path
                        else:
                            final_path = image_info['dest_path']
                    else:
                        final_path = image_info['dest_path']
                    
                    # 更新数据库中的图片路径为最终路径
                    try:
                        cursor.execute("""
                            UPDATE material_sample_questions 
                            SET image_path = %s 
                            WHERE form_id = %s AND question_number = %s
                        """, (final_path, form_id, adjusted_number))
                        
                        print(f"已更新问题 {adjusted_number} (原始: {question_number}) 的图片路径为: {final_path}")
                    except Exception as e:
                        print(f"更新问题 {adjusted_number} 的图片路径失败: {e}")
            
            # 收集所有问题点的文本数据，确保不会被错误覆盖
            question_texts = {}
            # 记录所有标准问题点，包括空问题点，以确保问题点一对一映射
            standard_questions = {}
            for key in request.form:
                if key.startswith('question_') and key.endswith('_text'):
                    try:
                        question_number = int(key.split('_')[1])
                        question_text = request.form[key].strip()
                        
                        # 确保所有问题点字段都记录下来，无论是否有内容
                        standard_questions[question_number] = {
                            'text': question_text,
                            'has_content': bool(question_text)
                        }
                        
                        # 只有有内容的问题点才加入处理队列
                        if question_text:
                            # 存储问题点文本，以便后续处理
                            question_texts[question_number] = question_text
                            print(f"收集问题点 {question_number} 的文本: {question_text[:30]}")
                    except Exception as e:
                        print(f"收集问题点文本时出错: {e}, 键名: {key}")
            
            # 处理所有问题点的文本数据
            print(f"收集到 {len(question_texts)} 个问题点文本，共 {len(standard_questions)} 个标准问题点")
            
            # 第一步：处理有内容的问题点
            for question_number, question_text in question_texts.items():
                try:
                    # 检查是否存在对应的图片路径
                    image_path_key = f"question_{question_number}_image"
                    image_path = request.form.get(image_path_key, '').strip()
                    
                    # 检查是否有原始编号（用于映射关系）
                    original_number_key = f"question_{question_number}_original_number"
                    original_number = request.form.get(original_number_key, '')
                    
                    # 确定最终要使用的问题编号
                    adjusted_number = question_number
                    
                    # 如果有映射关系，使用映射后的编号
                    if question_mapping and original_number in question_mapping:
                        adjusted_number = int(question_mapping[original_number])
                        print(f"根据映射关系调整问题文本编号: {original_number} -> {adjusted_number}")
                    elif original_number and original_number.isdigit():
                        # 如果有原始编号但没有映射，尝试直接使用原始编号
                        original_num = int(original_number)
                        if 1 <= original_num <= 18:
                            adjusted_number = original_num
                            print(f"使用原始问题编号: {adjusted_number}")
                    
                    # 关键修复：如果没有图片路径，检查是否有与原始编号对应的已移动图片
                    if not image_path and original_number and original_number.isdigit():
                        orig_num_int = int(original_number)
                        # 检查是否有对应的已移动图片
                        for img_info in moved_images:
                            if img_info.get('original_number') == orig_num_int:
                                # 找到了与原始编号对应的已移动图片
                                image_path = img_info.get('dest_path', '')
                                if image_path:
                                    print(f"找到与问题点 {question_number} (原始编号: {original_number}) 对应的已移动图片: {image_path}")
                                    break
                    
                    # 只有文本没有图片的情况，确保数据库记录正确
                    cursor.execute("""
                        SELECT id, question_text FROM material_sample_questions 
                        WHERE form_id = %s AND question_number = %s
                    """, (form_id, adjusted_number))
                    
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # 检查是否需要更新文本
                        existing_text = existing_record[1] if existing_record[1] else ""
                        if existing_text != question_text:
                            cursor.execute("""
                                UPDATE material_sample_questions 
                                SET question_text = %s
                                WHERE form_id = %s AND question_number = %s
                            """, (question_text, form_id, adjusted_number))
                            
                            if not image_path:
                                cursor.execute("""
                                    UPDATE material_sample_questions 
                                    SET image_path = NULL
                                    WHERE form_id = %s AND question_number = %s
                                """, (form_id, adjusted_number))
                            
                            print(f"已更新问题 {adjusted_number} 的文本为: '{question_text[:30]}...'，原始问题点编号: {question_number}")
                    else:
                        # 插入新记录
                        cursor.execute("""
                            INSERT INTO material_sample_questions 
                            (form_id, question_number, question_text, image_path)
                            VALUES (%s, %s, %s, NULL)
                        """, (form_id, adjusted_number, question_text))
                        
                        print(f"已创建问题 {adjusted_number} 的文本记录: '{question_text[:30]}...'，原始问题点编号: {question_number}")
                
                except Exception as e:
                    print(f"处理问题点 {question_number} 的文本时出错: {e}")
            
            # 第二步：确保空问题点在数据库中也是空的
            # 删除那些在前端为空但可能在数据库中有值的问题点记录
            for question_number, question_info in standard_questions.items():
                if not question_info['has_content']:  # 如果问题点在前端是空的
                    # 检查对应的图片路径是否也为空
                    image_path_key = f"question_{question_number}_image"
                    image_path = request.form.get(image_path_key, '').strip()
                    
                    # 修复：检查是否是我们已经处理过的图片
                    # 查找在moved_images中是否有对应的问题点图片
                    has_moved_image = any(img_info.get('question_number') == question_number for img_info in moved_images)
                    
                    if not image_path and not has_moved_image:  # 修复：只有当没有图片且不在已处理图片列表中时才删除
                        # 检查数据库中是否已有该问题点的记录
                        cursor.execute("""
                            SELECT id, image_path FROM material_sample_questions 
                            WHERE form_id = %s AND question_number = %s
                        """, (form_id, question_number))
                        
                        existing_record = cursor.fetchone()
                        if existing_record:
                            # 再次确认该记录没有图片路径，才删除记录
                            if not existing_record[1]:  # 如果数据库中的记录也没有图片路径
                                cursor.execute("""
                                    DELETE FROM material_sample_questions 
                                    WHERE form_id = %s AND question_number = %s
                                """, (form_id, question_number))
                                print(f"删除空问题点在数据库中的记录: 问题点 #{question_number}")
                            else:
                                print(f"保留问题点 #{question_number}，尽管文本为空，但数据库中有图片路径")
                    else:
                        print(f"保留问题点 #{question_number}，尽管文本为空，但有图片路径或已处理过图片")
                        
                        # 确保文本字段为空
                        cursor.execute("""
                            UPDATE material_sample_questions 
                            SET question_text = ''
                            WHERE form_id = %s AND question_number = %s
                        """, (form_id, question_number))
        
        # 检查是否有问题点缺少图片路径
        cursor.execute("""
            SELECT question_number, question_text 
            FROM material_sample_questions 
            WHERE form_id = %s AND (image_path IS NULL OR image_path = '')
            AND question_number <= 18
        """, (form_id,))
        
        missing_images = cursor.fetchall()
        if missing_images:
            print(f"警告: 有 {len(missing_images)} 个问题点缺少图片路径:")
            for row in missing_images:
                print(f"  问题 #{row[0]}: {row[1][:30]}...")
        
        conn.commit()
        
        # 移除临时目录
        try:
            if os.path.exists(temp_uploads_dir) and not os.listdir(temp_uploads_dir):
                os.rmdir(temp_uploads_dir)
                print(f"已移除空的临时目录: {temp_uploads_dir}")
        except Exception as e:
            print(f"移除临时目录失败: {e}")
            
        return jsonify({
            "success": True,
            "message": "表单更新成功",
            "report_code": report_code
        })
        
    except Exception as e:
        # 发生错误时回滚事务
        if 'conn' in locals() and conn:
            conn.rollback()
        
        import traceback
        error_details = traceback.format_exc()
        print(f"表单更新时发生错误: {e}")
        print(error_details)
        
        # 返回详细的错误信息
        return jsonify({
            "success": False,
            "message": f"表单更新失败: {str(e)}",
            "details": error_details.split('\n')
        }), 500
        
    finally:
        # 关闭数据库连接
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

# 在Material_Sample_Confirmation_Form_modify_bp蓝图中添加调试路由
@Material_Sample_Confirmation_Form_modify_bp.route('/debug_images/<report_code>')
def debug_images(report_code):
    """调试路由，用于显示指定报告的所有图片信息"""
    try:
        base_image_path = get_image_base_path()
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads')
        
        result = {
            "base_path": base_image_path,
            "temp_uploads_dir": temp_uploads_dir,
            "report_code": report_code,
            "folders": [],
            "all_temp_folders": [],
            "image_files": {}
        }
        
        # 显示临时目录是否存在
        result["temp_dir_exists"] = os.path.exists(temp_uploads_dir)
        
        # 列出临时目录中的所有文件夹
        if result["temp_dir_exists"]:
            try:
                result["all_temp_folders"] = os.listdir(temp_uploads_dir)
            except Exception as e:
                result["all_temp_folders_error"] = str(e)
        
        # 查找与report_code完全匹配的文件夹
        matching_folders = []
        if result["temp_dir_exists"]:
            folder_path = os.path.join(temp_uploads_dir, report_code)
            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                matching_folders.append({
                    "folder_name": report_code,
                    "folder_path": folder_path,
                    "is_dir": True,
                    "exists": True
                })
        
        result["folders"] = matching_folders
        
        # 查找每个匹配文件夹中的图片
        for folder in matching_folders:
            folder_path = folder["folder_path"]
            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                folder["files"] = []
                for file_name in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, file_name)
                    file_info = {
                        "file_name": file_name,
                        "file_path": file_path,
                        "is_file": os.path.isfile(file_path),
                        "size_bytes": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                        "last_modified": os.path.getmtime(file_path) if os.path.exists(file_path) else 0
                    }
                    folder["files"].append(file_info)
        
        return jsonify(result)
    except Exception as e:
        import traceback
        return jsonify({
            "error": str(e),
            "traceback": traceback.format_exc()
        })

# 添加一个简单的测试路由
@Material_Sample_Confirmation_Form_modify_bp.route('/test_route')
def test_route():
    return "测试路由正常工作"

# 将路由从app移动到blueprint
@Material_Sample_Confirmation_Form_modify_bp.route('/app_delete_image', methods=['POST'])
def app_delete_image():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "无效的请求数据"}), 400
            
        report_code = data.get('report_code')
        question_number = data.get('question_number')
        
        if not report_code or not question_number:
            return jsonify({"success": False, "message": "缺少必要参数"}), 400
            
        # 获取配置中的图片存储基础路径
        base_image_path = get_image_base_path()
        
        # 获取临时文件夹路径
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads', report_code)
        
        # 获取永久文件夹路径
        permanent_dir = os.path.join(base_image_path, 'images', 'MSCF_uploads', report_code)
        
        # 连接数据库，查询图片路径
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT image_path FROM material_sample_questions 
            WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
            AND question_number = %s
        """, (report_code, question_number))
        
        result = cursor.fetchone()
        
        if result and result['image_path']:
            # 从数据库中获取的图片路径
            image_path = result['image_path']
            
            # 提取文件名
            filename = os.path.basename(image_path)
            
            # 构建临时文件路径和永久文件路径
            temp_file_path = os.path.join(temp_uploads_dir, filename)
            permanent_file_path = os.path.join(permanent_dir, filename)
            
            # 删除临时文件（如果存在）
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                print(f"已删除临时文件: {temp_file_path}")
            
            # 删除永久文件（如果存在）
            if os.path.exists(permanent_file_path):
                os.remove(permanent_file_path)
                print(f"已删除永久文件: {permanent_file_path}")
            
            # 更新数据库记录
            cursor.execute("""
                UPDATE material_sample_questions 
                SET image_path = NULL 
                WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
                AND question_number = %s
            """, (report_code, question_number))
            
            conn.commit()
            
            return jsonify({"success": True, "message": "图片已删除"})
        else:
            return jsonify({"success": False, "message": "未找到相应的图片记录"}), 404
            
    except Exception as e:
        import traceback
        print(f"删除图片时发生错误: {e}")
        print(traceback.format_exc())
        return jsonify({"success": False, "message": f"发生错误: {str(e)}"}), 500
    finally:
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

# 添加查询图片关系接口
@Material_Sample_Confirmation_Form_modify_bp.route('/query_image_relation/<report_code>')
def query_image_relation(report_code):
    """查询图片与问题的关系"""
    conn = None
    cursor = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询问题记录
        cursor.execute("""
            SELECT q.question_number, q.question_text, q.image_path
            FROM material_sample_questions q
            JOIN material_sample_confirmation_form f ON q.form_id = f.id
            WHERE f.report_code = %s
        """, (report_code,))
        
        questions = cursor.fetchall()
        
        # 查询图片对应的文件夹
        base_image_path = get_image_base_path()
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads')
        
        result = {
            "report_code": report_code,
            "questions": questions,
            "base_image_path": base_image_path,
            "temp_uploads_dir": temp_uploads_dir,
            "image_folders": []
        }
        
        # 检查临时目录是否存在
        if os.path.exists(temp_uploads_dir):
            folder_path = os.path.join(temp_uploads_dir, report_code)
            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                folder_info = {
                    "folder_name": report_code,
                    "folder_path": folder_path,
                    "files": []
                }

                # 获取文件夹中的文件
                for file_name in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, file_name)
                    if os.path.isfile(file_path):
                        folder_info["files"].append({
                            "file_name": file_name,
                            "file_path": file_path,
                            "size": os.path.getsize(file_path)
                        })
                
                result["image_folders"].append(folder_info)
        
        return jsonify(result)
    
    except Exception as e:
        import traceback
        return jsonify({
            "error": str(e),
            "traceback": traceback.format_exc()
        }), 500
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

@Material_Sample_Confirmation_Form_modify_bp.route('/upload_image', methods=['POST'])
def upload_image():
    try:
        # 验证请求是否包含文件
        if 'image' not in request.files:
            return jsonify({'success': False, 'message': '没有提供图片文件'}), 400

        # 获取参数
        question_number = request.form.get('question_number', '').strip()
        report_code = request.form.get('report_code', '').strip()
        
        if not question_number:
            return jsonify({'success': False, 'message': '缺少问题编号参数'}), 400

        # 获取上传的图片文件
        image = request.files['image']
        if image.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400

        # 验证文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if '.' not in image.filename or image.filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
            return jsonify({'success': False, 'message': '不支持的文件类型'}), 400

        # 获取图片存储的基础路径和临时目录 - 修改为新结构
        base_image_path = get_image_base_path()
        
        # 创建临时目录用于存储上传图片
        temp_uploads_dir = os.path.join(base_image_path, 'images', 'MSCF_modify_temp_uploads')
        os.makedirs(temp_uploads_dir, exist_ok=True)
        
        # 确保报告对应的临时文件夹存在
        target_folder = os.path.join(temp_uploads_dir, report_code)
        os.makedirs(target_folder, exist_ok=True)
        
        # 在上传新图片前，先检查并删除同一问题编号的旧临时图片
        str_question_number = str(question_number)
        try:
            # 只有在文件夹存在时才尝试删除旧图片
            if os.path.exists(target_folder):
                deleted_files = []
                for filename in os.listdir(target_folder):
                    # 使用更宽松的文件名匹配逻辑来找到对应问题编号的图片
                    if (f"question_{str_question_number}" in filename or 
                        f"_{str_question_number}." in filename or
                        f"_{str_question_number}_" in filename or
                        filename.startswith(f"{str_question_number}_")):
                        file_to_delete = os.path.join(target_folder, filename)
                        try:
                            os.remove(file_to_delete)
                            deleted_files.append(filename)
                            print(f"已删除旧临时图片: {file_to_delete}")
                        except Exception as del_err:
                            print(f"删除旧临时图片失败: {file_to_delete}, 错误: {del_err}")
                
                if deleted_files:
                    print(f"为问题编号 {question_number} 删除了 {len(deleted_files)} 个旧临时图片文件")
        except Exception as clean_err:
            print(f"清理旧临时图片时出错: {clean_err}")
            # 继续上传，不中断处理
        
        # 处理可能的"new-x-y"格式的问题编号，提取有效数字部分或计算实际问题编号
        cleaned_question_number = str_question_number
        if str_question_number.startswith("new-"):
            parts = str_question_number.split("-")
            if len(parts) >= 3 and parts[1].isdigit() and parts[2].isdigit():
                try:
                    # 计算实际问题编号：(行号-1)*3 + 列号 + 1
                    # 例如：new-4-1 (第4行第1列) => (4-1)*3 + 1 = 10
                    # 修正计算方法：正确的公式应该是 (row-1)*3 + col
                    row = int(parts[1])
                    col = int(parts[2])
                    # 修正的计算方法
                    calculated_number = (row - 1) * 3 + col
                    if 1 <= calculated_number <= 18:
                        # 使用计算出的问题编号
                        cleaned_question_number = str(calculated_number)
                        print(f"从问题编号 {str_question_number} 计算实际问题编号: {cleaned_question_number} (行{row}列{col})")
                except (ValueError, TypeError):
                    # 如果计算失败，则使用第三部分作为问题编号
                    if parts[2].isdigit():
                        cleaned_question_number = parts[2]
                        print(f"从问题编号 {str_question_number} 中提取第三部分作为问题编号: {cleaned_question_number}")
        
        # 使用问题编号和时间戳创建唯一文件名
        # 提取report_code中的日期部分 (YYMMDD) 从 MSC20250529005 提取 250529
        report_date_part = ""
        if report_code and len(report_code) >= 10 and report_code.startswith("MSC"):
            report_date_part = report_code[3:9]  # 从 MSC20250529005 提取 250529
        
        # 使用当前时间的时分秒毫秒作为唯一标识，并添加随机数后缀
        timestamp = datetime.datetime.now().strftime('%H%M%S_%f')[:13]  # 取到毫秒的前3位
        random_suffix = random.randint(1000, 9999)  # 生成随机4位数
        
        # 文件名格式：问题编号_报告日期部分_时间戳_毫秒_随机数.扩展名
        # 例如：5_250529_234703_176_8339.png
        file_extension = image.filename.rsplit('.', 1)[1].lower()
        
        # 使用清理后的问题编号来命名文件，确保问题编号格式正确
        # 修复：确保问题编号是纯数字，避免路径中出现反斜杠等特殊字符
        try:
            # 尝试将问题编号转换为整数，然后再转回字符串，确保格式正确
            int_question_number = int(cleaned_question_number)
            cleaned_question_number = str(int_question_number)
        except (ValueError, TypeError):
            # 如果转换失败，使用默认值或生成一个随机编号
            print(f"警告: 问题编号 {cleaned_question_number} 不是有效的数字，使用默认值")
            cleaned_question_number = str(random.randint(1, 18))
        
        new_filename = f"{cleaned_question_number}_{report_date_part}_{timestamp}_{random_suffix}.{file_extension}"
        
        # 保存图片到临时文件夹
        temp_file_path = os.path.join(target_folder, new_filename)
        image.save(temp_file_path)
        print(f"图片已保存到临时路径: {temp_file_path}")
        
        # 计算最终路径 - 优先使用原始目录
        original_dir = get_original_image_dir(report_code)
        # 确保最终目录路径不包含反斜杠，统一使用正斜杠
        original_dir = original_dir.replace('\\', '/')
        final_file_path = os.path.join(original_dir, new_filename).replace('\\', '/')
        
        # 网站访问路径使用临时文件夹的路径
        site_path = f"/Material_Sample_Confirmation_Form_modify/images/MSCF_modify_temp_uploads/{report_code}/{new_filename}"
        
        # 添加debug日志，记录新增问题点编号的处理
        print(f"DEBUG: 原始问题编号={str_question_number}, 清理后问题编号={cleaned_question_number}, 临时文件路径={temp_file_path}")
        
        return jsonify({
            'success': True,
            'message': '图片上传成功',
            'file_path': temp_file_path,      # 返回临时文件路径
            'site_path': site_path,           # 网站访问路径
            'expected_final_path': final_file_path,  # 添加一个字段表示预期的最终路径，但实际尚未移动
            'original_dir': original_dir,     # 添加原始目录信息
            'original_question_number': str_question_number,  # 添加原始问题编号
            'cleaned_question_number': cleaned_question_number  # 添加清理后的问题编号
        })
        
    except Exception as e:
        print(f"图片上传时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'上传过程中发生错误: {str(e)}'}), 500

# 在蓝图中添加根路由处理
@Material_Sample_Confirmation_Form_modify_bp.route('/root_submit', methods=['POST'])
def handle_root_submit():
    """处理根路径的提交，重定向到蓝图的update路由"""
    print("检测到对蓝图根路径/root_submit的请求")
    return update_form()  # 直接调用update_form函数处理请求

@Material_Sample_Confirmation_Form_modify_bp.route('/store_question_mapping', methods=['POST'])
def store_question_mapping():
    """临时存储问题点的文本和图片路径映射关系"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "无效的请求数据"}), 400
            
        report_code = data.get('report_code')
        question_data = data.get('question_data')
        question_mapping = data.get('question_mapping')
        detailed_mapping = data.get('detailed_mapping')
        file_name_mismatch = data.get('file_name_mismatch', [])
        enable_reordering = data.get('enable_reordering', 'yes')  # 默认启用重排序
        
        print(f"接收到问题点映射数据，重排序设置为: {enable_reordering}")
        
        if not report_code:
            return jsonify({"success": False, "message": "缺少报告编码"}), 400
        
        # 强制读取配置中的重排序设置，确保与settings.json一致
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    enable_reordering = config.get('enable_image_reordering', 'yes')
                    print(f"从配置中获取图片重排序设置: {enable_reordering}")
        except Exception as e:
            print(f"读取图片重排序设置时出错: {e}，使用默认值: yes")
            enable_reordering = 'yes'  # 默认启用重排序
        
        # 处理question_data中可能的new-x-y格式问题编号
        if question_data:
            # 记录所有问题点及其实际编号
            processed_numbers = {}
            
            for i, data_item in enumerate(question_data):
                if data_item and 'originalNumber' in data_item:
                    original_number = data_item['originalNumber']
                    
                    # 添加当前位置信息
                    data_item['currentPosition'] = i + 1
                    
                    # 检查图片URL是否存在
                    image_src = data_item.get('imageSrc', '')
                    if image_src:
                        # 确保hasImage标志被正确设置
                        data_item['hasImage'] = True
                        print(f"问题点 {i+1} (原始编号: {original_number}) 有图片URL: {image_src[:30]}...")
                    
                    # 处理new-x-y格式
                    if original_number and original_number.startswith('new-'):
                        # 增强处理new-x-y格式编号的逻辑
                        parts = original_number.split('-')
                        if len(parts) >= 3 and parts[1].isdigit() and parts[2].isdigit():
                            # 计算实际问题编号：(行号-1)*3 + 列号
                            row = int(parts[1])
                            col = int(parts[2])
                            calculated_number = (row - 1) * 3 + col
                            if 1 <= calculated_number <= 18:
                                cleaned_number = str(calculated_number)
                            data_item['originalCleanedNumber'] = cleaned_number
                            data_item['calculatedNumber'] = calculated_number
                                
                                # 记录处理后的编号
                            processed_numbers[original_number] = cleaned_number
                                
                            print(f"处理new-x-y格式问题编号: {original_number} -> {cleaned_number} (行{row}列{col})")
                                
                                # 确保问题映射关系中有这个编号
                            if question_mapping and original_number not in question_mapping:
                                    question_mapping[original_number] = cleaned_number
                                    print(f"添加缺失的问题映射关系: {original_number} -> {cleaned_number}")
                        elif len(parts) >= 3 and parts[2].isdigit():
                            # 格式不完整，但至少有第三部分
                            cleaned_number = parts[2]
                            data_item['originalCleanedNumber'] = cleaned_number
                            
                            # 记录处理后的编号
                            processed_numbers[original_number] = cleaned_number
                            
                            print(f"格式不完整，直接提取问题编号: {original_number} -> {cleaned_number}")
        
        # 检查映射关系，确保不会将多个图片映射到同一个问题点
        if question_mapping:
            # 更新映射关系，确保new-x-y格式的编号被正确映射
            for orig_num, processed_num in processed_numbers.items():
                if orig_num not in question_mapping:
                    question_mapping[orig_num] = processed_num
                    print(f"添加映射关系: {orig_num} -> {processed_num}")
        
            # 创建一个反向映射，检查是否有多个原始编号映射到同一个新编号
            reverse_mapping = {}
            duplicates = []
            
            for original_num, new_num in question_mapping.items():
                if new_num in reverse_mapping:
                    # 发现重复映射
                    duplicates.append({
                        'new_number': new_num,
                        'original_numbers': [reverse_mapping[new_num], original_num]
                    })
                    print(f"警告: 发现重复映射 - 原始编号 {reverse_mapping[new_num]} 和 {original_num} 都映射到了 {new_num}")
                else:
                    reverse_mapping[new_num] = original_num
            
            # 如果发现重复映射，修复映射关系
            if duplicates:
                print(f"发现 {len(duplicates)} 个重复映射，尝试修复")
                
                # 查找可用的问题点编号
                used_numbers = set(question_mapping.values())
                available_numbers = [str(i) for i in range(1, 19) if str(i) not in used_numbers]
                
                # 修复重复映射
                for duplicate in duplicates:
                    new_num = duplicate['new_number']
                    original_nums = duplicate['original_numbers']
                    
                    # 保留第一个映射，修改其他映射
                    for i in range(1, len(original_nums)):
                        if available_numbers:
                            # 分配一个新的可用编号
                            new_available = available_numbers.pop(0)
                            question_mapping[original_nums[i]] = new_available
                            print(f"修复重复映射: 将原始编号 {original_nums[i]} 重新映射到 {new_available}")
                        else:
                            # 如果没有可用编号，删除这个映射
                            del question_mapping[original_nums[i]]
                            print(f"修复重复映射: 删除原始编号 {original_nums[i]} 的映射，因为没有可用编号")
            
        # 强制启用重排序功能，以确保新增问题点的图片能被正确处理
        enable_reordering = 'yes'
        print(f"设置重排序模式为: {enable_reordering}")
            
        # 只有当启用重排序设置为'yes'时才处理文件名不匹配的情况
        renamed_files = []
        if enable_reordering.lower() == 'yes':
            print(f"重排序模式：开始检查所有问题点的图片映射关系")
            
            # 创建一个问题点编号到数据项的映射
            question_data_map = {}
            for i, data in enumerate(question_data):
                if data:
                    # 连续的新编号（从1开始）
                    new_number = i + 1
                    question_data_map[str(new_number)] = data
                    
                    # 记录新的位置信息
                    data['newPosition'] = new_number
                    
                    # 确保originalNumber存在
                    if 'originalNumber' not in data:
                        data['originalNumber'] = str(new_number)
            
            # 首先收集所有需要重命名的图片信息
            need_rename_list = []
            
            # 从问题点数据中收集需要重命名的图片信息
            if question_data:
                for i, data in enumerate(question_data):
                    if data and data.get('hasImage') and data.get('imageName'):
                        original_number = data.get('originalNumber')
                        # 连续的新编号（从1开始）
                        new_number = i + 1
                        image_name = data.get('imageName')
                        
                        # 从图片文件名中提取编号
                        file_number = ''
                        if image_name:
                            name_parts = image_name.split('_')
                            if name_parts and len(name_parts) > 0:
                                file_number = name_parts[0]
                        
                        # 如果文件编号与新编号不一致，需要重命名
                        if file_number and file_number != str(new_number):
                            print(f"发现需要重命名的图片: 原始编号={original_number}, 新编号={new_number}, 文件编号={file_number}, 文件名={image_name}")
                            need_rename_list.append({
                                'originalNumber': original_number,
                                'newNumber': new_number,
                                'fileNumber': file_number,
                                'imageName': image_name,
                                'originalPosition': int(file_number) if file_number.isdigit() else 0  # 记录原始位置
                            })
                        else:
                            print(f"图片文件名与问题点编号一致，无需重命名: {image_name}")
            
            # 如果需要重命名的图片列表为空，但启用了重排序，可能是因为图片名称已经与原始位置不一致
            # 此时，我们需要从文件名中提取位置信息，并与问题点数据进行映射
            if file_name_mismatch:
                for mismatch in file_name_mismatch:
                    file_number = mismatch.get('fileNumber')
                    question_number = mismatch.get('questionNumber')
                    image_name = mismatch.get('imageName')
                    
                    if file_number and question_number and image_name:
                        need_rename_list.append({
                            'originalNumber': file_number,
                            'newNumber': question_number,
                            'fileNumber': file_number,
                            'imageName': image_name,
                            'originalPosition': int(file_number) if file_number.isdigit() else 0
                        })
                        print(f"从mismatch数据中添加重命名任务: 原始编号={file_number}, 新编号={question_number}, 文件名={image_name}")
            
            # 只有当有需要重命名的图片时才执行重命名操作
            if need_rename_list:
                print(f"需要重命名的图片数量: {len(need_rename_list)}")
                
                # 对需要重命名的图片按照原始位置排序
                need_rename_list.sort(key=lambda x: x.get('originalPosition', 0))
                
                # 重命名临时文件夹中的图片
                # 获取临时图片路径
                temp_dir = os.path.join(get_image_base_path(), "images", "MSCF_modify_temp_uploads", report_code)
                
                # 确保临时目录存在
                os.makedirs(temp_dir, exist_ok=True)
                
                # 执行重命名操作
                for rename_info in need_rename_list:
                    original_number = rename_info.get('originalNumber')
                    new_number = rename_info.get('newNumber')
                    image_name = rename_info.get('imageName')
                    
                    if not original_number or not new_number or not image_name:
                        print(f"重命名信息不完整，跳过: {rename_info}")
                        continue
                    
                    try:
                        # 构建原始文件路径和新文件路径
                        # 从原始文件名中提取时间戳部分
                        name_parts = image_name.split('_')
                        timestamp = "_".join(name_parts[1:]) if len(name_parts) > 1 else ""
                        
                        # 如果没有时间戳，生成一个新的
                        if not timestamp:
                            timestamp = datetime.datetime.now().strftime("%y%m%d_%H%M%S")
                        
                        # 获取文件扩展名
                        _, ext = os.path.splitext(image_name)
                        if not ext:
                            ext = ".png"  # 默认扩展名
                        
                        # 构建新的文件名
                        new_filename = f"{new_number}_{timestamp}{ext}"
                        
                        # 构建完整的文件路径
                        old_filepath = os.path.join(temp_dir, image_name)
                        new_filepath = os.path.join(temp_dir, new_filename)
                        
                        print(f"准备重命名临时文件: {old_filepath} -> {new_filepath}")
                        
                        # 检查源文件是否存在
                        if not os.path.exists(old_filepath):
                            print(f"源文件不存在，跳过重命名: {old_filepath}")
                            continue
                            
                        # 检查目标文件是否已存在，如果存在则先删除
                        if os.path.exists(new_filepath):
                            os.remove(new_filepath)
                            print(f"目标文件已存在，已删除: {new_filepath}")
                        
                        # 重命名文件
                        shutil.copy2(old_filepath, new_filepath)
                        print(f"已复制并重命名临时文件: {old_filepath} -> {new_filepath}")
                        
                        # 记录重命名信息
                        renamed_files.append({
                            'originalPath': old_filepath,
                            'newPath': new_filepath,
                            'originalName': image_name,
                            'newName': new_filename,
                            'originalNumber': original_number,
                            'newNumber': new_number
                        })
                        
                        # 更新问题点数据中的图片路径
                        for i, q_data in enumerate(question_data):
                            if q_data and i + 1 == int(new_number):
                                # 更新图片URL
                                old_src = q_data.get('imageSrc', '')
                                if old_src:
                                    new_src = old_src.replace(image_name, new_filename)
                                    q_data['imageSrc'] = new_src
                                    q_data['imageName'] = new_filename
                                    print(f"已更新问题点 {new_number} 的图片路径: {new_src}")
                                    
                                    # 记录图片映射关系，确保问题点和图片正确关联
                                    q_data['imageMapping'] = {
                                        'original_number': original_number,
                                        'new_number': new_number,
                                        'original_filename': image_name,
                                        'new_filename': new_filename
                                    }
                    except Exception as e:
                        print(f"重命名图片时出错: {e}")
                    else:
                        print("重排序未启用，跳过图片重命名")
        
        # 保存数据到临时存储
        temp_question_data_store[report_code] = {
            'question_data': question_data,
            'question_mapping': question_mapping,
            'enable_reordering': enable_reordering,
            'renamed_files': renamed_files,
            'timestamp': datetime.datetime.now().isoformat()
        }
        
        return jsonify({
            "success": True, 
            "message": "已保存问题点映射关系",
            "question_data": question_data,
            "question_mapping": question_mapping,
            "renamed_files": renamed_files
        })
        
    except Exception as e:
        import traceback
        print(f"保存问题点映射关系时发生错误: {e}")
        print(traceback.format_exc())
        return jsonify({"success": False, "message": f"发生错误: {str(e)}"}), 500

@Material_Sample_Confirmation_Form_modify_bp.route('/get_question_mapping/<report_code>', methods=['GET'])
def get_question_mapping(report_code):
    """获取临时存储的问题点映射关系"""
    try:
        if not report_code:
            return jsonify({"success": False, "message": "缺少报告编码"}), 400
            
        # 从临时存储中获取数据
        stored_data = temp_question_data_store.get(report_code)
        
        if not stored_data:
            # 即使没有找到存储的数据，也返回配置文件中的重排序设置
            enable_image_reordering = 'no'
            try:
                config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        enable_image_reordering = config.get('enable_image_reordering', 'no')
                        print(f"从配置中获取图片重排序设置: {enable_image_reordering}")
            except Exception as e:
                print(f"读取图片重排序设置时出错: {e}")
            
            # 返回默认值，但仍然标记为成功，这样前端可以使用配置文件中的设置
            return jsonify({
                "success": True,
                "message": "未找到该报告的问题点映射数据，但返回了默认重排序设置",
                "question_data": [],
                "question_mapping": {},
                "enable_reordering": enable_image_reordering,
                "timestamp": datetime.datetime.now().isoformat()
            })
            
        return jsonify({
            "success": True,
            "question_data": stored_data.get('question_data'),
            "question_mapping": stored_data.get('question_mapping'),
            "enable_reordering": stored_data.get('enable_reordering', 'no'),
            "renamed_files": stored_data.get('renamed_files', []),
            "timestamp": stored_data.get('timestamp')
        })
    except Exception as e:
        import traceback
        print(f"获取问题点映射关系时发生错误: {e}")
        print(traceback.format_exc())
        return jsonify({"success": False, "message": f"发生错误: {str(e)}"}), 500

@Material_Sample_Confirmation_Form_modify_bp.route('/prepare_form_submit', methods=['POST'])
def prepare_form_submit():
    """准备表单提交，确保问题点映射关系被正确传递"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "无效的请求数据"}), 400
            
        report_code = data.get('report_code')
        question_mapping = data.get('question_mapping', {})
        
        if not report_code:
            return jsonify({"success": False, "message": "缺少报告编码"}), 400
        
        # 获取重排序设置
        enable_image_reordering = 'yes'  # 默认启用重排序
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'settings.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    enable_image_reordering = config.get('enable_image_reordering', 'yes')
                    print(f"从配置中获取图片重排序设置: {enable_image_reordering}")
        except Exception as e:
            print(f"读取图片重排序设置时出错: {e}")
        
        # 从临时存储中获取数据
        stored_data = temp_question_data_store.get(report_code, {})
        stored_mapping = stored_data.get('question_mapping', {})
        stored_question_data = stored_data.get('question_data', [])
        
        print(f"临时存储中的问题点数据: {len(stored_question_data)} 条记录")
        print(f"临时存储中的映射关系: {len(stored_mapping)} 条映射")
        
        # 如果临时存储中有new-x-y格式的问题点，确保它们被正确映射
        processed_new_format = {}
        for i, data in enumerate(stored_question_data):
            if data and 'originalNumber' in data and data['originalNumber'].startswith('new-'):
                original_number = data['originalNumber']
                # 解析new-x-y格式
                parts = original_number.split('-')
                if len(parts) >= 3 and parts[1].isdigit() and parts[2].isdigit():
                    row = int(parts[1])
                    col = int(parts[2])
                    calculated_number = (row - 1) * 3 + col
                    if 1 <= calculated_number <= 18:
                        # 记录处理后的问题点编号
                        processed_new_format[original_number] = str(calculated_number)
                        print(f"处理new-x-y格式问题编号: {original_number} -> {calculated_number}")
        
        # 合并映射关系，前端传递的优先
        final_mapping = {**stored_mapping, **question_mapping}
        
        # 添加处理后的new-x-y格式问题点映射
        for original, processed in processed_new_format.items():
            if original not in final_mapping:
                final_mapping[original] = processed
                print(f"添加缺失的new-x-y格式问题点映射: {original} -> {processed}")
        
        # 如果启用了重排序，但没有映射关系，生成默认的映射关系
        if enable_image_reordering.lower() == 'yes' and not final_mapping:
            # 默认映射：保持原始编号不变
            for i in range(1, 19):
                final_mapping[str(i)] = i
                
            # 如果有new-x-y格式的编号，生成默认映射
            if stored_data and 'question_data' in stored_data:
                for i, data in enumerate(stored_data['question_data']):
                    if data and 'originalNumber' in data:
                        original_number = data['originalNumber']
                        if original_number and original_number.startswith('new-'):
                            # 默认映射：new-x-y -> 顺序编号
                            final_mapping[original_number] = i + 1
        
        # 更新临时存储中的映射关系
        if report_code in temp_question_data_store:
            temp_question_data_store[report_code]['question_mapping'] = final_mapping
            print(f"已更新临时存储中的映射关系: {len(final_mapping)} 条映射")
        else:
            # 如果临时存储中没有该报告的数据，创建一个新的
            temp_question_data_store[report_code] = {
                'question_mapping': final_mapping,
                'enable_reordering': enable_image_reordering,
                'timestamp': datetime.datetime.now().isoformat()
            }
            print(f"已创建临时存储数据: {len(final_mapping)} 条映射")
        
        # 检查并记录所有问题点的图片状态
        question_images_status = {}
        if stored_data and 'question_data' in stored_data:
            for i, data in enumerate(stored_data['question_data']):
                if data:
                    question_number = i + 1
                    image_src = data.get('imageSrc', '')
                    has_image = bool(image_src) and len(str(image_src)) > 0
                    
                    # 如果是使用new-x-y格式的问题点，记录映射关系
                    original_number = data.get('originalNumber')
                    if original_number and original_number.startswith('new-'):
                        if original_number in processed_new_format:
                            mapped_number = processed_new_format[original_number]
                            question_number = int(mapped_number)
                            print(f"使用映射后的问题点编号记录图片状态: {original_number} -> {question_number}")
                    
                    if has_image:
                        question_images_status[str(question_number)] = {
                            'has_image': True,
                            'image_src': image_src,
                            'original_number': original_number
                        }
                        print(f"问题点 {question_number} 有图片URL: {image_src[:30]}...")
        
        # 生成HTML表单代码，包含隐藏字段
        html_form = f"""
        <input type="hidden" name="question_mapping_json" value='{json.dumps(final_mapping)}'>
        <input type="hidden" name="using_reordering_mode" value='{"yes" if enable_image_reordering.lower() == "yes" else "no"}'>
        <input type="hidden" name="question_images_status" value='{json.dumps(question_images_status)}'>
        """
        
        # 添加调试输出，记录最终的映射关系
        debug_output = {
            "question_mapping": final_mapping,
            "processed_new_format": processed_new_format,
            "question_images_status": question_images_status
        }
        print(f"准备表单提交的最终数据: {json.dumps(debug_output, indent=2)}")
        
        return jsonify({
            "success": True,
            "message": "准备表单提交成功",
            "question_mapping": final_mapping,
            "enable_reordering": enable_image_reordering,
            "using_reordering_mode": "yes" if enable_image_reordering.lower() == 'yes' else "no",
            "html_form": html_form,
            "debug_data": debug_output  # 添加调试数据
        })
        
    except Exception as e:
        import traceback
        print(f"准备表单提交时发生错误: {e}")
        print(traceback.format_exc())
        return jsonify({"success": False, "message": f"发生错误: {str(e)}"}), 500