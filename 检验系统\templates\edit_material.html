<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>编辑物料</title>
    <style>
        /* 横向显示供应商列表 */
        #suppliers-container {
            display: flex;
            align-items: center;
        }
        #suppliers-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-right: 10px;
        }
        .supplier-item {
            display: flex;
            align-items: center;
            color: #0804ff;
        }
        .supplier-item button {
            margin-left: 5px;
        }
        #new-suppliers {
            margin-top: 0;
        }
        .new-supplier-input {
            display: none;
            margin-right: 5px;
            min-width: 60px; /* 设置最小宽度 */
        }
    </style>
    <script>
        function addRow() {
            var table = document.getElementById('ctq-table');
            var rowCount = table.rows.length;
            if (rowCount < 11) { // 最多支持 10 组数据（加上表头共 11 行）
                var newRow = table.insertRow(rowCount);
                var cell1 = newRow.insertCell(0);
                var cell2 = newRow.insertCell(1);
                var cell3 = newRow.insertCell(2);
                var cell4 = newRow.insertCell(3);
                var cell5 = newRow.insertCell(4);

                cell1.innerHTML = rowCount;
                cell2.innerHTML = '<input type="text" name="position[]">';
                cell3.innerHTML = '<input type="text" name="base_value[]">';
                cell4.innerHTML = '<input type="text" name="lower_tolerance[]">';
                cell5.innerHTML = '<input type="text" name="upper_tolerance[]">';
            } else {
                alert("最多支持 10 组数据");
            }
        }

        // 增加供应商的管理
        function addNewSupplierField() {
            const container = document.getElementById('new-suppliers');
            const newInput = document.createElement('input');
            newInput.type = 'text';
            newInput.name = 'new_supplier[]';
            newInput.className = 'new-supplier-input';
            newInput.style.width = '60px'; // 设置初始宽度
            newInput.addEventListener('input', adjustWidth); // 添加输入事件监听器
            container.insertBefore(newInput, container.lastElementChild);
            newInput.style.display = 'inline'; // 显示文本框
        }

        function deleteSupplier(supplier) {
            if (confirm('确认删除该供应商？')) {
                fetch('/manage_supplier', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        material_id: '{{ material[0] }}',
                        material_code: document.getElementById('material_code').value,
                        supplier: supplier,
                        action: 'delete'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 删除成功后从页面中移除对应的供应商元素
                        const supplierItem = document.querySelector(`.supplier-item input[value="${supplier}"]`).closest('.supplier-item');
                        supplierItem.remove();
                        refreshMaterialData('{{ material[0] }}'); // 在删除成功后刷新物料数据
                    } else {
                        alert('删除失败，请重试');
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        }

        // 动态调整输入框宽度
        function adjustWidth(event) {
            const input = event.target;
            const text = input.value;
            const tempSpan = document.createElement('span');
            tempSpan.style.fontFamily = window.getComputedStyle(input).fontFamily;
            tempSpan.style.fontSize = window.getComputedStyle(input).fontSize;
            tempSpan.style.visibility = 'hidden';
            tempSpan.style.whiteSpace = 'pre';
            document.body.appendChild(tempSpan);
            tempSpan.textContent = text;
            input.style.width = Math.max(60, tempSpan.offsetWidth + 10) + 'px'; // 设置最小宽度为 60px，并增加一些额外的空间
            document.body.removeChild(tempSpan);
        }

        // 隐藏所有新的供应商输入框
        function hideNewSupplierFields() {
            const inputs = document.querySelectorAll('.new-supplier-input');
            inputs.forEach(input => {
                input.style.display = 'none';
            });
        }

        // 页面加载时隐藏所有新的供应商输入框
        window.onload = hideNewSupplierFields;

        function refreshMaterialData(material_code) {
            fetch(`/get_material_data?material_code=${material_code}`, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新页面上的物料信息
                    document.getElementById('material_code').value = data.material[0];
                    document.getElementById('material_name').value = data.material[1];
                    // 更新其他字段...
                } else {
                    alert('数据刷新失败，请重试');
                }
            })
            .catch(error => console.error('Error:', error));
        }
    </script>
</head>

<body>
    <h1>编辑物料</h1>
    <form method="post" id="edit-material-form">
        <label for="material_code">物料料号:</label>
        <input type="text" id="material_code" name="material_code" value="{{ material[0] }}" readonly><br>
        <label for="material_name">物料名称:</label>
        <input type="text" id="material_name" name="material_name" value="{{ material[1] }}"><br>
        <label for="norm">规格:</label>
        <input type="text" id="norm" name="norm" value="{{ material[2] }}"><br>
        <label for="material">材质:</label>
        <input type="text" id="material" name="material" value="{{ material[3] }}" required><br>
        <label for="colour">颜色:</label>
        <input type="text" id="colour" name="colour" value="{{ material[4] }}" required><br>
        <label for="version">版本:</label>
        <input type="text" id="version" name="version" value="{{ material[5] }}"><br>
        <label for="remark">备注:</label>
        <input type="text" id="remark" name="remark" value="{{ material[6] }}"><br>
        <!-- 供应商部分修改 -->
        <div id="suppliers-container">
            <label>供应商列表:</label>
            <div id="suppliers-list">
                {% for supplier in suppliers %}
                <div class="supplier-item">
                    <input type="text" name="supplier[]" value="{{ supplier }}" style="width: 60px;" oninput="adjustWidth(event)">
                    <button type="button" onclick="deleteSupplier('{{ supplier }}')">×</button>
                </div>
                {% endfor %}
            </div>
            <div id="new-suppliers">
                <button type="button" onclick="addNewSupplierField()">+</button>
            </div>
        </div>

        <label for="entry_time">录入时间:</label>
        <input type="text" id="entry_time" name="entry_time" value="{{ material[7] }}" readonly><br>

        <h2>CTQ 尺寸测量</h2>
        <table id="ctq-table" border="1">
            <tr>
                <th>序号</th>
                <th>位置</th>
                <th>基准值</th>
                <th>下限公差</th>
                <th>上限公差</th>
            </tr>
            {% for i, ctq in ctq_measurements_with_index %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="text" name="position[]" value="{{ ctq[0] if ctq[0] else '' }}"></td>
                <td><input type="text" name="base_value[]" value="{{ ctq[1] if ctq[1] else '' }}"></td>
                <td><input type="text" name="lower_tolerance[]" value="{{ ctq[2] if ctq[2] else '' }}"></td>
                <td><input type="text" name="upper_tolerance[]" value="{{ ctq[3] if ctq[3] else '' }}"></td>
            </tr>
            {% endfor %}
        </table>
        <button type="button" onclick="addRow()">+</button>
        <input type="submit" value="提交">
    </form>
</body>
</html>